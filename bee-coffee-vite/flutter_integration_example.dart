// Flutter integration example for BeE IDE iOS app
// This file shows how to integrate the Bluetooth adapter with webview_flutter

import 'dart:async';
import 'dart:typed_data';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:flutter_reactive_ble/flutter_reactive_ble.dart';

class BeeIDEApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'BeE IDE',
      theme: ThemeData(primarySwatch: Colors.blue),
      home: BeeIDEWebView(),
    );
  }
}

class BeeIDEWebView extends StatefulWidget {
  @override
  _BeeIDEWebViewState createState() => _BeeIDEWebViewState();
}

class _BeeIDEWebViewState extends State<BeeIDEWebView> {
  InAppWebViewController? webViewController;
  BluetoothDevice? connectedDevice;
  BluetoothCharacteristic? rxCharacteristic;
  BluetoothCharacteristic? txCharacteristic;

  // BeE device UUIDs
  static const String SERVICE_UUID = "6e400001-b5a3-f393-e0a9-e50e24dcca9e";
  static const String RX_UUID = "6e400002-b5a3-f393-e0a9-e50e24dcca9e";
  static const String TX_UUID = "6e400003-b5a3-f393-e0a9-e50e24dcca9e";

  @override
  void initState() {
    super.initState();
    _initializeBluetooth();
  }

  void _initializeBluetooth() async {
    // Check if Bluetooth is supported
    if (await FlutterBluePlus.isSupported == false) {
      print("Bluetooth not supported by this device");
      return;
    }

    // Listen to Bluetooth state changes
    FlutterBluePlus.adapterState.listen((BluetoothAdapterState state) {
      print("Bluetooth adapter state: $state");
      if (state == BluetoothAdapterState.on) {
        // Bluetooth is ready
      } else if (state == BluetoothAdapterState.off) {
        // Show dialog to enable Bluetooth
        _showBluetoothOffDialog();
      }
    });
  }

  void _showBluetoothOffDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Bluetooth Required'),
          content: Text('Please enable Bluetooth to connect to BeE devices.'),
          actions: [
            TextButton(
              child: Text('OK'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('BeE IDE'), backgroundColor: Colors.orange),
      body: SafeArea(
        child: InAppWebView(
          initialUrlRequest: URLRequest(
            url: Uri.parse("http://localhost:5173"), // Your BeE IDE URL
          ),
          initialOptions: InAppWebViewGroupOptions(
            crossPlatform: InAppWebViewOptions(
              javaScriptEnabled: true,
              useShouldOverrideUrlLoading: true,
            ),
            ios: IOSInAppWebViewOptions(allowsInlineMediaPlayback: true),
          ),
          onWebViewCreated: (controller) {
            webViewController = controller;

            // Add JavaScript handler for Bluetooth operations
            controller.addJavaScriptHandler(
              handlerName: 'bluetoothHandler',
              callback: (args) async {
                if (args.isNotEmpty) {
                  return await _handleBluetoothMessage(args[0]);
                }
                return {'success': false, 'error': 'No arguments provided'};
              },
            );
          },
          onLoadStop: (controller, url) async {
            // Inject JavaScript functions for communication
            await controller.evaluateJavascript(
              source: """
              window.handleBluetoothResponse = function(response) {
                if (window._bluetoothCallbacks && window._bluetoothCallbacks[response.id]) {
                  const callback = window._bluetoothCallbacks[response.id];
                  delete window._bluetoothCallbacks[response.id];
                  
                  if (response.success) {
                    callback.resolve(response.data);
                  } else {
                    callback.reject(new Error(response.error));
                  }
                }
              };
              
              window.handleBluetoothEvent = function(event) {
                console.log('Bluetooth event:', event);
                // Handle Bluetooth events from Flutter
              };
            """,
            );
          },
          onConsoleMessage: (controller, consoleMessage) {
            print("Console: ${consoleMessage.message}");
          },
        ),
      ),
    );
  }

  Future<Map<String, dynamic>> _handleBluetoothMessage(
    Map<String, dynamic> message,
  ) async {
    try {
      String method = message['method'] ?? '';
      Map<String, dynamic> params = Map<String, dynamic>.from(
        message['params'] ?? {},
      );
      dynamic id = message['id'];

      print("Handling Bluetooth method: $method");

      Map<String, dynamic> result;

      switch (method) {
        case 'bluetooth_connect':
          result = await _connectToDevice();
          break;

        case 'bluetooth_disconnect':
          result = await _disconnectDevice();
          break;

        case 'bluetooth_write':
          List<int> data = List<int>.from(params['data'] ?? []);
          result = await _writeData(data);
          break;

        case 'bluetooth_write_chunked':
          List<int> data = List<int>.from(params['data'] ?? []);
          int chunkSize = params['chunkSize'] ?? 20;
          int delay = params['delay'] ?? 2;
          result = await _writeDataChunked(data, chunkSize, delay);
          break;

        default:
          result = {'success': false, 'error': 'Unknown method: $method'};
      }

      result['id'] = id;

      // Send response back to WebView
      await _sendResponseToWebView(result);

      return result;
    } catch (e) {
      print("Error handling Bluetooth message: $e");
      Map<String, dynamic> errorResult = {
        'id': message['id'],
        'success': false,
        'error': e.toString(),
      };

      await _sendResponseToWebView(errorResult);
      return errorResult;
    }
  }

  Future<void> _sendResponseToWebView(Map<String, dynamic> response) async {
    if (webViewController != null) {
      String responseJson = jsonEncode(response);
      await webViewController!.evaluateJavascript(
        source:
            """
        if (window.handleBluetoothResponse) {
          window.handleBluetoothResponse($responseJson);
        }
      """,
      );
    }
  }

  Future<Map<String, dynamic>> _connectToDevice() async {
    try {
      // Check Bluetooth state
      var adapterState = await FlutterBluePlus.adapterState.first;
      if (adapterState != BluetoothAdapterState.on) {
        throw Exception("Bluetooth is not enabled");
      }

      // Start scanning for BeE devices
      print("Starting scan for BeE devices...");
      await FlutterBluePlus.startScan(
        withNames: ["BeE"],
        timeout: Duration(seconds: 10),
      );

      // Wait for scan results
      BluetoothDevice? targetDevice;
      await for (ScanResult result in FlutterBluePlus.scanResults) {
        if (result.device.name.startsWith("BeE")) {
          targetDevice = result.device;
          print("Found BeE device: ${result.device.name}");
          break;
        }
      }

      await FlutterBluePlus.stopScan();

      if (targetDevice == null) {
        throw Exception(
          "No BeE device found. Make sure your device is on and in pairing mode.",
        );
      }

      // Connect to the device
      print("Connecting to ${targetDevice.name}...");
      await targetDevice.connect(timeout: Duration(seconds: 15));
      connectedDevice = targetDevice;

      // Discover services
      print("Discovering services...");
      List<BluetoothService> services = await targetDevice.discoverServices();

      BluetoothService? targetService;
      for (BluetoothService service in services) {
        if (service.uuid.toString().toLowerCase() ==
            SERVICE_UUID.toLowerCase()) {
          targetService = service;
          break;
        }
      }

      if (targetService == null) {
        throw Exception("BeE service not found on device");
      }

      // Get characteristics
      print("Setting up characteristics...");
      for (BluetoothCharacteristic characteristic
          in targetService.characteristics) {
        String charUUID = characteristic.uuid.toString().toLowerCase();

        if (charUUID == RX_UUID.toLowerCase()) {
          rxCharacteristic = characteristic;
          print("Found RX characteristic");
        } else if (charUUID == TX_UUID.toLowerCase()) {
          txCharacteristic = characteristic;
          print("Found TX characteristic");

          // Enable notifications for TX characteristic
          await characteristic.setNotifyValue(true);

          // Listen for incoming data
          characteristic.value.listen((value) {
            if (value.isNotEmpty) {
              String message = String.fromCharCodes(value);
              print("Received from device: $message");

              // Send message to WebView
              _sendEventToWebView({'type': 'message', 'data': message});
            }
          });
        }
      }

      if (rxCharacteristic == null || txCharacteristic == null) {
        throw Exception("Required characteristics not found");
      }

      // Listen for device disconnection
      targetDevice.state.listen((BluetoothDeviceState state) {
        print("Device state changed: $state");
        if (state == BluetoothDeviceState.disconnected) {
          connectedDevice = null;
          rxCharacteristic = null;
          txCharacteristic = null;

          // Notify WebView of disconnection
          _sendEventToWebView({'type': 'disconnected'});
        }
      });

      print("Successfully connected to BeE device");
      return {
        'success': true,
        'deviceName': targetDevice.name,
        'deviceId': targetDevice.id.id,
      };
    } catch (e) {
      print("Connection error: $e");
      return {'success': false, 'error': e.toString()};
    }
  }

  Future<Map<String, dynamic>> _disconnectDevice() async {
    try {
      if (connectedDevice != null) {
        await connectedDevice!.disconnect();
        print("Disconnected from device");
      }

      connectedDevice = null;
      rxCharacteristic = null;
      txCharacteristic = null;

      return {'success': true};
    } catch (e) {
      print("Disconnect error: $e");
      return {'success': false, 'error': e.toString()};
    }
  }

  Future<Map<String, dynamic>> _writeData(List<int> data) async {
    try {
      if (rxCharacteristic == null) {
        throw Exception("Not connected to device");
      }

      await rxCharacteristic!.write(data, withoutResponse: true);
      return {'success': true};
    } catch (e) {
      print("Write error: $e");
      return {'success': false, 'error': e.toString()};
    }
  }

  Future<Map<String, dynamic>> _writeDataChunked(
    List<int> data,
    int chunkSize,
    int delay,
  ) async {
    try {
      if (rxCharacteristic == null) {
        throw Exception("Not connected to device");
      }

      for (int i = 0; i < data.length; i += chunkSize) {
        int end = (i + chunkSize < data.length) ? i + chunkSize : data.length;
        List<int> chunk = data.sublist(i, end);

        await rxCharacteristic!.write(chunk, withoutResponse: true);

        if (delay > 0 && i + chunkSize < data.length) {
          await Future.delayed(Duration(milliseconds: delay));
        }
      }

      return {'success': true};
    } catch (e) {
      print("Chunked write error: $e");
      return {'success': false, 'error': e.toString()};
    }
  }

  Future<void> _sendEventToWebView(Map<String, dynamic> event) async {
    if (webViewController != null) {
      String eventJson = jsonEncode(event);
      await webViewController!.evaluateJavascript(
        source:
            """
        if (window.handleBluetoothEvent) {
          window.handleBluetoothEvent($eventJson);
        }
      """,
      );
    }
  }

  @override
  void dispose() {
    if (connectedDevice != null) {
      connectedDevice!.disconnect();
    }
    super.dispose();
  }
}
