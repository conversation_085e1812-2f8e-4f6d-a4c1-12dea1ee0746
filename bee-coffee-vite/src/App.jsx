import React from "react";
import { createTheme } from "@mui/material";
import { ThemeProvider } from "@emotion/react";
import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import axiosInstance from "./services/axiosInstance";

import ProtectedRoute from "./components/Common/ProtectedRoute";
import PublicRoute from "./components/Common/PublicRoute";
import Home from "./components/Home/Home";
import Login from "./components/Authentication/Login";
import Register from "./components/Authentication/Register";
import Profile from "./components/Authentication/Profile";
import Product from "./components/Product/Product";
import BeeShop from "./components/Product/BeeShop";
import Cart from "./components/Cart/Cart";
import CartView from "./components/Cart/CartView";
import Student from "./components/Admin/Academy/Student";
import Teacher from "./components/Admin/Academy/Teacher";
import TeacherDashboard from "./components/Academy/Teacher/Teacher";
import StudentDashboard from "./components/Academy/Student/Student";
import Class from "./components/Admin/Academy/Class";
import ClassStudent from "./components/Admin/Academy/ClassStudent";
import Lesson from "./components/Admin/Academy/Lesson";

import ProtectedAdminRoute from "./components/Admin/Common/ProtectedAdminRoute";
import BeEDashboard from "./components/Admin/Dashboard/BeEDashboard";
import CategoryAll from "./components/Admin/Category/CategoryAll";
import CategoryNew from "./components/Admin/Category/CategoryNew";
import ProductAll from "./components/Admin/Product/ProductAll";
import ProductNew from "./components/Admin/Product/ProductNew";
// import ProductViewSKU from "./components/Admin/Product/ProductViewSKU";
import ProductViewName from "./components/Admin/Product/ProductViewName";
import OrderAll from "./components/Admin/Order/OrderAll";
import OrderDetail from "./components/Admin/Order/OrderDetail";
import CustomerAll from "./components/Admin/Customer/CustomerAll";
import CustomerNew from "./components/Admin/Customer/CustomerNew";
import Promotion from "./components/Admin/Promotion/Promotion/Promotion";
import PromotionNew from "./components/Admin/Promotion/Promotion/PromotionNew";
import CouponNew from "./components/Admin/Promotion/Coupon/CouponNew";
import Coupon from "./components/Admin/Promotion/Coupon/Coupon";

import PageNotFound from "./components/404";
import Policy from "./components/Admin/Policy/Policy";
import SinglePage from "./components/Common/SinglePage";
import Carousel from "./components/Admin/Carousel/Carousel";
import POS from "./components/POS/POS";
import BarcodeScan from "./components/Admin/Product/BarcodeScan";
import Contact from "./components/Home/Contact";
import ForgotPassword from "./components/Authentication/ForgotPassword";
import ResetPassword from "./components/Authentication/ResetPassword";
import BlogAll from "./components/Admin/Blog/BlogAll";
import BlogNew from "./components/Admin/Blog/BlogNew";
import BeeBlog from "./components/Blog/BeeBlog";
import BlogPage from "./components/Blog/BlogPage";
import ProtectedPOSRoute from "./components/Common/ProtectedPOSRoute";
import Transaction from "./components/Admin/Transaction/Transaction";
import ScratchEditor from "./components/IDE/ScratchEditor";
import BeeIDE from "./components/IDE/BeeIDE/BeeIDE";
import IDE from "./components/IDE/IDE";
import BeePython from "./components/IDE/BeePython/BeePython";
import BeeJupyter from "./components/IDE/BeeJupyter/BeeJupyter";
import BeeIoT from "./components/IDE/BeeIoT/BeeIoT";
import BeeGamepadMQTT from "./components/IDE/BeeGamepadMQTT/BeeGamepadMQTT";
import BeeAI from "./components/IDE/BeeAI/BeeAI";

function App() {
    const theme = createTheme({
        typography: {
            fontFamily: `"Public Sans"`,
        },
    });

    const [user, setUser] = React.useState(null);
    const [cartItem, setCartItem] = React.useState([]);
    const [loading, setLoading] = React.useState(true); // Add loading state

    React.useEffect(() => {
        async function fetchUserData() {
            try {
                const token = localStorage.getItem("access_token") || sessionStorage.getItem("access_token");
                if (!token) {
                    setUser(null);
                    setLoading(false);
                    return;
                }

                const profileResponse = await axiosInstance.get("/api/account/profile");
                if (profileResponse.status === 200) setUser(profileResponse.data);
            } catch (err) {
                // console.error(JSON.stringify(err));
            } finally {
                setLoading(false);
            }
        }

        fetchUserData();
    }, []);

    return (
        <ThemeProvider theme={theme}>
            <Router>
                <Routes>
                    <Route path="login" element={<Login setUser={setUser} />} />
                    <Route path="register" element={<Register setUser={setUser} />} />
                    <Route element={<PublicRoute setCartItem={setCartItem} loading={loading} />}>
                        <Route
                            path="/"
                            element={
                                <Home user={user} setUser={setUser} cartItem={cartItem} setCartItem={setCartItem} />
                            }
                        />
                        <Route
                            path="/shop"
                            element={
                                <BeeShop user={user} setUser={setUser} cartItem={cartItem} setCartItem={setCartItem} />
                            }
                        />
                        <Route
                            path="/shop/:category_query"
                            element={
                                <BeeShop user={user} setUser={setUser} cartItem={cartItem} setCartItem={setCartItem} />
                            }
                        />
                        <Route
                            path="/blog"
                            element={
                                <BeeBlog user={user} setUser={setUser} cartItem={cartItem} setCartItem={setCartItem} />
                            }
                        />
                        <Route
                            path="/blog/:blog_slug"
                            element={
                                <BlogPage user={user} setUser={setUser} cartItem={cartItem} setCartItem={setCartItem} />
                            }
                        />
                        <Route
                            path="/product/item/:item_sku"
                            element={
                                <Product user={user} setUser={setUser} cartItem={cartItem} setCartItem={setCartItem} />
                            }
                        />
                        <Route
                            path="/chinh-sach/:query_slug"
                            element={
                                <SinglePage
                                    user={user}
                                    setUser={setUser}
                                    cartItem={cartItem}
                                    setCartItem={setCartItem}
                                />
                            }
                        />
                        <Route
                            path="/ho-tro/:query_slug"
                            element={<SinglePage user={user} setUser={setUser} cartItem={cartItem} />}
                        />
                        <Route
                            path="/lien-he"
                            element={
                                <Contact user={user} setUser={setUser} cartItem={cartItem} setCartItem={setCartItem} />
                            }
                        />
                        <Route path="/forgot-password" element={<ForgotPassword />} />
                        <Route path="/reset-password/:uid/:token" element={<ResetPassword />} />
                        <Route path="/play" element={<IDE />} />
                        <Route path="/play/scratch" element={<ScratchEditor />} />
                        <Route path="/play/gamepad" element={<BeeGamepadMQTT user={user} />} />
                        <Route path="/play/AI" element={<BeeAI user={user} setUser={setUser} />} />
                    </Route>
                    {/* Need authentication */}
                    <Route
                        element={
                            <ProtectedRoute
                                isAuthenticated={user != null}
                                setCartItem={setCartItem}
                                loading={loading}
                            />
                        }
                    >
                        <Route path="/play/bee-ide" element={<BeeIDE user={user} setUser={setUser} />} />
                        <Route path="/play/python" element={<BeePython user={user} setUser={setUser} />} />
                        <Route path="/play/jupyter" element={<BeeJupyter user={user} setUser={setUser} />} />
                        <Route path="/play/iot" element={<BeeIoT user={user} setUser={setUser} />} />
                        <Route path="/teacher" element={<TeacherDashboard user={user} setUser={setUser} />} />
                        <Route path="/student" element={<StudentDashboard user={user} setUser={setUser} />} />
                        <Route
                            path="/cart"
                            element={
                                <Cart user={user} setUser={setUser} cartItem={cartItem} setCartItem={setCartItem} />
                            }
                        />
                        <Route
                            path="/profile"
                            element={
                                <Profile user={user} setUser={setUser} cartItem={cartItem} setCartItem={setCartItem} />
                            }
                        />
                        <Route
                            path="/cart/:order_id"
                            element={
                                <CartView user={user} setUser={setUser} cartItem={cartItem} setCartItem={setCartItem} />
                            }
                        />
                    </Route>
                    {/* Need admin authentication */}
                    <Route element={<ProtectedAdminRoute user={user} loading={loading} />}>
                        {/* Admin */}
                        <Route path="/admin/" element={<Navigate to="/admin/dashboard" />} />
                        <Route path="/admin/dashboard" element={<BeEDashboard user={user} />} />
                        {/* Products */}
                        <Route path="/admin/product/" element={<Navigate to="/admin/product/all" />} />
                        <Route path="/admin/product/all" element={<ProductAll user={user} />} />
                        <Route path="/admin/product/edit/:product_id" element={<ProductNew user={user} />} />
                        <Route path="/admin/product/clone/:product_id" element={<ProductNew user={user} />} />
                        <Route path="/admin/product/view/:product_id" element={<ProductViewName user={user} />} />
                        <Route path="/admin/product/create" element={<ProductNew user={user} />} />
                        <Route path="/admin/product/barcode" element={<BarcodeScan user={user} />} />
                        {/* Orders */}
                        <Route path="/admin/order/all" element={<OrderAll user={user} />} />
                        <Route path="/admin/order/edit/:order_id" element={<OrderDetail user={user} />} />
                        {/* Customers */}
                        <Route path="/admin/customer/all" element={<CustomerAll user={user} />} />
                        <Route path="/admin/customer/edit/:username" element={<CustomerNew user={user} />} />
                        <Route path="/admin/customer/create" element={<CustomerNew user={user} />} />
                        {/* Blog */}
                        <Route path="/admin/blog/all" element={<BlogAll user={user} />} />
                        <Route path="/admin/blog/create" element={<BlogNew user={user} />} />
                        <Route path="/admin/blog/edit/:blog_id" element={<BlogNew user={user} />} />
                        <Route path="/admin/blog/view/:blog_id" element={<BlogNew user={user} />} />
                        {/* Transaction */}
                        <Route path="/admin/transaction" element={<Transaction user={user} />} />
                        {/* Academy */}
                        <Route path="/admin/academy/student" element={<Student user={user} />} />
                        <Route path="/admin/academy/teacher" element={<Teacher user={user} />} />
                        <Route path="/admin/academy/class" element={<Class user={user} />} />
                        <Route path="/admin/academy/class-student" element={<ClassStudent user={user} />} />
                        <Route path="/admin/academy/lesson" element={<Lesson user={user} />} />
                        {/* Categories */}
                        <Route path="/admin/setting/category/all" element={<CategoryAll user={user} />} />
                        <Route path="/admin/setting/category/edit/:category_id" element={<CategoryNew user={user} />} />
                        <Route path="/admin/setting/category/create" element={<CategoryNew user={user} />} />
                        {/* Promotions */}
                        <Route path="/admin/setting/promotion/promotion/all" element={<Promotion user={user} />} />
                        <Route
                            path="/admin/setting/promotion/promotion/create"
                            element={<PromotionNew user={user} />}
                        />
                        <Route
                            path="/admin/setting/promotion/promotion/edit/:promotion_id"
                            element={<PromotionNew user={user} />}
                        />
                        <Route path="/admin/setting/promotion/coupon/all" element={<Coupon user={user} />} />
                        <Route path="/admin/setting/promotion/coupon/create" element={<CouponNew user={user} />} />
                        <Route path="/admin/setting/promotion/coupon/edit/:code" element={<CouponNew user={user} />} />
                        {/* Policy */}
                        <Route path="/admin/setting/policy-support/" element={<Policy user={user} />} />
                        {/* Carousel */}
                        <Route path="/admin/setting/carousel/" element={<Carousel user={user} />} />
                    </Route>
                    {/* POS */}
                    <Route element={<ProtectedPOSRoute user={user} loading={loading} />}>
                        {/* POS */}
                        <Route path="/pos" element={<POS user={user} setUser={setUser} />} />
                    </Route>
                    <Route path="*" element={<PageNotFound />} />
                </Routes>
            </Router>
        </ThemeProvider>
    );
}

export default App;
