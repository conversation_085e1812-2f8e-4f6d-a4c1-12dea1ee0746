# API Services Documentation

Tà<PERSON> liệu hướng dẫn sử dụng các API services trong ứng dụng Academy.

## C<PERSON>u trúc

```
src/services/
├── index.js              # Export tất cả services và utilities
├── axiosInstance.js      # Axios configuration với interceptors
├── authService.js        # Authentication service
├── teacherAPI.js         # Teacher API endpoints
├── studentAPI.js         # Student API endpoints
└── README.md            # Tài liệu này
```

## Cách sử dụng

### 1. Import Services

```javascript
// Import individual services
import { teacherAPI, studentAPI } from '../services';

// Import hooks
import { useTeacherAPI, useStudentAPI } from '../hooks/useAPI';

// Import utilities
import { apiUtils, ERROR_MESSAGES, SUCCESS_MESSAGES } from '../services';
```

### 2. Sử dụng với Hooks (Recommended)

#### Teacher API

```javascript
import React from 'react';
import { useTeacherAPI } from '../hooks/useAPI';

function TeacherComponent() {
    const teacherAPI = useTeacherAPI();
    
    // Get courses with auto-loading
    const { data: courses, loading, error } = teacherAPI.useCourses();
    
    // Create course (manual trigger)
    const { execute: createCourse, loading: creating } = teacherAPI.useCreateCourse({
        onSuccess: (result) => {
            console.log('Course created:', result);
        },
        onError: (error) => {
            console.error('Failed to create course:', error);
        }
    });
    
    const handleCreateCourse = async () => {
        try {
            const courseData = {
                title: 'New Course',
                description: 'Course description',
                subject: 'Toán học',
                grade: 'Lớp 8',
                price: 500000,
                originalPrice: 700000
            };
            await createCourse(courseData);
        } catch (error) {
            // Error handled by onError callback
        }
    };
    
    if (loading) return <div>Loading...</div>;
    if (error) return <div>Error: {error}</div>;
    
    return (
        <div>
            <h1>My Courses ({courses?.length || 0})</h1>
            <button onClick={handleCreateCourse} disabled={creating}>
                {creating ? 'Creating...' : 'Create Course'}
            </button>
            {/* Render courses */}
        </div>
    );
}
```

#### Student API

```javascript
import React from 'react';
import { useStudentAPI } from '../hooks/useAPI';

function StudentComponent() {
    const studentAPI = useStudentAPI();
    
    // Get available courses with filters
    const { data: courses, execute: searchCourses } = studentAPI.useAvailableCourses(
        { subject: 'Toán học', price_range: 'under_500k' },
        { immediate: true }
    );
    
    // Purchase course
    const { execute: purchaseCourse, loading: purchasing } = studentAPI.usePurchaseCourse({
        onSuccess: () => {
            alert('Mua khóa học thành công!');
        }
    });
    
    const handlePurchase = async (courseId) => {
        try {
            await purchaseCourse(courseId, {
                payment_method: 'credit_card',
                card_token: 'card_token_here'
            });
        } catch (error) {
            console.error('Purchase failed:', error);
        }
    };
    
    return (
        <div>
            {courses?.map(course => (
                <div key={course.id}>
                    <h3>{course.title}</h3>
                    <p>Price: {course.price}</p>
                    <button 
                        onClick={() => handlePurchase(course.id)}
                        disabled={purchasing}
                    >
                        {purchasing ? 'Processing...' : 'Purchase'}
                    </button>
                </div>
            ))}
        </div>
    );
}
```

### 3. Sử dụng trực tiếp API (Advanced)

```javascript
import { teacherAPI, studentAPI } from '../services';

// Teacher API
const createCourse = async (courseData) => {
    try {
        const result = await teacherAPI.createCourse(courseData);
        console.log('Course created:', result);
        return result;
    } catch (error) {
        console.error('Error:', error);
        throw error;
    }
};

// Student API
const purchaseCourse = async (courseId, paymentData) => {
    try {
        const result = await studentAPI.purchaseCourse(courseId, paymentData);
        console.log('Purchase successful:', result);
        return result;
    } catch (error) {
        console.error('Purchase failed:', error);
        throw error;
    }
};
```

## API Endpoints

### Teacher API

#### Course Management
- `getCourses()` - Lấy danh sách khóa học
- `getCourseById(courseId)` - Lấy chi tiết khóa học
- `createCourse(courseData)` - Tạo khóa học mới
- `updateCourse(courseId, courseData)` - Cập nhật khóa học
- `deleteCourse(courseId)` - Xóa khóa học
- `toggleCoursePublication(courseId, isPublished)` - Xuất bản/hủy xuất bản

#### Course Content
- `getCourseContent(courseId)` - Lấy nội dung khóa học
- `createCourseContent(courseId, contentData)` - Tạo nội dung mới
- `updateCourseContent(courseId, contentId, contentData)` - Cập nhật nội dung
- `deleteCourseContent(courseId, contentId)` - Xóa nội dung

#### Student Management
- `getCourseStudents(courseId)` - Lấy danh sách học sinh
- `getStudentProgress(courseId, studentId)` - Xem tiến độ học sinh
- `assignCourseToStudents(courseId, studentIds)` - Giao khóa học

#### Analytics
- `getDashboardStats()` - Thống kê dashboard
- `getCourseAnalytics(courseId)` - Phân tích khóa học
- `exportCourseData(courseId, format)` - Xuất dữ liệu

### Student API

#### Course Discovery
- `getAvailableCourses(filters)` - Tìm khóa học có sẵn
- `getEnrolledCourses()` - Khóa học đã đăng ký
- `getCourseDetails(courseId)` - Chi tiết khóa học
- `purchaseCourse(courseId, paymentData)` - Mua khóa học

#### Learning Progress
- `getCourseContent(courseId)` - Nội dung khóa học
- `getCourseProgress(courseId)` - Tiến độ học tập
- `markLessonComplete(courseId, lessonId, data)` - Hoàn thành bài học
- `updateLessonProgress(courseId, lessonId, data)` - Cập nhật tiến độ

#### Quiz Management
- `getQuizDetails(courseId, quizId)` - Chi tiết bài kiểm tra
- `startQuizAttempt(courseId, quizId)` - Bắt đầu làm bài
- `submitQuizAnswers(courseId, quizId, attemptId, answers)` - Nộp bài
- `getQuizResults(courseId, quizId, attemptId)` - Xem kết quả

#### TurboWarp Assignments
- `getTurboWarpAssignment(courseId, assignmentId)` - Chi tiết bài tập
- `submitTurboWarpProject(courseId, assignmentId, data)` - Nộp dự án
- `getTurboWarpSubmissionStatus(courseId, assignmentId)` - Trạng thái nộp bài

## Error Handling

### Automatic Error Handling

```javascript
// Hooks tự động xử lý lỗi
const { data, error, loading } = useTeacherAPI().useCourses({
    onError: (error) => {
        // Custom error handling
        if (apiUtils.isAuthError(error)) {
            // Redirect to login
        } else if (apiUtils.isNetworkError(error)) {
            // Show network error message
        }
    }
});
```

### Manual Error Handling

```javascript
try {
    const result = await teacherAPI.createCourse(courseData);
} catch (error) {
    const errorMessage = apiUtils.formatErrorMessage(error);
    
    if (apiUtils.isAuthError(error)) {
        // Handle authentication error
        authService.logout();
    } else if (apiUtils.isNetworkError(error)) {
        // Handle network error
        showNetworkErrorDialog();
    } else {
        // Handle other errors
        showErrorMessage(errorMessage);
    }
}
```

## Caching

### Automatic Caching

```javascript
// Cache tự động với TTL
const { data } = useTeacherAPI().useCourses({
    cacheKey: 'teacher_courses',
    cacheTTL: 5 * 60 * 1000 // 5 minutes
});
```

### Manual Cache Management

```javascript
// Clear cache
localStorage.removeItem('api_cache_teacher_courses');

// Check cache
const cachedData = localStorage.getItem('api_cache_teacher_courses');
```

## File Upload

```javascript
import { useCommonAPI } from '../hooks/useAPI';

function FileUploadComponent() {
    const { uploadFile, uploadProgress } = useCommonAPI();
    
    const handleUpload = async (file) => {
        try {
            const result = await uploadFile(file, 'course_thumbnails');
            console.log('Upload successful:', result);
        } catch (error) {
            console.error('Upload failed:', error);
        }
    };
    
    return (
        <div>
            <input type="file" onChange={(e) => handleUpload(e.target.files[0])} />
            {uploadProgress > 0 && <div>Progress: {uploadProgress}%</div>}
        </div>
    );
}
```

## Environment Configuration

Cấu hình trong `.env`:

```env
# API Base URL
VITE_API_URL=http://localhost:4000

# Development mode
VITE_DEV=true
```

## Best Practices

1. **Sử dụng Hooks**: Ưu tiên sử dụng hooks thay vì gọi API trực tiếp
2. **Error Handling**: Luôn xử lý lỗi trong onError callback
3. **Loading States**: Hiển thị loading state cho UX tốt hơn
4. **Caching**: Sử dụng cache cho dữ liệu ít thay đổi
5. **Cleanup**: Cleanup subscriptions trong useEffect
6. **Type Safety**: Sử dụng TypeScript nếu có thể

## Troubleshooting

### Common Issues

1. **401 Unauthorized**: Token hết hạn → Tự động refresh hoặc logout
2. **Network Error**: Mất kết nối → Retry mechanism
3. **500 Server Error**: Lỗi server → Hiển thị thông báo lỗi
4. **Cache Issues**: Dữ liệu cũ → Clear cache manually

### Debug Mode

```javascript
// Enable debug logging
if (import.meta.env.DEV) {
    console.log('API Request:', config);
    console.log('API Response:', response);
}
```

## Migration Guide

Nếu đang sử dụng API cũ, hãy migrate theo hướng dẫn:

```javascript
// Old way
import axios from 'axios';
const response = await axios.get('/api/courses');

// New way
import { useTeacherAPI } from '../hooks/useAPI';
const { data: courses } = useTeacherAPI().useCourses();
```
