import React from "react";
import AdminLayout from "../Common/AdminLayout2";
import { DataGrid } from "@mui/x-data-grid";
import {
    Box,
    Button,
    Paper,
    Switch,
    Divider,
    Typography,
    styled,
    Checkbox,
    FormControlLabel,
    Modal,
    IconButton,
} from "@mui/material";
import { deepPurple, grey, green, red } from "@mui/material/colors";
import axiosInstance from "../../../services/axiosInstance";
import Loading from "../../Common/Loading";
import { useDocumentTitle } from "../../../hooks/useDocumentTitle";
import CustomTextField from "../../Common/CustomTextField";
import CustomButton from "../../Common/CustomButton";

import AddIcon from "@mui/icons-material/Add";
import CheckIcon from "@mui/icons-material/Check";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";

function TeacherTable({ rows, handleEdit, setRequestRefreshRows }) {
    const handleDelete = (id) => {
        if (window.confirm("Bạn có chắc chắn muốn xóa giáo viên này?")) {
            // Thêm logic xóa học viên ở đây
            axiosInstance
                .delete(`/api/academy/teacher/${id}`)
                .then(() => {
                    setRequestRefreshRows(true);
                })
                .catch((error) => {
                    console.error("Error deleting teacher:", error);
                });
        }
    };

    const columns = [
        {
            field: "id",
            headerName: "STT",
            width: 70,
        },
        {
            field: "name",
            headerName: "Tên giáo viên",
            width: 150,
        },
        {
            field: "phone",
            headerName: "Số điện thoại",
            width: 130,
        },
        {
            field: "email",
            headerName: "Email",
            width: 200,
        },
        {
            field: "major",
            headerName: "Chuyên môn",
            width: 200,
        },
        {
            field: "note",
            headerName: "Ghi chú",
            width: 200,
        },
        {
            field: "action",
            headerName: "Thao tác",
            width: 120,
            renderCell: (params) => (
                <Box sx={{ display: "flex", justifyContent: "center" }}>
                    <IconButton sx={{ color: green[600] }} size="small" onClick={() => handleEdit(params.row.id)}>
                        <EditIcon />
                    </IconButton>
                    <IconButton sx={{ color: red[600] }} size="small" onClick={() => handleDelete(params.row.id)}>
                        <DeleteIcon />
                    </IconButton>
                </Box>
            ),
        },
    ];
    const paginationModel = { page: 0, pageSize: 10 };

    return (
        <Box sx={{ display: "flex", gap: 2, flexDirection: "column", borderRadius: "20px" }}>
            <Paper sx={{ height: "400px", width: "100%", borderRadius: "20px", overflow: "hidden" }}>
                <DataGrid
                    rows={rows}
                    columns={columns}
                    initialState={{ pagination: { paginationModel } }}
                    pageSizeOptions={[10, 25, 50]}
                    checkboxSelection
                    sx={{ borderRadius: "20px" }}
                />
            </Paper>
        </Box>
    );
}

function Teacher({ user }) {
    useDocumentTitle("Quản lý giáo viên | BeE");

    const [loading, setLoading] = React.useState(true);
    const [rows, setRows] = React.useState([]);
    const [filteredRows, setFilteredRows] = React.useState([]);
    const [open, setOpen] = React.useState(false);
    const [isEditing, setIsEditing] = React.useState(false);
    const [editingTeacher, setEditingTeacher] = React.useState(null);
    const [requestRefreshRows, setRequestRefreshRows] = React.useState(false);

    const handleOpen = () => setOpen(true);
    const handleClose = () => {
        setOpen(false);
        setIsEditing(false);
        setEditingTeacher(null);
    };

    React.useEffect(() => {
        const fetchData = async () => {
            try {
                const response = await axiosInstance.get("/api/academy/teacher/");
                setRows(response.data);
                setFilteredRows(response.data);
            } catch (err) {
                console.error(err);
            } finally {
                setLoading(false);
                setRequestRefreshRows(false);
            }
        };
        fetchData();
    }, [requestRefreshRows]);

    const handleSearchTeacher = (event) => {
        const keyword = event.target.value.toLowerCase();
        if (keyword === "") {
            setFilteredRows(rows);
        } else {
            const filtered = rows.filter(
                (teacher) =>
                    (teacher.name && teacher.name.toLowerCase().includes(keyword)) ||
                    (teacher.email && teacher.email.toLowerCase().includes(keyword)) ||
                    (teacher.phone && teacher.phone.includes(keyword)) ||
                    (teacher.major && teacher.major.toLowerCase().includes(keyword)) ||
                    (teacher.note && teacher.note.toLowerCase().includes(keyword))
            );
            setFilteredRows(filtered);
        }
    };

    const style = {
        position: "absolute",
        top: "50%",
        left: "50%",
        transform: "translate(-50%, -50%)",
        width: 400,
        bgcolor: "background.paper",
        border: "none",
        boxShadow: 24,
        p: 4,
        borderRadius: "20px",
    };

    const [newTeacher, setNewTeacher] = React.useState({
        name: "",
        phone: "",
        email: "",
        major: "",
        note: "",
    });

    // Cập nhật state khi mở modal chỉnh sửa
    React.useEffect(() => {
        if (isEditing && editingTeacher) {
            setNewTeacher({
                name: editingTeacher.name || "",
                phone: editingTeacher.phone || "",
                email: editingTeacher.email || "",
                major: editingTeacher.major || "",
                note: editingTeacher.note || "",
            });
        } else {
            // Reset form khi thêm mới
            setNewTeacher({
                name: "",
                phone: "",
                email: "",
                major: "",
                note: "",
            });
        }
    }, [isEditing, editingTeacher]);

    const handleSubmitTeacher = () => {
        const formData = new FormData();
        formData.append("name", newTeacher.name);
        formData.append("phone", newTeacher.phone);
        formData.append("email", newTeacher.email);
        formData.append("major", newTeacher.major);
        formData.append("note", newTeacher.note);

        if (isEditing && editingTeacher) {
            // Cập nhật giáo viên
            axiosInstance
                .patch(`/api/academy/teacher/${editingTeacher.id}`, formData)
                .then((response) => {
                    if (response.status === 200) {
                        handleClose();
                        setRequestRefreshRows(true);
                    }
                })
                .catch((error) => {
                    console.error(error);
                });
        } else {
            // Thêm mới học viên
            axiosInstance
                .post("/api/academy/teacher/", formData)
                .then((response) => {
                    if (response.status === 201) {
                        handleClose();
                        setRequestRefreshRows(true);
                        setNewTeacher({
                            name: "",
                            phone: "",
                            email: "",
                            major: "",
                            note: "",
                        });
                    }
                })
                .catch((error) => {
                    console.error(error);
                });
        }
    };

    return (
        <AdminLayout title="Quản lý giáo viên" user={user}>
            <Box sx={{ display: "flex", gap: 2, flexDirection: "column" }}>
                <Box sx={{ display: "flex", justifyContent: "space-between", mb: "20px" }}>
                    <CustomTextField
                        sx={{ width: { xs: "100%", md: "300px" } }}
                        label="Tìm kiếm"
                        variant="outlined"
                        size="small"
                        onChange={handleSearchTeacher}
                        placeholder="Tìm theo tên, email, số điện thoại..."
                    />
                    <CustomButton variant="contained" onClick={handleOpen} startIcon={<AddIcon />}>
                        Thêm giáo viên
                    </CustomButton>
                </Box>
                <TeacherTable
                    rows={filteredRows}
                    handleEdit={(id) => {
                        setIsEditing(true);
                        setEditingTeacher(rows.find((row) => row.id === id));
                        handleOpen();
                    }}
                    setRequestRefreshRows={setRequestRefreshRows}
                />
                <Modal open={open} onClose={handleClose} sx={{ borderRadius: "20px", border: "none" }}>
                    <Box sx={style}>
                        <Typography id="modal-modal-title" variant="h6" component="h2" sx={{ mb: 2 }}>
                            {isEditing ? "Chỉnh sửa giáo viên" : "Tạo giáo viên"}
                        </Typography>
                        <Divider sx={{ mb: 2, mt: 2 }} />
                        <Box sx={{ display: "flex", gap: 2, flexDirection: "column" }}>
                            <CustomTextField
                                label="Tên giáo viên"
                                variant="outlined"
                                size="small"
                                value={newTeacher.name}
                                onChange={(e) => setNewTeacher({ ...newTeacher, name: e.target.value })}
                            />
                            <CustomTextField
                                label="Số điện thoại"
                                type="tel"
                                variant="outlined"
                                size="small"
                                value={newTeacher.phone}
                                onChange={(e) => setNewTeacher({ ...newTeacher, phone: e.target.value })}
                            />
                            <CustomTextField
                                label="Email"
                                type="email"
                                variant="outlined"
                                size="small"
                                value={newTeacher.email}
                                onChange={(e) => setNewTeacher({ ...newTeacher, email: e.target.value })}
                            />
                            <CustomTextField
                                label="Chuyên môn"
                                variant="outlined"
                                size="small"
                                value={newTeacher.major}
                                onChange={(e) => setNewTeacher({ ...newTeacher, major: e.target.value })}
                            />
                            <CustomTextField
                                label="Ghi chú"
                                variant="outlined"
                                size="small"
                                value={newTeacher.note}
                                onChange={(e) => setNewTeacher({ ...newTeacher, note: e.target.value })}
                            />
                        </Box>
                        <Box sx={{ display: "flex", justifyContent: "space-between", mt: 2 }}>
                            <CustomButton variant="contained" onClick={handleSubmitTeacher} startIcon={<CheckIcon />}>
                                {isEditing ? "Cập nhật" : "Lưu"}
                            </CustomButton>
                            <Button color="inherit" onClick={handleClose}>
                                Hủy
                            </Button>
                        </Box>
                    </Box>
                </Modal>
            </Box>
        </AdminLayout>
    );
}

export default Teacher;
