import React, { useState, useEffect } from "react";
import Grid from "@mui/material/Grid2";
import {
    Box,
    Typography,
    Paper,
    Divider,
    Chip,
    IconButton,
    Checkbox,
    FormGroup,
    FormControlLabel,
    Button,
} from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import Autocomplete from "@mui/material/Autocomplete";
import DeleteIcon from "@mui/icons-material/Delete";
import PersonAddIcon from "@mui/icons-material/PersonAdd";
import ClassIcon from "@mui/icons-material/Class";
import EditIcon from "@mui/icons-material/Edit";
import { useDocumentTitle } from "../../../hooks/useDocumentTitle";
import AdminLayout from "../Common/AdminLayout2";
import CustomTextField from "../../Common/CustomTextField";
import CustomButton from "../../Common/CustomButton";
import axiosInstance from "../../../services/axiosInstance";

import CheckIcon from "@mui/icons-material/Check";
import CancelIcon from "@mui/icons-material/Cancel";
import TaskAltIcon from '@mui/icons-material/TaskAlt';

function formatPrice(number) {
    return new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "VND",
    }).format(number);
}

function ClassStudentTable({ rows, handleRemoveStudent, handleEditStudent, classId }) {
    const columns = [
        {
            field: "id",
            headerName: "STT",
            width: 70,
        },
        {
            field: "name",
            headerName: "Tên học viên",
            width: 200,
        },
        {
            field: "birthday",
            headerName: "Ngày sinh",
            width: 130,
            // valueFormatter: (params) => new Date(params.value).toLocaleDateString(),
        },
        {
            field: "parent_name",
            headerName: "Tên Bố/Mẹ",
            width: 200,
        },
        {
            field: "parent_phone",
            headerName: "Số điện thoại",
            width: 150,
        },
        {
            field: "joined_date",
            headerName: "Ngày tham gia",
            width: 150,
            // valueFormatter: (params) => new Date(params.value).toLocaleDateString(),
        },
        {
            field: "fee",
            headerName: "Học phí",
            width: 150,
            renderCell: (params) => formatPrice(params.value),
        },
        {
            field: "discount",
            headerName: "Giảm giá",
            width: 150,
            renderCell: (params) => formatPrice(params.value),
        },
        {
            field: "paid",
            headerName: "Đã thanh toán",
            width: 150,
            renderCell: (params) => (params.value ? <CheckIcon color="success" /> : <CancelIcon color="error" />),
        },
        {
            field: "note",
            headerName: "Ghi chú",
            width: 200,
        },
        {
            field: "certificate_no",
            headerName: "Chứng nhận",
            width: 200,
        },
        {
            field: "actions",
            headerName: "Thao tác",
            width: 100,
            renderCell: (params) => (
                <Box>
                    <IconButton color="primary" onClick={() => handleEditStudent(params.row.id)}>
                        <EditIcon />
                    </IconButton>
                    <IconButton color="error" onClick={() => handleRemoveStudent(params.row.id, classId)}>
                        <DeleteIcon />
                    </IconButton>
                </Box>
            ),
        },
    ];

    const paginationModel = { page: 0, pageSize: 10 };

    return (
        <Box
            sx={{
                display: "flex",
                gap: 2,
                flexDirection: "column",
                borderRadius: "20px",
            }}
        >
            <Paper
                sx={{
                    height: "400px",
                    width: "100%",
                    borderRadius: "20px",
                    overflow: "hidden",
                    display: "flex", // Thêm display flex
                    mb: 2,
                }}
            >
                <Box
                    sx={{
                        flexGrow: 1, // Cho phép box mở rộng để lấp đầy không gian
                        width: "1px", // Chiều rộng ban đầu nhỏ
                        overflow: "hidden", // Ngăn nội dung tràn ra ngoài
                    }}
                >
                    <DataGrid
                        rows={rows}
                        columns={columns}
                        initialState={{ pagination: { paginationModel } }}
                        pageSizeOptions={[10, 25, 50]}
                        // checkboxSelection
                        sx={{
                            borderRadius: "20px",
                            // Bỏ width 100%
                            "& .MuiDataGrid-main": {
                                overflow: "auto !important",
                            },
                            "& .MuiDataGrid-virtualScroller": {
                                overflow: "auto !important",
                            },
                            "& .MuiDataGrid-cell": {
                                whiteSpace: "nowrap",
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                            },
                            "& .MuiDataGrid-columnHeaders": {
                                whiteSpace: "nowrap",
                                overflow: "hidden",
                            },
                            "& .MuiDataGrid-footerContainer": {
                                borderTop: "1px solid rgba(224, 224, 224, 1)",
                            },
                        }}
                        autoHeight={false}
                        disableExtendRowFullWidth
                    />
                </Box>
            </Paper>
        </Box>
    );
}

function ClassInfoCard({ classData, totalFee, totalDiscount }) {
    if (!classData) return null;

    // Đảm bảo schedule là một mảng
    const schedule = Array.isArray(classData.schedule)
        ? classData.schedule
        : typeof classData.schedule === "string"
            ? JSON.parse(classData.schedule)
            : [];

    return (
        <Paper sx={{ p: 2, borderRadius: "15px", mb: 1 }}>
            <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                <ClassIcon sx={{ mr: 1, color: "primary.main" }} />
                <Typography variant="h6" sx={{ mr: 1 }}>{classData.name}</Typography>
                {classData.completed && (
                    <TaskAltIcon color="success" />
                )}
            </Box>
            <Divider sx={{ mb: 3 }} />
            <Box sx={{ width: "100%" }}>
                <Grid container spacing={2} sx={{ width: "100%" }}>
                    <Grid size={{ xs: 12, md: 6 }}>
                        <Typography variant="body2" sx={{ mb: 1 }}>
                            <strong>Giáo viên:</strong> {classData.teacher ? classData.teacher.name : "Chưa phân công"}
                        </Typography>
                        <Typography variant="body2" sx={{ mb: 1 }}>
                            <strong>Phòng:</strong> {classData.room}
                        </Typography>
                        <Typography variant="body2" sx={{ mb: 1 }}>
                            <strong>Ngày bắt đầu:</strong> {new Date(classData.start_date).toLocaleDateString()}
                        </Typography>
                        <Typography variant="body2" sx={{ mb: 1 }} >
                            <strong>Trạng thái:</strong> {classData.status}
                        </Typography>
                        <Box sx={{ mb: 1 }}>
                            {classData.discount === 0 ? (
                                <Typography variant="body2">
                                    <strong>Học phí:</strong> {formatPrice(classData.fee)}
                                </Typography>
                            ) : (
                                <Box sx={{ display: "flex", alignItems: "center" }}>
                                    <strong>Học phí: </strong>
                                    <Typography variant="body2" sx={{ color: "red", ml: "5px", mr: 1 }}>
                                        {formatPrice(classData.fee - classData.discount)}
                                    </Typography>
                                    <Typography variant="body2" sx={{ textDecoration: "line-through" }}>
                                        {formatPrice(classData.fee)}
                                    </Typography>
                                </Box>
                            )}
                        </Box>

                    </Grid>
                    <Grid size={{ xs: 12, md: 6 }}>
                        <Typography variant="body2" sx={{ mb: 1 }}>
                            <strong>Số buổi học:</strong> {classData.number_of_lessons}
                        </Typography>
                        <Typography variant="body2" sx={{ mb: 1 }}>
                            <strong>Lịch học:</strong>
                        </Typography>
                        <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1, mb: 1 }}>
                            {schedule.map((item, index) => (
                                <Chip
                                    key={index}
                                    label={`${item.day}: ${item.start} - ${item.end}`}
                                    size="small"
                                    color="primary"
                                />
                            ))}
                        </Box>
                        <Typography variant="body2" sx={{ mb: 1 }}>
                            <strong>Tổng thu: {formatPrice(totalFee - totalDiscount)}</strong>
                        </Typography>
                    </Grid>
                </Grid>
            </Box>
        </Paper>
    );
}

function ClassStudent({ user }) {
    useDocumentTitle("Quản lý học viên lớp học | BeE");

    const [loading, setLoading] = useState(true);
    const [classes, setClasses] = useState([]);
    const [students, setStudents] = useState([]);
    const [classStudents, setClassStudents] = useState([]);
    const [selectedClass, setSelectedClass] = useState(null);
    const [selectedStudent, setSelectedStudent] = useState(null);
    const [filteredStudents, setFilteredStudents] = useState([]);
    const [searchTerm, setSearchTerm] = useState("");
    const [fee, setFee] = useState("");
    const [discount, setDiscount] = useState(0);
    const [paid, setPaid] = useState(false);
    const [note, setNote] = useState("");
    const [certificate, setCertificate] = useState("");
    const [isEditing, setIsEditing] = useState(false);
    const [editingStudentId, setEditingStudentId] = useState(null);
    const [totalFee, setTotalFee] = useState(0);
    const [totalDiscount, setTotalDiscount] = useState(0);

    // Fetch classes, students, and class-student relationships
    useEffect(() => {
        const fetchData = async () => {
            try {
                setLoading(true);
                const [classesResponse, studentsResponse] = await Promise.all([
                    axiosInstance.get("/api/academy/class/"),
                    axiosInstance.get("/api/academy/student/"),
                ]);

                // Process classes data
                const processedClasses = classesResponse.data.map((classItem) => ({
                    ...classItem,
                    schedule:
                        typeof classItem.schedule === "string"
                            ? JSON.parse(classItem.schedule)
                            : Array.isArray(classItem.schedule)
                                ? classItem.schedule
                                : [],
                }));

                setClasses(processedClasses);
                setStudents(studentsResponse.data);
                setFilteredStudents(studentsResponse.data);

                // If a class is selected, fetch its students
                if (selectedClass) {
                    fetchClassStudents(selectedClass.id);
                }
            } catch (err) {
                console.error("Error fetching data:", err);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, [selectedClass?.id]);

    // Fetch students for a specific class
    const fetchClassStudents = async (classId) => {
        try {
            const response = await axiosInstance.get(`/api/academy/class-student/${classId}`);
            let totalFee_temp = 0;
            let totalDiscount_temp = 0;
            response.data.map((cs) => {
                totalFee_temp += cs.fee;
                totalDiscount_temp += cs.discount;
            });
            setTotalFee(totalFee_temp);
            setTotalDiscount(totalDiscount_temp);
            setClassStudents(
                !Array.isArray(response.data)
                    ? [
                        {
                            ...response.data.student,
                            joined_date: response.data.joined_date,
                            fee: response.data.fee,
                            discount: response.data.discount,
                            paid: response.data.paid,
                            note: response.data.note,
                            certificate_no: response.data.certificate_no
                        },
                    ]
                    : response.data.map((cs) => {
                        return {
                            ...cs.student,
                            joined_date: cs.joined_date,
                            fee: cs.fee,
                            discount: cs.discount,
                            paid: cs.paid,
                            note: cs.note,
                            certificate_no: cs.certificate_no
                        };
                    })
            );
        } catch (err) {
            console.error("Error fetching class students:", err);
            setClassStudents([]);
        }
    };

    // Handle class selection
    const handleClassChange = (event, newValue) => {
        setSelectedClass(newValue);
        setFee(newValue.fee - newValue.discount);
        setSelectedStudent(null);
    };

    // Handle student selection
    const handleStudentChange = (event, newValue) => {
        setSelectedStudent(newValue);
    };

    // Handle student search
    const handleSearchStudent = (event) => {
        const searchTerm = event.target.value.toLowerCase();
        setSearchTerm(searchTerm);

        if (!searchTerm) {
            setFilteredStudents(students);
            return;
        }

        const filtered = students.filter(
            (student) =>
                student.name.toLowerCase().includes(searchTerm) ||
                (student.email && student.email.toLowerCase().includes(searchTerm)) ||
                (student.parent_phone && student.parent_phone.includes(searchTerm))
        );

        setFilteredStudents(filtered);
    };

    // Add or update student in class
    const handleAddStudent = async () => {
        if (!selectedClass || !selectedStudent) {
            alert("Vui lòng chọn lớp học và học viên");
            return;
        }

        try {
            if (isEditing && editingStudentId) {
                // Cập nhật thông tin học viên trong lớp
                await axiosInstance.patch(
                    `/api/academy/class-student/${selectedClass.id}/student/${editingStudentId}`,
                    {
                        fee: fee,
                        discount: discount,
                        paid: paid,
                        note: note,
                        certificate_no: certificate
                    }
                );
            } else {
                // Thêm học viên mới vào lớp
                await axiosInstance.post("/api/academy/class-student/", {
                    student_id: selectedStudent.id,
                    classroom_id: selectedClass.id,
                    fee: fee,
                    discount: discount,
                    paid: paid,
                    note: note,
                    certificate_no: certificate
                });
            }

            // Refresh class students
            fetchClassStudents(selectedClass.id);

            // Reset form
            setSelectedStudent(null);
            setDiscount("");
            setPaid(false);
            setNote("");
            setCertificate("");
            setIsEditing(false);
            setEditingStudentId(null);
        } catch (err) {
            console.error("Error managing student in class:", err);
            alert("Có lỗi xảy ra khi thao tác với học viên");
        }
    };

    // Remove student from class
    const handleRemoveStudent = async (studentId, classId) => {
        if (!classId) return;

        if (window.confirm("Bạn có chắc chắn muốn xóa học viên này khỏi lớp?")) {
            try {
                await axiosInstance.delete(`/api/academy/class-student/${classId}/student/${studentId}`);
                // Refresh class students
                fetchClassStudents(classId);
            } catch (err) {
                console.error("Error removing student from class:", err);
                alert("Có lỗi xảy ra khi xóa học viên khỏi lớp");
            }
        }
    };

    // Handle edit student
    const handleEditStudent = (studentId) => {
        // Tìm thông tin học viên trong danh sách classStudents
        const studentToEdit = classStudents.find((student) => student.id === studentId);
        if (!studentToEdit) return;

        // Cập nhật state để hiển thị thông tin học viên trong form
        setSelectedStudent(studentToEdit);
        setFee(studentToEdit.fee || 0);
        setDiscount(studentToEdit.discount || 0);
        setNote(studentToEdit.note || "");
        setPaid(studentToEdit.paid || false);
        setCertificate(studentToEdit.certificate_no || "");

        // Đánh dấu đang chỉnh sửa
        setIsEditing(true);
        setEditingStudentId(studentId);

        // Cuộn trang lên form chỉnh sửa
        window.scrollTo({
            top: 0,
            behavior: "smooth",
        });
    };

    // Filter out students already in the class
    const availableStudents = filteredStudents.filter((student) => !classStudents.some((cs) => cs.id === student.id));

    return (
        <AdminLayout title="Quản lý học viên lớp học" user={user}>
            <Box sx={{ display: "flex", gap: 2, flexDirection: "column" }}>
                {/* Class selection */}
                <Grid container spacing={2}>
                    <Grid size={{ xs: 12, md: 5 }}>
                        <Paper sx={{ p: 2, borderRadius: "15px", mb: 2 }}>
                            <Typography variant="h6" sx={{ mb: 2 }}>
                                Thông tin khóa học
                            </Typography>
                            <Autocomplete
                                options={classes}
                                getOptionLabel={(option) => option.name}
                                value={selectedClass}
                                onChange={handleClassChange}
                                renderInput={(params) => (
                                    <CustomTextField {...params} label="Lớp học" placeholder="Chọn lớp học" />
                                )}
                                sx={{ mb: 2 }}
                            />
                            {selectedClass && (
                                <ClassInfoCard
                                    classData={selectedClass}
                                    totalFee={totalFee}
                                    totalDiscount={totalDiscount}
                                />
                            )}
                        </Paper>
                    </Grid>
                    <Grid size={{ xs: 12, md: 7 }} sx={{ overflow: "hidden" }}>
                        {/* Add student to class */}
                        {selectedClass && (
                            <Paper sx={{ p: 2, borderRadius: "15px", mb: 3 }}>
                                <Typography variant="h6" sx={{ mb: 2 }}>
                                    {isEditing ? "Cập nhật thông tin học viên" : "Thêm học viên vào lớp"}
                                </Typography>
                                <Box sx={{ display: "flex", gap: 2, mb: 2 }}>
                                    <CustomTextField
                                        label="Tìm kiếm học viên"
                                        variant="outlined"
                                        sx={{ width: "50%" }}
                                        value={searchTerm}
                                        onChange={handleSearchStudent}
                                        placeholder="Tìm theo tên, email, số điện thoại..."
                                        disabled={isEditing}
                                    />
                                    <Autocomplete
                                        options={availableStudents}
                                        getOptionLabel={(option) =>
                                            `${option.name} - ${option.parent_phone || "Không có SĐT"}`
                                        }
                                        value={selectedStudent}
                                        onChange={handleStudentChange}
                                        renderInput={(params) => (
                                            <CustomTextField
                                                {...params}
                                                label="Học viên"
                                                placeholder="Chọn học viên"
                                                fullWidth
                                            />
                                        )}
                                        sx={{ flexGrow: 1, width: "50%" }}
                                        disabled={isEditing}
                                    />
                                </Box>

                                <Box sx={{ display: "flex", gap: 2, mb: 2 }}>
                                    <CustomTextField
                                        label={"Học phí: " + formatPrice(fee)}
                                        variant="outlined"
                                        sx={{ width: "50%" }}
                                        value={fee}
                                        onChange={(e) => setFee(e.target.value)}
                                        placeholder="Học phí"
                                    />
                                    <CustomTextField
                                        label={"Giảm giá: " + formatPrice(discount)}
                                        variant="outlined"
                                        sx={{ width: "50%" }}
                                        value={discount}
                                        onChange={(e) => setDiscount(e.target.value)}
                                        placeholder="Giảm giá"
                                    />
                                </Box>
                                <Box sx={{ display: "flex", gap: 2, mb: 2 }}>
                                    <CustomTextField
                                        label="Ghi chú"
                                        variant="outlined"
                                        sx={{ width: "50%" }}
                                        value={note}
                                        onChange={(e) => setNote(e.target.value)}
                                        placeholder="Ghi chú"
                                    />
                                    <CustomTextField
                                        label="Chứng nhận tốt nghiệp"
                                        variant="outlined"
                                        sx={{ width: "50%" }}
                                        value={certificate}
                                        onChange={(e) => setCertificate(e.target.value)}
                                        placeholder="Ex:10010"
                                    />
                                </Box>
                                <FormGroup sx={{ mb: 1 }}>
                                    <FormControlLabel
                                        control={
                                            <Checkbox checked={paid} onChange={(e) => setPaid(e.target.checked)} />
                                        }
                                        label="Đã thanh toán"
                                    />
                                </FormGroup>
                                <Box sx={{ display: "flex", gap: 2 }}>
                                    <CustomButton
                                        variant="contained"
                                        startIcon={isEditing ? <EditIcon /> : <PersonAddIcon />}
                                        onClick={handleAddStudent}
                                        disabled={!selectedStudent}
                                        sx={{ width: isEditing ? "50%" : "100%" }}
                                    >
                                        {isEditing ? "Cập nhật" : "Thêm"}
                                    </CustomButton>
                                    {/* Thêm nút hủy chỉnh sửa nếu đang trong chế độ chỉnh sửa */}
                                    {isEditing && (
                                        <Button
                                            sx={{ borderRadius: "10px", width: "50%" }}
                                            variant="outlined"
                                            color="inherit"
                                            onClick={() => {
                                                setIsEditing(false);
                                                setEditingStudentId(null);
                                                setSelectedStudent(null);
                                                setFee("");
                                                setDiscount("");
                                                setPaid(false);
                                                setNote("");
                                            }}
                                            startIcon={<CancelIcon />}
                                        >
                                            Hủy chỉnh sửa
                                        </Button>
                                    )}
                                </Box>
                            </Paper>
                        )}
                    </Grid>
                </Grid>
                {/* Display class students */}
                {selectedClass && (
                    <Paper sx={{ p: 2, borderRadius: "15px", mb: 2 }}>
                        <Typography variant="h6" sx={{ mb: 2 }}>
                            Danh sách học viên trong lớp {selectedClass.name}
                        </Typography>
                        {loading ? (
                            <Typography>Đang tải...</Typography>
                        ) : (
                            <ClassStudentTable
                                rows={classStudents}
                                handleRemoveStudent={handleRemoveStudent}
                                handleEditStudent={handleEditStudent}
                                classId={selectedClass.id}
                            />
                        )}
                    </Paper>
                )}
            </Box>
        </AdminLayout>
    );
}

export default ClassStudent;
