import React from "react";
import AdminLayout from "../Common/AdminLayout2";
import { DataGrid } from "@mui/x-data-grid";
import {
    Box,
    Button,
    Paper,
    Switch,
    Divider,
    Typography,
    styled,
    Checkbox,
    FormControlLabel,
    Modal,
    IconButton,
} from "@mui/material";
import { deepPurple, grey, green, red } from "@mui/material/colors";
import FileUploadIcon from "@mui/icons-material/FileUpload";
import { useParams } from "react-router-dom";
import axiosInstance from "../../../services/axiosInstance";
import Loading from "../../Common/Loading";
import { useDocumentTitle } from "../../../hooks/useDocumentTitle";
import CustomTextField from "../../Common/CustomTextField";
import CustomButton from "../../Common/CustomButton";

import AddIcon from "@mui/icons-material/Add";
import CheckIcon from "@mui/icons-material/Check";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs from "dayjs";

function StudentTable({ rows, handleEdit, setRequestRefreshRows }) {
    const handleDelete = (id) => {
        if (window.confirm("Bạn có chắc chắn muốn xóa học viên này?")) {
            // Thêm logic xóa học viên ở đây
            axiosInstance
                .delete(`/api/academy/student/${id}`)
                .then(() => {
                    setRequestRefreshRows(true);
                })
                .catch((error) => {
                    console.error("Error deleting student:", error);
                });
        }
    };

    const columns = [
        {
            field: "id",
            headerName: "STT",
            width: 70,
        },
        {
            field: "name",
            headerName: "Tên học viên",
            width: 150,
        },
        {
            field: "birthday",
            headerName: "Ngày sinh",
            width: 130,
        },
        {
            field: "parent_name",
            headerName: "Tên Bố/Mẹ",
            width: 150,
        },
        {
            field: "parent_phone",
            headerName: "Số điện thoại",
            width: 150,
        },
        {
            field: "email",
            headerName: "Email",
            width: 200,
        },
        {
            field: "note",
            headerName: "Ghi chú",
            width: 200,
        },
        {
            field: "action",
            headerName: "Thao tác",
            width: 120,
            renderCell: (params) => (
                <Box sx={{ display: "flex", justifyContent: "center" }}>
                    <IconButton sx={{ color: green[600] }} size="small" onClick={() => handleEdit(params.row.id)}>
                        <EditIcon />
                    </IconButton>
                    <IconButton sx={{ color: red[600] }} size="small" onClick={() => handleDelete(params.row.id)}>
                        <DeleteIcon />
                    </IconButton>
                </Box>
            ),
        },
    ];

    const paginationModel = { page: 0, pageSize: 10 };

    return (
        <Box
            sx={{
                display: "flex",
                gap: 2,
                flexDirection: "column",
                borderRadius: "20px",
            }}
        >
            <Paper
                sx={{
                    height: "400px",
                    width: "100%",
                    borderRadius: "20px",
                    overflow: "hidden",
                    display: "flex",
                    mb: 2,
                }}
            >
                <Box
                    sx={{
                        flexGrow: 1,
                        width: "1px",
                        overflow: "hidden",
                    }}
                >
                    <DataGrid
                        rows={rows}
                        columns={columns}
                        initialState={{ pagination: { paginationModel } }}
                        pageSizeOptions={[10, 25, 50]}
                        checkboxSelection
                        sx={{
                            borderRadius: "20px",
                            "& .MuiDataGrid-main": {
                                overflow: "auto !important",
                            },
                            "& .MuiDataGrid-virtualScroller": {
                                overflow: "auto !important",
                            },
                            "& .MuiDataGrid-cell": {
                                whiteSpace: "nowrap",
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                            },
                            "& .MuiDataGrid-columnHeaders": {
                                whiteSpace: "nowrap",
                                overflow: "hidden",
                            },
                        }}
                        autoHeight={false}
                        disableExtendRowFullWidth
                    />
                </Box>
            </Paper>
        </Box>
    );
}

function Student({ user }) {
    useDocumentTitle("Quản lý học viên | BeE");

    const [loading, setLoading] = React.useState(true);
    const [rows, setRows] = React.useState([]);
    const [filteredRows, setFilteredRows] = React.useState([]);
    const [open, setOpen] = React.useState(false);
    const [isEditing, setIsEditing] = React.useState(false);
    const [editingStudent, setEditingStudent] = React.useState(null);
    const [requestRefreshRows, setRequestRefreshRows] = React.useState(false);

    const handleOpen = () => setOpen(true);
    const handleClose = () => {
        setOpen(false);
        setIsEditing(false);
        setEditingStudent(null);
    };

    React.useEffect(() => {
        const fetchData = async () => {
            try {
                const response = await axiosInstance.get("/api/academy/student/");
                setRows(response.data);
                setFilteredRows(response.data);
            } catch (err) {
                console.error(err);
            } finally {
                setLoading(false);
                setRequestRefreshRows(false);
            }
        };
        fetchData();
    }, [requestRefreshRows]);

    const handleSearchStudent = (event) => {
        const keyword = event.target.value.toLowerCase();
        if (keyword === "") {
            setFilteredRows(rows);
        } else {
            const filtered = rows.filter(
                (student) =>
                    (student.name && student.name.toLowerCase().includes(keyword)) ||
                    (student.email && student.email.toLowerCase().includes(keyword)) ||
                    (student.parent_name && student.parent_name.toLowerCase().includes(keyword)) ||
                    (student.parent_phone && student.parent_phone.includes(keyword)) ||
                    (student.note && student.note.toLowerCase().includes(keyword))
            );
            setFilteredRows(filtered);
        }
    };

    const style = {
        position: "absolute",
        top: "50%",
        left: "50%",
        transform: "translate(-50%, -50%)",
        width: "90%",
        maxWidth: 400,
        bgcolor: "background.paper",
        border: "none",
        boxShadow: 24,
        p: 4,
        borderRadius: "20px",
    };

    const [newStudent, setNewStudent] = React.useState({
        name: "",
        birthday: dayjs(),
        parent_name: "",
        parent_phone: "",
        email: "",
        note: "",
    });

    // Cập nhật state khi mở modal chỉnh sửa
    React.useEffect(() => {
        if (isEditing && editingStudent) {
            setNewStudent({
                name: editingStudent.name || "",
                birthday: editingStudent.birthday || "",
                parent_name: editingStudent.parent_name || "",
                parent_phone: editingStudent.parent_phone || "",
                email: editingStudent.email || "",
                note: editingStudent.note || "",
            });
        } else {
            // Reset form khi thêm mới
            setNewStudent({
                name: "",
                birthday: dayjs(),
                parent_name: "",
                parent_phone: "",
                email: "",
                note: "",
            });
        }
    }, [isEditing, editingStudent]);

    const handleSubmitStudent = () => {
        if (!newStudent.name) {
            alert("Tên học viên không được để trống!");
            return;
        }
        if (!newStudent.birthday) {
            alert("Ngày sinh không được để trống!");
            return;
        }
        if (!newStudent.parent_name) {
            alert("Tên Bố/Mẹ không được để trống!");
            return;
        }
        if (!newStudent.parent_phone) {
            alert("Số điện thoại không được để trống!");
            return;
        }

        const formData = new FormData();
        formData.append("name", newStudent.name);
        formData.append("birthday", newStudent.birthday.format("YYYY-MM-DD"));
        formData.append("parent_name", newStudent.parent_name);
        formData.append("parent_phone", newStudent.parent_phone);
        formData.append("email", newStudent.email || "");
        formData.append("note", newStudent.note || "");

        if (isEditing && editingStudent) {
            // Cập nhật học viên
            axiosInstance
                .patch(`/api/academy/student/${editingStudent.id}`, formData)
                .then((response) => {
                    if (response.status === 200) {
                        handleClose();
                        setRequestRefreshRows(true);
                    }
                })
                .catch((error) => {
                    console.error(error);
                });
        } else {
            // Thêm mới học viên
            axiosInstance
                .post("/api/academy/student/", formData)
                .then((response) => {
                    if (response.status === 201) {
                        handleClose();
                        setRequestRefreshRows(true);
                        setNewStudent({
                            name: "",
                            birthday: "",
                            parent_name: "",
                            parent_phone: "",
                            email: "",
                            note: "",
                        });
                    }
                })
                .catch((error) => {
                    console.error(error);
                });
        }
    };

    const handleDateChange = (date) => {
        setNewStudent((prev) => ({
            ...prev,
            birthday: date,
        }));
    };

    return (
        <AdminLayout title="Quản lý học viên" user={user}>
            <Box sx={{ display: "flex", gap: 2, flexDirection: "column" }}>
                <Box sx={{ display: "flex", justifyContent: "space-between", mb: "20px" }}>
                    <CustomTextField
                        sx={{ width: { xs: "200px", md: "300px" } }}
                        label="Tìm kiếm"
                        variant="outlined"
                        size="small"
                        onChange={handleSearchStudent}
                        placeholder="Tìm theo tên, email, số điện thoại..."
                    />
                    <CustomButton
                        variant="contained"
                        onClick={handleOpen}
                        startIcon={<AddIcon />}
                        sx={{ display: { xs: "none", md: "flex" } }}
                    >
                        Thêm học viên
                    </CustomButton>
                    <IconButton
                        onClick={handleOpen}
                        sx={{
                            display: { xs: "flex", md: "none" },
                            color: "white",
                            backgroundColor: red[500],
                        }}
                    >
                        <AddIcon />
                    </IconButton>
                </Box>
                <StudentTable
                    rows={filteredRows}
                    handleEdit={(id) => {
                        setIsEditing(true);
                        setEditingStudent(rows.find((row) => row.id === id));
                        handleOpen();
                    }}
                    setRequestRefreshRows={setRequestRefreshRows}
                />
                <Modal open={open} onClose={handleClose} sx={{ borderRadius: "20px", border: "none" }}>
                    <Box sx={style}>
                        <Typography id="modal-modal-title" variant="h6" component="h2" sx={{ mb: 2 }}>
                            {isEditing ? "Chỉnh sửa học viên" : "Tạo học viên"}
                        </Typography>
                        <Divider sx={{ mb: 2, mt: 2 }} />
                        <Box sx={{ display: "flex", gap: 2, flexDirection: "column" }}>
                            <CustomTextField
                                label="Tên học viên"
                                variant="outlined"
                                // size="small"
                                value={newStudent.name}
                                onChange={(e) => setNewStudent({ ...newStudent, name: e.target.value })}
                            />
                            <LocalizationProvider dateAdapter={AdapterDayjs}>
                                <DatePicker
                                    label="Ngày sinh"
                                    value={typeof (newStudent.birthday) === "string" ? dayjs(newStudent.birthday) : newStudent.birthday}
                                    onChange={handleDateChange}
                                    slots={{
                                        textField: CustomTextField,
                                    }}
                                    slotProps={{
                                        textField: {
                                            fullWidth: true,
                                            required: true,
                                            // size: "small",
                                        },
                                    }}
                                />
                            </LocalizationProvider>
                            <CustomTextField
                                label="Tên Bố/Mẹ"
                                variant="outlined"
                                // size="small"
                                value={newStudent.parent_name}
                                onChange={(e) => setNewStudent({ ...newStudent, parent_name: e.target.value })}
                            />
                            <CustomTextField
                                label="Số điện thoại"
                                type="tel"
                                variant="outlined"
                                // size="small"
                                value={newStudent.parent_phone}
                                onChange={(e) => setNewStudent({ ...newStudent, parent_phone: e.target.value })}
                            />
                            <CustomTextField
                                label="Email"
                                type="email"
                                variant="outlined"
                                // size="small"
                                value={newStudent.email}
                                onChange={(e) => setNewStudent({ ...newStudent, email: e.target.value })}
                            />
                            <CustomTextField
                                label="Ghi chú"
                                variant="outlined"
                                // size="small"
                                value={newStudent.note}
                                onChange={(e) => setNewStudent({ ...newStudent, note: e.target.value })}
                            />
                        </Box>
                        <Box sx={{ display: "flex", justifyContent: "space-between", mt: 2 }}>
                            <CustomButton variant="contained" onClick={handleSubmitStudent} startIcon={<CheckIcon />}>
                                {isEditing ? "Cập nhật" : "Lưu"}
                            </CustomButton>
                            <Button color="inherit" onClick={handleClose}>
                                Hủy
                            </Button>
                        </Box>
                    </Box>
                </Modal>
            </Box>
        </AdminLayout>
    );
}

export default Student;
