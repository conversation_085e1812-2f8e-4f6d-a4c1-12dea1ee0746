class SerialConnection {
    constructor() {
        this.port = null;
        this.reader = null;
        this.writer = null;
    }

    async disconnect() {
        if (this.port) {
            try {
                // Giải phóng writer trước
                if (this.writer) {
                    try {
                        await this.writer.close();
                        await new Promise((resolve) => setTimeout(resolve, 100));
                        this.writer = null;
                    } catch (error) {
                        console.warn("Error closing writer:", error);
                    }
                }

                // Giải phóng reader
                if (this.reader) {
                    try {
                        await this.reader.cancel();
                        await new Promise((resolve) => setTimeout(resolve, 100));
                        await this.reader.releaseLock();
                        this.reader = null;
                    } catch (error) {
                        console.warn("Error releasing reader:", error);
                    }
                }

                // <PERSON><PERSON><PERSON> bảo tất cả streams đã được unlock
                const streams = this.port.readable.locked || this.port.writable.locked;
                if (streams) {
                    await new Promise((resolve) => setTimeout(resolve, 500));
                }

                // Đóng port
                await this.port.close();
                await new Promise((resolve) => setTimeout(resolve, 100));
                this.port = null;

                return {
                    success: true,
                    connected: false,
                    message: "Disconnected!",
                };
            } catch (error) {
                console.error("Error while disconnecting:", error);
                return {
                    success: false,
                    connected: true,
                    message: "Error while disconnecting: " + error.message,
                };
            }
        }
    }

    async connect() {
        this.disconnect();
        try {
            const filters = [
                // Silicon Labs CP210x (ESP32 DevKit, NodeMCU)
                { usbVendorId: 0x10c4, usbProductId: 0xea60 },

                // WCH CH340/CH341
                { usbVendorId: 0x1a86, usbProductId: 0x7523 },
                { usbVendorId: 0x1a86, usbProductId: 0x5523 },

                // Espressif native USB (ESP32-S2 / S3)
                { usbVendorId: 0x303a }, // Không filter PID để bắt mọi firmware Espressif
            ];
            const port = await navigator.serial.requestPort({ filters });

            try {
                await port.open({ baudRate: 115200 });
            } catch (error) {
                if (error.name === "InvalidStateError") {
                    await new Promise((resolve) => setTimeout(resolve, 1000));
                    await port.open({ baudRate: 115200 });
                } else {
                    throw error;
                }
            }

            this.port = port;
            this.writer = port.writable.getWriter();
            this.reader = port.readable.getReader();

            // Kiểm tra firmware trước khi nạp code
            if (import.meta.env.VITE_ENV !== "dev") {
                const firmwareCheck = await this.checkHardware();
                if (!firmwareCheck.success || !firmwareCheck.isBeeBrain) {
                    this.disconnect();
                    return {
                        success: false,
                        connected: false,
                        message: "Please use BeeBrain firmware to upload code!",
                    };
                }
            }

            return {
                success: true,
                connected: true,
                reader: this.reader,
                writer: this.writer,
                message: "Connected successfully!",
            };
        } catch (error) {
            console.error("Error:", error);
            return {
                success: false,
                connected: false,
                message: error.message,
            };
        }
    }

    async checkHardware() {
        if (!this.port || !this.writer) {
            return {
                success: false,
                message: "Please connect to the board first!",
            };
        }

        try {
            // Ensure any existing reader is released
            if (this.reader) {
                await this.reader.cancel();
                await this.reader.releaseLock();
            }

            const encoder = new TextEncoder();

            // Reset REPL và gửi Ctrl-B
            await this.writer.write(encoder.encode("\x03\x03")); // Ctrl-C twice
            await new Promise((resolve) => setTimeout(resolve, 100));
            await this.writer.write(encoder.encode("\x02")); // Ctrl-B
            await new Promise((resolve) => setTimeout(resolve, 100));

            // Create new reader
            this.reader = this.port.readable.getReader();

            // Read response with timeout
            const response = await Promise.race([
                this.readResponseCheckHW(),
                new Promise((_, reject) => setTimeout(() => reject(new Error("Timeout checking firmware")), 2000)),
            ]);

            // Kiểm tra response có chứa "BeeBrain"
            if (response.includes("BeeBrain")) {
                return {
                    success: true,
                    isBeeBrain: true,
                    message: "BeeBrain firmware detected",
                    version: response.trim(),
                };
            } else {
                alert("Please use BeeBrain firmware to upload code!");
                return {
                    success: true,
                    isBeeBrain: false,
                    message: "This is not BeeBrain firmware",
                    version: response.trim(),
                };
            }
        } catch (error) {
            console.error("Error checking hardware:", error);
            return {
                success: false,
                isBeeBrain: false,
                message: "Error checking firmware: " + error.message,
            };
        }
    }

    async uploadCode(code) {
        if (!this.port || !this.writer) {
            return {
                success: false,
                message: "Please connect to the board first!",
            };
        }

        try {
            // Ensure any existing reader is released
            if (this.reader) {
                await this.reader.cancel();
                await this.reader.releaseLock();
                // this.reader = null; // Terminal will reuse this.reader, so, please don't set this.reader = null;
            }

            const encoder = new TextEncoder();

            // Reset REPL
            await this.writer.write(encoder.encode("\x03\x03")); // Ctrl-C
            // await this.writer.write(encoder.encode('\x02')); // Ctrl-B
            await new Promise((resolve) => setTimeout(resolve, 100));

            // 1. Bật chế độ Raw REPL
            await this.writer.write(encoder.encode("\x01")); // Ctrl-A (Enter Raw REPL)
            await new Promise((resolve) => setTimeout(resolve, 100));

            // Định dạng code để gửi
            const formattedCode = code.replace(/\r\n/g, "\n").replace(/\n/g, "\r\n"); // Thêm Ctrl-D để chạy

            await this.writer.write(encoder.encode(formattedCode));
            await new Promise((resolve) => setTimeout(resolve, 100));

            // Kết thúc input và thực thi code
            await this.writer.write(encoder.encode("\x04")); // Ctrl-D để thực thi
            await new Promise((resolve) => setTimeout(resolve, 1000));

            // Exit Raw REPL mode
            await this.writer.write(encoder.encode("\x02")); // Ctrl-B
            await new Promise((resolve) => setTimeout(resolve, 100));

            // Create new reader
            this.reader = this.port.readable.getReader();

            // Read response with timeout
            const response = await Promise.race([
                this.readResponse(),
                new Promise((_, reject) => setTimeout(() => reject(new Error("Timeout waiting for response")), 60000)),
            ]);

            return {
                success: true,
                message: response.includes("OK") ? response.split("OK")[1] : response, //"Code uploaded successfully",
                response: response,
            };
        } catch (error) {
            console.error("Lỗi nạp code:", error);
            return {
                success: false,
                message: error.message,
            };
        } finally {
            if (this.reader) {
                await this.reader.cancel();
                await this.reader.releaseLock();
                this.reader = null;
            }
        }
    }

    async readResponseCheckHW() {
        if (!this.reader) {
            throw new Error("No reader available");
        }

        let response = "";
        const decoder = new TextDecoder();

        try {
            while (true) {
                const { value, done } = await this.reader.read();
                if (done) break;

                const text = decoder.decode(value);
                response += text;

                // Raw REPL returns:
                // 1. OK + output (if successful)
                // 2. Error message (if failed)
                if (response.includes("\x04")) {
                    // Found end of output marker
                    const parts = response.split("\x04");
                    if (parts.length >= 2) {
                        // Get the output (first part)
                        const output = parts[0] || "";

                        // Check for error (second part, if exists)
                        if (parts.length > 1 && parts[1] && parts[1].trim()) {
                            throw new Error(parts[1].trim());
                        }
                        return output.trim();
                    }
                    break;
                }

                // await new Promise(resolve => setTimeout(resolve, 10));
                return response.trim();
            }
        } catch (error) {
            console.error("Error reading response:", error);
            // throw error;
        }

        return response;
    }

    async readResponse() {
        if (!this.reader) {
            throw new Error("No reader available");
        }

        let response = "";
        const decoder = new TextDecoder();

        try {
            while (true) {
                const { value, done } = await this.reader.read();
                if (done) break;

                const text = decoder.decode(value);
                response += text;

                // Raw REPL returns:
                // 1. OK + output (if successful)
                // 2. Error message (if failed)
                if (response.includes("\x04")) {
                    // Found end of output marker
                    const parts = response.split("\x04");
                    if (parts.length >= 2) {
                        // Get the output (first part)
                        const output = parts[0] || "";

                        // Check for error (second part, if exists)
                        if (parts.length > 1 && parts[1] && parts[1].trim()) {
                            throw new Error(parts[1].trim());
                        }
                        return output.trim();
                    }
                    break;
                }

                // await new Promise(resolve => setTimeout(resolve, 10));
                return response.trim();
            }
        } catch (error) {
            console.error("Error reading response:", error);
            throw error;
        }

        return response;
    }

    async uploadFile(code, name = "main.py") {
        // Kiểm tra firmware trước khi nạp code
        if (!this.port || !this.writer) {
            return {
                success: false,
                message: "Please connect to the board first!",
            };
        }

        try {
            // Ensure any existing reader is released
            if (this.reader) {
                await this.reader.cancel();
                await this.reader.releaseLock();
                // this.reader = null;
            }

            const encoder = new TextEncoder();

            // Reset REPL
            await this.writer.write(encoder.encode("\x03\x03")); // Ctrl-C twice to ensure we exit any running program
            await new Promise((resolve) => setTimeout(resolve, 100));

            // Create and write main.py file
            const writeFileCommand = `f = open('${name}', 'w')\nf.write(${JSON.stringify(code)})\nf.close()\n`;
            // Split code into lines and send
            const lines = writeFileCommand.split("\n");
            for (const line of lines) {
                await this.writer.write(encoder.encode(line + "\r\n"));
                await new Promise((resolve) => setTimeout(resolve, 100));
            }

            // Soft reset to execute main.py
            await this.writer.write(encoder.encode("\x04")); // Ctrl-D for soft reset
            await new Promise((resolve) => setTimeout(resolve, 500));

            // Set up new reader for response
            this.reader = this.port.readable.getReader();

            // Read response with timeout
            const response = await Promise.race([
                this.readResponse(),
                new Promise((_, reject) => setTimeout(() => reject(new Error("Timeout waiting for response")), 5000)),
            ]);

            return {
                success: true,
                message: response, //"Code uploaded successfully",
                response: response,
            };
        } catch (error) {
            console.error("Error uploading code:", error);
            return {
                success: false,
                message: "Error uploading code: " + error.message,
            };
        }
    }

    // Add a new method to execute main.py directly
    async runMainPy() {
        if (!this.port || !this.writer) {
            return {
                success: false,
                message: "Please connect to the board first!",
            };
        }

        try {
            const encoder = new TextEncoder();

            // Reset REPL
            await this.writer.write(encoder.encode("\x03\x03")); // Ctrl-C twice
            await new Promise((resolve) => setTimeout(resolve, 100));

            // Execute main.py
            await this.writer.write(encoder.encode('exec(open("main.py").read())\r\n'));
            await new Promise((resolve) => setTimeout(resolve, 100));

            return {
                success: true,
                message: "Executing main.py",
            };
        } catch (error) {
            console.error("Error running main.py:", error);
            return {
                success: false,
                message: "Error running main.py: " + error.message,
            };
        }
    }

    async enterREPL() {
        if (!this.port || !this.writer) {
            return {
                success: false,
                message: "Please connect to the board first!",
            };
        }

        try {
            const encoder = new TextEncoder();

            // Send Ctrl-C twice to interrupt any running program and enter REPL
            await this.writer.write(encoder.encode("\x03\x03"));
            await new Promise((resolve) => setTimeout(resolve, 100));

            // Send enter to show the prompt
            await this.writer.write(encoder.encode("\r\n"));

            return {
                success: true,
                message: "Entered REPL mode",
            };
        } catch (error) {
            return {
                success: false,
                message: "Error entering REPL mode: " + error.message,
            };
        }
    }

    async softReset() {
        if (!this.port || !this.writer) {
            return {
                success: false,
                message: "Please connect to the board first!",
            };
        }

        try {
            const encoder = new TextEncoder();

            // Send Ctrl-D for soft reset
            await this.writer.write(encoder.encode("\x04"));
            await new Promise((resolve) => setTimeout(resolve, 500));

            return {
                success: true,
                message: "Device soft reset",
            };
        } catch (error) {
            return {
                success: false,
                message: "Error during soft reset: " + error.message,
            };
        }
    }
}

// Export a singleton instance
export const serialConnection = new SerialConnection();
