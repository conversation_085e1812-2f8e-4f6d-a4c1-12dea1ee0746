import { Box, Button, CircularProgress, Di<PERSON>r, IconButton, Paper, TextField, Tooltip, Typography } from "@mui/material";
import React, { useEffect } from "react";
import BeeAppBar from "./BeeAppBar";
import { grey } from "@mui/material/colors";
import Editor from "@monaco-editor/react";
import "xterm/css/xterm.css";
import { serialConnection } from "../Common/Connection";
import DeleteIcon from "@mui/icons-material/Delete";
import AddIcon from "@mui/icons-material/Add";
import PlayArrowIcon from "@mui/icons-material/PlayArrow";
import Loading from "../../Common/Loading";
import { useDocumentTitle } from "../../../hooks/useDocumentTitle";

const createNewCell = (id, initialValues = {}) => ({
    id,
    title: initialValues.title || "",
    code: initialValues.code || "",
    output: "",
    isExecuting: false,
});

function BeeJupyter({ user, setUser }) {
    const terminalRef = React.useRef(null);
    const [message, setMessage] = React.useState("");
    const [pyodide, setPyodide] = React.useState(null);
    const [loading, setLoading] = React.useState(true);
    const [cells, setCells] = React.useState([createNewCell(1)]);
    const [editingTitleId, setEditingTitleId] = React.useState(null);
    const nextCellId = React.useRef(2);
    const [uploading, setUploading] = React.useState(false);
    const [serialMonitorInterval, setSerialMonitorInterval] = React.useState(null);

    const [platform, setPlatform] = React.useState("python");

    // Sử dụng custom hook
    useDocumentTitle(platform === "python" ? "BeE Jupyter | Python" : "BeE Jupyter | MicroPython");

    // Initialize Pyodide when component mounts
    useEffect(() => {
        async function initPyodide() {
            try {
                setLoading(true);
                // Sử dụng script tag để load Pyodide
                const script = document.createElement("script");
                script.src = "/assets/pyodide/pyodide.js";
                script.type = "text/javascript";

                await new Promise((resolve, reject) => {
                    script.onload = resolve;
                    script.onerror = reject;
                    document.head.appendChild(script);
                });

                // Sau khi script được load, sử dụng global loadPyodide
                const pyodideInstance = await window.loadPyodide({
                    indexURL: "/assets/pyodide/",
                });

                setPyodide(pyodideInstance);
                setMessage("Pyodide loaded successfully");
            } catch (error) {
                console.error("Error loading Pyodide:", error);
                setMessage("Failed to initialize Python environment");
            } finally {
                setLoading(false);
            }
        }
        initPyodide();
    }, []);

    // Load project data from localStorage
    const [project, setProject] = React.useState(() => {
        const savedProject = localStorage.getItem("BeeJupyterProject");
        const defaultProject = {
            name: "main",
            serialConnected: false,
            port: null,
            writer: null,
            reader: null,
        };

        if (savedProject) {
            const loadedProject = JSON.parse(savedProject);
            return {
                ...defaultProject,
                name: loadedProject.name,
            };
        }
        return defaultProject;
    });

    // Load cells from localStorage
    React.useEffect(() => {
        const savedCells = localStorage.getItem("BeeJupyterCells");
        if (savedCells) {
            const loadedCells = JSON.parse(savedCells);
            setCells(loadedCells);
            // Update nextCellId to be greater than the highest cell id
            nextCellId.current = Math.max(...loadedCells.map((cell) => cell.id)) + 1;
        }
    }, []);

    // Save cells to localStorage whenever they change
    useEffect(() => {
        localStorage.setItem("BeeJupyterCells", JSON.stringify(cells));
    }, [cells]);

    // Save project data to localStorage
    useEffect(() => {
        localStorage.setItem(
            "BeeJupyterProject",
            JSON.stringify({
                name: project.name,
            })
        );
    }, [project.name]);

    // Sửa đổi useEffect để theo dõi thay đổi trạng thái kết nối
    useEffect(() => {
        // Nếu kết nối được thiết lập, bắt đầu giám sát
        if (project.serialConnected && project.port) {
            const intervalId = setupSerialDisconnectMonitor();
            setSerialMonitorInterval(intervalId);
        } else {
            // Nếu ngắt kết nối, dừng giám sát
            if (serialMonitorInterval) {
                clearInterval(serialMonitorInterval);
                setSerialMonitorInterval(null);
            }
        }

        // Cleanup khi component unmount
        return () => {
            if (serialMonitorInterval) {
                clearInterval(serialMonitorInterval);
            }
        };
    }, [project.serialConnected, project.port]);

    const handleConnectSerial = async () => {
        const result = await serialConnection.connect();

        if (result.connected && terminalRef.current) {
            // Set up reader loop for terminal
            const readLoop = async () => {
                while (true) {
                    try {
                        const { value, done } = await serialConnection.reader.read();
                        if (done) break;
                        terminalRef.current.write(new TextDecoder().decode(value));
                    } catch (error) {
                        console.error("Error reading from serial:", error);
                        break;
                    }
                }
            };
            readLoop();
        }
        if (!result.connected) {
            setPlatform("python");
            return;
        }

        setProject((prev) => ({
            ...prev,
            serialConnected: result.connected,
            port: serialConnection.port,
            writer: serialConnection.writer,
            reader: serialConnection.reader,
        }));

        if (result.message) {
            if (terminalRef.current) {
                terminalRef.current.writeln(result.message);
            }
        }
        setMessage(result.message);
    };

    const handleUploadCode = async (cellId) => {
        // Kiểm tra nếu đang upload thì không cho upload nữa
        if (uploading) {
            setMessage("Upload is already in progress...");
            return;
        }

        if (!project.serialConnected) {
            alert("Please connect to the board first!");
            return;
        }

        try {
            setUploading(true);
            setMessage("Uploading code...");

            setCells((prev) =>
                prev.map((cell) => (cell.id === cellId ? { ...cell, isExecuting: true, output: "" } : cell))
            );

            const cell = cells.find((c) => c.id === cellId);
            if (!cell?.code.trim()) {
                setCells((prev) =>
                    prev.map((cell) =>
                        cell.id === cellId
                            ? {
                                ...cell,
                                output: "No code to execute",
                                isExecuting: false,
                            }
                            : cell
                    )
                );
                return;
            }

            const result = await serialConnection.uploadCode(cell.code.trim());

            setCells((prev) =>
                prev.map((cell) =>
                    cell.id === cellId
                        ? {
                            ...cell,
                            output: result.message.trim().replace(/\r\n/g, "<br/>") || "<p>No output<p>",
                            isExecuting: false,
                        }
                        : cell
                )
            );

            setMessage("Done uploading code");

            // Restart the read loop after upload
            if (result.success && serialConnection.reader) {
                const readLoop = async () => {
                    while (true) {
                        try {
                            const { value, done } = await serialConnection.reader.read();
                            if (done) break;
                            if (terminalRef.current) {
                                terminalRef.current.write(new TextDecoder().decode(value));
                            }
                        } catch (error) {
                            console.error("Error reading from serial:", error);
                            setMessage("Error: Cannot read from serial");
                            break;
                        }
                    }
                };
                readLoop();
            }
        } catch (error) {
            console.error("Upload error:", error);
            setMessage("Upload failed: " + error.message);
            setCells((prev) =>
                prev.map((cell) => (cell.id === cellId ? { ...cell, isExecuting: false, output: "Upload failed: " + error.message } : cell))
            );
        } finally {
            setUploading(false);
        }
    };

    // Hàm để monitor serial disconnect
    const setupSerialDisconnectMonitor = () => {
        const checkConnection = async () => {
            try {
                if (!navigator.serial) return;

                const ports = await navigator.serial.getPorts();
                const portExists = ports.some((p) => p === project.port);

                if (!portExists && project.serialConnected) {
                    // Đóng kết nối hiện tại
                    try {
                        // Giải phóng writer nếu đang có
                        if (project.writer) {
                            await project.writer.close();
                        }

                        // Giải phóng reader nếu đang có
                        if (project.reader) {
                            await project.reader.cancel();
                            await project.reader.releaseLock();
                        }

                        // Đóng port
                        if (project.port) {
                            await project.port.close();
                        }
                    } catch (error) {
                        console.warn("Error closing serial connection:", error);
                    }

                    // Cập nhật trạng thái
                    setProject((prev) => ({
                        ...prev,
                        serialConnected: false,
                        port: null,
                        writer: null,
                        reader: null,
                    }));

                    setMessage("Serial disconnected");
                    if (terminalRef.current) {
                        terminalRef.current.writeln("\r\nSerial disconnected");
                    }
                }
            } catch (error) {
                console.error("Error checking port status:", error);
            }
        };

        // Kiểm tra mỗi 2 giây
        const intervalId = setInterval(checkConnection, 2000);
        return intervalId;
    };

    const handleSave = () => {
        // Combine all cells' content
        const fileContent = cells
            .map((cell, index) => {
                const titleComment = cell.title ? `## ${cell.title}` : `##`;
                return titleComment ? `${titleComment}\n${cell.code}` : cell.code;
            })
            .join("\n\n");

        // Create blob and download link
        const blob = new Blob([fileContent], { type: "text/x-python" });
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = `${project.name || "jupyter_notebook"}.py`;

        // Trigger download
        document.body.appendChild(link);
        link.click();

        // Cleanup
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
        setMessage("Project saved successfully");
    };

    const handleLoad = (e) => {
        const file = e.target.files[0];
        if (!file) return;

        // Update project name from filename (remove .py extension)
        const projectName = file.name.replace(/\.py$/, "");
        setProject((prev) => ({
            ...prev,
            name: projectName,
        }));

        const reader = new FileReader();
        reader.onload = (e) => {
            const content = e.target.result;

            // Split content by '##' comments
            const cellContents = content.split(/(?=^##)/m);

            // Process each cell
            const newCells = cellContents
                .filter((cellContent) => cellContent.trim()) // Remove empty cells
                .map((cellContent, index) => {
                    const lines = cellContent.trim().split("\n");
                    let title = "";
                    let code = "";

                    // Check if first line is a title comment
                    if (lines[0].startsWith("##")) {
                        title = lines[0].substring(2).trim();
                        code = lines.slice(1).join("\n").trim();
                    } else {
                        code = cellContent.trim();
                    }

                    return createNewCell(nextCellId.current + index, {
                        title: title,
                        code: code,
                    });
                });

            // Update nextCellId
            nextCellId.current += newCells.length;

            // Set the new cells
            setCells(newCells);
        };

        reader.readAsText(file);
        setMessage("Project loaded successfully");
    };

    // const editorRef = React.useRef(null);

    // function handleEditorDidMount(editor, monaco) {
    // 	editorRef.current = editor;

    // 	// Register Python completions
    // 	monaco.languages.registerCompletionItemProvider('python', {
    // 		provideCompletionItems: (model, position) => {
    // 			return {
    // 				suggestions: pythonSuggestions.map(suggestion => ({
    // 					...suggestion,
    // 					range: {
    // 						startLineNumber: position.lineNumber,
    // 						endLineNumber: position.lineNumber,
    // 						startColumn: position.column,
    // 						endColumn: position.column
    // 					}
    // 				}))
    // 			};
    // 		}
    // 	});

    // 	// Add custom Python keywords highlighting
    // 	monaco.languages.setMonarchTokensProvider('python', {
    // 		keywords: [
    // 			'False', 'None', 'True', 'and', 'as', 'assert', 'async', 'await',
    // 			'break', 'class', 'continue', 'def', 'del', 'elif', 'else',
    // 			'except', 'finally', 'for', 'from', 'global', 'if', 'import',
    // 			'in', 'is', 'lambda', 'nonlocal', 'not', 'or', 'pass', 'raise',
    // 			'return', 'try', 'while', 'with', 'yield'
    // 		],
    // 		tokenizer: {
    // 			root: [
    // 				[/[a-zA-Z_]\w*/, {
    // 					cases: {
    // 						'@keywords': 'keyword',
    // 						'@default': 'identifier'
    // 					}
    // 				}],
    // 				// Add more tokenizer rules as needed
    // 			]
    // 		}
    // 	});
    // }

    const addNewCell = () => {
        setCells((prev) => [...prev, createNewCell(nextCellId.current)]);
        nextCellId.current += 1;
    };

    const deleteCell = (cellId) => {
        setCells((prev) => prev.filter((cell) => cell.id !== cellId));
    };

    const updateCellTitle = (cellId, newTitle) => {
        setCells((prev) => prev.map((cell) => (cell.id === cellId ? { ...cell, title: newTitle } : cell)));
    };

    const updateCellCode = (cellId, newCode) => {
        setCells((prev) => prev.map((cell) => (cell.id === cellId ? { ...cell, code: newCode } : cell)));
    };

    const installPackage = async (packageName) => {
        if (!pyodide) return;
        setMessage(`Installing ${packageName}...`);
        try {
            // Load micropip if not already loaded
            await pyodide.loadPackage("micropip");

            // Install the requested package
            await pyodide.runPythonAsync(`
				import micropip
				await micropip.install('${packageName}')
			`);

            setMessage(`Successfully installed ${packageName}`);
            return true;
        } catch (error) {
            console.error(`Failed to install ${packageName}:`, error);
            setMessage(`Failed to install ${packageName}`);
            return false;
        }
    };

    const executeCell = async (cellId) => {
        if (!pyodide) return;

        setMessage("Executing cell...");
        setCells((prev) =>
            prev.map((cell) => (cell.id === cellId ? { ...cell, isExecuting: true, output: "" } : cell))
        );

        try {
            const cell = cells.find((c) => c.id === cellId);
            if (!cell?.code.trim()) {
                setCells((prev) =>
                    prev.map((cell) =>
                        cell.id === cellId
                            ? {
                                ...cell,
                                output: "No code to execute",
                                isExecuting: false,
                            }
                            : cell
                    )
                );
                return;
            }

            // Check for pip install commands
            if (cell.code.includes("pip install")) {
                const matches = cell.code.match(/pip install\s+([a-zA-Z0-9_-]+)/);
                if (matches && matches[1]) {
                    const packageName = matches[1];
                    await installPackage(packageName);
                    setCells((prev) =>
                        prev.map((c) =>
                            c.id === cellId
                                ? {
                                    ...c,
                                    output: `Successfully installed ${packageName}`,
                                    isExecuting: false,
                                }
                                : c
                        )
                    );
                    return;
                }
            }

            await pyodide.runPythonAsync(`
				import sys
				import io
				if not isinstance(sys.stdout, io.StringIO):
					sys.stdout = io.StringIO()

				def capture_outputs():
					output_text = sys.stdout.getvalue()
					sys.stdout.seek(0)
					sys.stdout.truncate(0)

					# Capture the last expression's value
					try:
						last_value = _  # _ holds the last expression's value in our context
						if last_value is not None:
							output_text += str(last_value)
					except NameError:
						pass

					image_data = None
					
					# Only check for images if the last executed code actually generated one
					if any('plt.' in line or 'PIL' in line for line in _last_executed_code.split('\\n')):
						# Check for matplotlib figures
						try:
							import matplotlib.pyplot as plt
							if plt.get_fignums():
								buf = io.BytesIO()
								plt.savefig(buf, format='png', bbox_inches='tight')
								buf.seek(0)
								import base64
								image_data = base64.b64encode(buf.getvalue()).decode('utf-8')
								plt.close('all')
								return {
									'text': output_text,
									'image': image_data
								}
						except Exception as e:
							print(f"Matplotlib Error: {str(e)}")
							pass

						# Check for PIL images
						try:
							from PIL import Image
							for var_name, var_value in globals().items():
								if isinstance(var_value, Image.Image):
									buf = io.BytesIO()
									var_value.save(buf, format='PNG')
									buf.seek(0)
									import base64
									image_data = base64.b64encode(buf.getvalue()).decode('utf-8')
									break
						except Exception as e:
							print(f"PIL error: {str(e)}")
							pass

					return {
						'text': output_text,
						'image': image_data
					}

				_last_executed_code = ""  # Initialize global variable to store last executed code
			`);

            // Store the code being executed
            await pyodide.runPythonAsync(
                `_last_executed_code = ${JSON.stringify(cell.code.replace("plt.show()", ""))}`
            );

            // Split the code into lines
            const lines = cell.code.replace("plt.show()", "").trim().split("\n");

            let lastLine = "";
            if (lines.length > 0) {
                lastLine = lines[lines.length - 1];
            }

            if (lastLine.trimEnd().startsWith("  ")) {
                // Execute the entire code block if the last line is indented (while, for, if, function...)
                await pyodide.runPythonAsync(cell.code.replace("plt.show()", "").trim());
            } else {
                // Execute all lines except the last one
                if (lines.length > 1) {
                    await pyodide.runPythonAsync(lines.slice(0, -1).join("\n"));
                }

                // Execute the last line and store its result
                if (lines.length > 0) {
                    await pyodide.runPythonAsync(`_ = ${lastLine}`);
                }
            }

            // Capture outputs
            const response = await pyodide.runPythonAsync(`
				import json
				json.dumps(capture_outputs())
			`);

            let parsedResponse = JSON.parse(response);

            let finalOutput = "";

            // Add text output if present
            if (parsedResponse.text && parsedResponse.text.trim()) {
                finalOutput += `<p>${parsedResponse.text.trim()}</p>`;
            }

            // Add image output if present
            if (parsedResponse.image) {
                // If there was text output, add a newline before the image
                if (finalOutput) {
                    finalOutput += "<br/>";
                }
                // Use HTML img tag directly instead of Markdown
                finalOutput += `<img src="data:image/png;base64,${parsedResponse.image}" alt="Output" style="max-width: 100%; height: auto;" />`;
            }

            setCells((prev) =>
                prev.map((cell) =>
                    cell.id === cellId
                        ? {
                            ...cell,
                            output: finalOutput || "<p>No output<p>",
                            isExecuting: false,
                        }
                        : cell
                )
            );

            setMessage("Cell executed successfully");
        } catch (error) {
            setCells((prev) =>
                prev.map((cell) =>
                    cell.id === cellId
                        ? {
                            ...cell,
                            output: `Error: ${error.message}`,
                            isExecuting: false,
                        }
                        : cell
                )
            );
            setMessage(`Error executing cell`);
        }
    };

    const calculateEditorHeight = (code) => {
        const lineCount = (code.match(/\n/g) || []).length + 1;
        const lineHeight = 19; // Monaco editor default line height
        const padding = 10; // Extra padding
        return Math.min(Math.max(lineCount * lineHeight + padding, 50), 1000); // Min 50px, Max 1000px
    };

    // Add this function to handle adding a cell before the current cell
    const addCellBefore = (currentCellId) => {
        setCells((prev) => {
            const index = prev.findIndex((cell) => cell.id === currentCellId);
            const newCell = createNewCell(nextCellId.current);
            nextCellId.current += 1;
            return [...prev.slice(0, index), newCell, ...prev.slice(index)];
        });
    };

    return (
        <Box
            sx={{
                minHeight: "100vh",
                display: "flex",
                flexDirection: "column",
                backgroundColor: "white",
            }}
        >
            <BeeAppBar
                user={user}
                setUser={setUser}
                project={project}
                setProject={setProject}
                handleConnectSerial={handleConnectSerial}
                handleSave={handleSave}
                handleLoad={handleLoad}
                platform={platform}
                setPlatform={setPlatform}
            />
            <Box
                sx={{
                    display: "flex",
                    flexDirection: "column",
                    flexGrow: 1,
                    mt: "64px",
                    overflow: "hidden",
                }}
            >
                <Box
                    sx={{
                        display: "flex",
                        flexGrow: 1,
                        minHeight: 0,
                        justifyContent: "center",
                    }}
                >
                    {loading ? (
                        <Loading />
                    ) : (
                        <Box
                            sx={{
                                flexGrow: 1,
                                flexShrink: 1, // Allow shrinking
                                minWidth: 100, // Prevent it from disappearing completely
                                minHeight: 0,
                                m: 1,
                                position: "relative",
                                overflowY: "auto",
                                maxWidth: "1200px",
                            }}
                        >
                            {cells.map((cell) => (
                                <React.Fragment key={cell.id}>
                                    <Box
                                        sx={{
                                            display: "flex",
                                            justifyContent: "center",
                                            my: 1,
                                            opacity: 0,
                                            transition: "opacity 0.2s",
                                            height: "10px",
                                            "&:hover": {
                                                opacity: 1,
                                                height: "50px",
                                            },
                                        }}
                                    >
                                        <Button
                                            size="small"
                                            startIcon={<AddIcon />}
                                            onClick={() => addCellBefore(cell.id)}
                                            sx={{
                                                minWidth: "120px",
                                                borderRadius: 2,
                                                backgroundColor: "rgba(255, 255, 255, 0.9)",
                                                "&:hover": {
                                                    backgroundColor: "rgba(255, 255, 255, 1)",
                                                },
                                            }}
                                        >
                                            Add Cell
                                        </Button>
                                    </Box>
                                    <Paper
                                        key={cell.id}
                                        elevation={2}
                                        sx={{
                                            p: 2,
                                            mr: 2,
                                            ml: 2,
                                            borderRadius: "20px",
                                        }}
                                    >
                                        <Box
                                            sx={{
                                                display: "flex",
                                                alignItems: "center",
                                                mb: 2,
                                                gap: 2,
                                            }}
                                        >
                                            {editingTitleId === cell.id ? (
                                                <TextField
                                                    placeholder="Enter cell title/comment"
                                                    value={cell.title}
                                                    onChange={(e) => updateCellTitle(cell.id, e.target.value)}
                                                    onBlur={() => setEditingTitleId(null)}
                                                    onKeyPress={(e) => {
                                                        if (e.key === "Enter") {
                                                            setEditingTitleId(null);
                                                        }
                                                    }}
                                                    autoFocus
                                                    fullWidth
                                                    variant="outlined"
                                                    size="small"
                                                />
                                            ) : (
                                                <Typography
                                                    onClick={() => setEditingTitleId(cell.id)}
                                                    sx={{
                                                        flexGrow: 1,
                                                        cursor: "pointer",
                                                        p: 1,
                                                        color: grey[500],
                                                        "&:hover": {
                                                            backgroundColor: "rgba(0, 0, 0, 0.04)",
                                                            borderRadius: 1,
                                                        },
                                                        // color: cell.title ? 'text.primary' : 'text.secondary',
                                                        fontStyle: cell.title ? "normal" : "italic",
                                                    }}
                                                >
                                                    <i>{cell.title || "Click to add title/comment"}</i>
                                                </Typography>
                                            )}
                                            <IconButton
                                                onClick={() =>
                                                    platform === "python"
                                                        ? executeCell(cell.id)
                                                        : handleUploadCode(cell.id)
                                                }
                                                disabled={cell.isExecuting || (platform === "micropython" && uploading)}
                                                color="primary"
                                                sx={{ flexShrink: 0 }}
                                            >
                                                {platform === "micropython" && uploading ? (
                                                    <CircularProgress size={20} />
                                                ) : (
                                                    <PlayArrowIcon />
                                                )}
                                            </IconButton>
                                            <IconButton
                                                onClick={() => deleteCell(cell.id)}
                                                color="error"
                                                sx={{ flexShrink: 0 }}
                                            >
                                                <DeleteIcon />
                                            </IconButton>
                                        </Box>

                                        <Editor
                                            height={calculateEditorHeight(cell.code)}
                                            defaultLanguage="python"
                                            value={cell.code}
                                            onChange={(value) => updateCellCode(cell.id, value)}
                                            options={{
                                                minimap: { enabled: false },
                                                scrollBeyondLastLine: false,
                                                fontSize: 14,
                                                lineNumbers: "off",
                                            }}
                                        />

                                        {cell.output && (
                                            <Box
                                                sx={{
                                                    mt: 2,
                                                    p: 2,
                                                    backgroundColor: "#f5f5f5",
                                                    borderRadius: "10px",
                                                    fontFamily: "monospace",
                                                }}
                                            >
                                                <Typography
                                                    variant="body2"
                                                    sx={{
                                                        mb: 1,
                                                        fontWeight: "bold",
                                                    }}
                                                >
                                                    Output:
                                                </Typography>
                                                <Box
                                                    dangerouslySetInnerHTML={{
                                                        __html: cell.output,
                                                    }}
                                                />
                                            </Box>
                                        )}
                                    </Paper>
                                </React.Fragment>
                            ))}
                            <Box
                                sx={{
                                    display: "flex",
                                    justifyContent: "center",
                                    mt: 2,
                                }}
                            >
                                <Button
                                    startIcon={<AddIcon />}
                                    onClick={addNewCell}
                                    variant="outlined"
                                    sx={{
                                        alignSelf: "flex-start",
                                        ml: 2,
                                        borderRadius: 2,
                                    }}
                                >
                                    Add Cell
                                </Button>
                            </Box>
                        </Box>
                    )}
                </Box>
                <Box
                    sx={{
                        height: "20px",
                        borderTop: "1px solid #ddd",
                        display: "flex",
                        alignItems: "center",
                        justifyItems: "center",
                        px: 2,
                        backgroundColor: "#f5f5f5",
                    }}
                >
                    <Typography variant="body2" sx={{ color: grey[500], fontSize: "12px" }}>
                        Mode: {platform === "python" ? "Python" : "MicroPython"}
                    </Typography>
                    <Divider orientation="vertical" sx={{ mr: "10px", ml: "10px" }} flexItem />
                    <Typography variant="body2" sx={{ color: grey[500], fontSize: "12px" }}>
                        Trạng thái: {project.serialConnected ? (uploading ? "Uploading..." : "Connected") : "Disconnected"}
                    </Typography>
                    <Divider orientation="vertical" sx={{ mr: "10px", ml: "10px" }} flexItem />
                    <Typography variant="body2" sx={{ color: grey[500], fontSize: "12px" }}>
                        Log: {message}
                    </Typography>
                </Box>
            </Box>
        </Box>
    );
}

export default BeeJupyter;
