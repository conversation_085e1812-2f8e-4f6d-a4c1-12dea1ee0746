import React, { useEffect, useRef, forwardRef, useImperativeHandle } from "react";
import { Box, Paper, Typography, Alert, CircularProgress, IconButton, Tooltip } from "@mui/material";
import { CameraAlt as CameraIcon, Refresh as RefreshIcon, Fullscreen as FullscreenIcon } from "@mui/icons-material";

// TensorFlow.js imports - these will be loaded dynamically
let mobilenet, cocoSsd, tmImage;

const TensorFlowCamera = forwardRef(
    (
        {
            currentModel,
            modelUrl,
            isDetecting,
            setIsDetecting,
            isModelLoaded,
            setIsModelLoaded,
            predictions,
            setPredictions,
            detectionSettings,
            cameraError,
            setCameraError,
            modelError,
            setModelError,
            mqttClientRef,
            userIP,
        },
        ref
    ) => {
        const videoRef = useRef(null);
        const canvasRef = useRef(null);
        const streamRef = useRef(null);
        const modelRef = useRef(null);
        const detectionIntervalRef = useRef(null);

        useImperativeHandle(ref, () => ({
            video: videoRef.current,
            canvas: canvasRef.current,
        }));

        // Load TensorFlow.js and models dynamically
        useEffect(() => {
            const loadTensorFlow = async () => {
                try {
                    // Load TensorFlow.js
                    if (!window.tf) {
                        const script = document.createElement("script");
                        script.src = "https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@latest/dist/tf.min.js";
                        document.head.appendChild(script);
                        await new Promise((resolve) => {
                            script.onload = resolve;
                        });
                        tf = window.tf;
                    }

                    // Load model-specific libraries
                    if (currentModel === "mobilenet" && !window.mobilenet) {
                        const script = document.createElement("script");
                        script.src =
                            "https://cdn.jsdelivr.net/npm/@tensorflow-models/mobilenet@2.1.1/dist/mobilenet.min.js";
                        document.head.appendChild(script);
                        await new Promise((resolve) => {
                            script.onload = resolve;
                        });
                        mobilenet = window.mobilenet;
                    } else if (currentModel === "cocossd" && !window.cocoSsd) {
                        const script = document.createElement("script");
                        script.src =
                            "https://cdn.jsdelivr.net/npm/@tensorflow-models/coco-ssd@2.2.2/dist/coco-ssd.min.js";
                        document.head.appendChild(script);
                        await new Promise((resolve) => {
                            script.onload = resolve;
                        });
                        cocoSsd = window.cocoSsd;
                    } else if (currentModel === "teachable" && !window.tmImage) {
                        const script = document.createElement("script");
                        script.src =
                            "https://cdn.jsdelivr.net/npm/@teachablemachine/image@latest/dist/teachablemachine-image.min.js";
                        document.head.appendChild(script);
                        await new Promise((resolve) => {
                            script.onload = resolve;
                        });
                        tmImage = window.tmImage;
                    }

                    await loadModel();
                } catch (error) {
                    console.error("Error loading TensorFlow.js:", error);
                    setModelError("Failed to load TensorFlow.js libraries");
                }
            };

            loadTensorFlow();
        }, [currentModel, modelUrl]);

        // Load the selected model
        const loadModel = async () => {
            try {
                setModelError(null);
                setIsModelLoaded(false);

                if (currentModel === "mobilenet") {
                    modelRef.current = await mobilenet.load();
                } else if (currentModel === "cocossd") {
                    modelRef.current = await cocoSsd.load();
                } else if (currentModel === "teachable" && modelUrl) {
                    // Teachable Machine URLs có format: https://teachablemachine.withgoogle.com/models/MODEL_ID/
                    // Cần xử lý đúng path cho model.json và metadata.json
                    let baseUrl = modelUrl.trim();

                    // Nếu URL không kết thúc bằng /, thêm vào
                    if (!baseUrl.endsWith("/")) {
                        baseUrl += "/";
                    }

                    const modelURL = baseUrl + "model.json";
                    const metadataURL = baseUrl + "metadata.json";

                    try {
                        // Kiểm tra xem URLs có accessible không trước khi load
                        const modelResponse = await fetch(modelURL, { method: "HEAD" });
                        const metadataResponse = await fetch(metadataURL, { method: "HEAD" });

                        if (!modelResponse.ok) {
                            throw new Error(
                                `Model file not accessible: ${modelResponse.status} ${modelResponse.statusText}`
                            );
                        }
                        if (!metadataResponse.ok) {
                            throw new Error(
                                `Metadata file not accessible: ${metadataResponse.status} ${metadataResponse.statusText}`
                            );
                        }

                        modelRef.current = await tmImage.load(modelURL, metadataURL);
                    } catch (fetchError) {
                        console.error("Error accessing model files:", fetchError);
                        throw new Error(
                            `Cannot access Teachable Machine model files. Please check the URL: ${fetchError.message}`
                        );
                    }
                }

                if (modelRef.current) {
                    setIsModelLoaded(true);
                }
            } catch (error) {
                console.error("Error loading model:", error);
                setModelError(`Failed to load ${currentModel} model: ${error.message}`);
            }
        };

        // Initialize camera
        useEffect(() => {
            const initCamera = async () => {
                try {
                    setCameraError(null);

                    if (streamRef.current) {
                        streamRef.current.getTracks().forEach((track) => track.stop());
                    }

                    const stream = await navigator.mediaDevices.getUserMedia({
                        video: {
                            width: { ideal: 640 },
                            height: { ideal: 480 },
                            facingMode: "environment",
                        },
                    });

                    if (videoRef.current) {
                        videoRef.current.srcObject = stream;
                        streamRef.current = stream;
                    }
                } catch (error) {
                    console.error("Error accessing camera:", error);
                    setCameraError("Failed to access camera. Please check permissions.");
                }
            };

            initCamera();

            return () => {
                if (streamRef.current) {
                    streamRef.current.getTracks().forEach((track) => track.stop());
                }
            };
        }, []);

        // Detection loop
        useEffect(() => {
            if (isDetecting && isModelLoaded && modelRef.current) {
                const detect = async () => {
                    try {
                        if (videoRef.current && videoRef.current.readyState === 4) {
                            let predictions = [];

                            if (currentModel === "mobilenet") {
                                predictions = await modelRef.current.classify(videoRef.current);
                            } else if (currentModel === "cocossd") {
                                predictions = await modelRef.current.detect(videoRef.current);
                            } else if (currentModel === "teachable") {
                                predictions = await modelRef.current.predict(videoRef.current);
                            }

                            // Filter predictions by confidence threshold
                            const filteredPredictions = predictions
                                .filter(
                                    (pred) => (pred.probability || pred.score) >= detectionSettings.confidenceThreshold
                                )
                                .slice(0, detectionSettings.maxPredictions);

                            setPredictions(filteredPredictions);

                            // Draw bounding boxes if enabled
                            if (detectionSettings.showBoundingBoxes && canvasRef.current) {
                                drawPredictions(filteredPredictions);
                            }

                            // Send to MQTT if enabled
                            if (
                                detectionSettings.enableMQTT &&
                                mqttClientRef.current &&
                                filteredPredictions.length > 0
                            ) {
                                const mqttData = {
                                    timestamp: new Date().toISOString(),
                                    model: currentModel,
                                    predictions: filteredPredictions.map((pred) => ({
                                        class: pred.class || pred.className,
                                        confidence: pred.probability || pred.score,
                                        bbox: pred.bbox || null,
                                        position: pred.bbox
                                            ? {
                                                x: pred.bbox[0] + pred.bbox[2] / 2,
                                                y: pred.bbox[1] + pred.bbox[3] / 2,
                                            }
                                            : null,
                                    })),
                                    ip: userIP,
                                };

                                mqttClientRef.current.publish(`bee/AI/${userIP}/`, JSON.stringify(mqttData));
                            }
                        }
                    } catch (error) {
                        console.error("Detection error:", error);
                    }
                };

                detectionIntervalRef.current = setInterval(detect, detectionSettings.detectionInterval);
            } else {
                if (detectionIntervalRef.current) {
                    clearInterval(detectionIntervalRef.current);
                }
            }

            return () => {
                if (detectionIntervalRef.current) {
                    clearInterval(detectionIntervalRef.current);
                }
            };
        }, [isDetecting, isModelLoaded, detectionSettings, currentModel, userIP]);

        // Draw predictions on canvas
        const drawPredictions = (predictions) => {
            const canvas = canvasRef.current;
            const video = videoRef.current;

            if (!canvas || !video) return;

            const ctx = canvas.getContext("2d");
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            predictions.forEach((prediction) => {
                if (prediction.bbox) {
                    const [x, y, width, height] = prediction.bbox;

                    // Draw bounding box
                    ctx.strokeStyle = "#00ff00";
                    ctx.lineWidth = 2;
                    ctx.strokeRect(x, y, width, height);

                    // Draw label background
                    const label = `${prediction.class || prediction.className}: ${Math.round((prediction.probability || prediction.score) * 100)}%`;
                    ctx.fillStyle = "rgba(0, 255, 0, 0.8)";
                    ctx.fillRect(x, y - 25, ctx.measureText(label).width + 10, 25);

                    // Draw label text
                    ctx.fillStyle = "black";
                    ctx.font = "16px Arial";
                    ctx.fillText(label, x + 5, y - 5);
                }
            });
        };

        const refreshCamera = () => {
            if (streamRef.current) {
                streamRef.current.getTracks().forEach((track) => track.stop());
            }
            window.location.reload();
        };

        return (
            <Paper
                elevation={12}
                sx={{
                    p: 3,
                    background: "rgba(255, 255, 255, 0.05)",
                    backdropFilter: "blur(10px)",
                    borderRadius: "16px",
                    border: "1px solid rgba(255, 255, 255, 0.1)",
                    position: "relative",
                }}
            >
                <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 2 }}>
                    <Typography variant="h6" sx={{ color: "white" }}>
                        <CameraIcon sx={{ mr: 1, verticalAlign: "middle" }} />
                        AI Camera Feed
                    </Typography>
                    <Box>
                        <Tooltip title="Refresh Camera">
                            <IconButton onClick={refreshCamera} sx={{ color: "white" }}>
                                <RefreshIcon />
                            </IconButton>
                        </Tooltip>
                    </Box>
                </Box>

                {cameraError && (
                    <Alert severity="error" sx={{ mb: 2 }}>
                        {cameraError}
                    </Alert>
                )}

                {modelError && (
                    <Alert severity="error" sx={{ mb: 2 }}>
                        {modelError}
                    </Alert>
                )}

                <Box
                    sx={{
                        position: "relative",
                        width: "100%",
                        height: "auto",
                        borderRadius: "12px",
                        overflow: "hidden",
                        background: "#000",
                    }}
                >
                    <video
                        ref={videoRef}
                        autoPlay
                        playsInline
                        muted
                        style={{
                            width: "100%",
                            height: "auto",
                            display: "block",
                        }}
                    />
                    <canvas
                        ref={canvasRef}
                        style={{
                            position: "absolute",
                            top: 0,
                            left: 0,
                            width: "100%",
                            height: "100%",
                            pointerEvents: "none",
                        }}
                    />

                    {!isModelLoaded && (
                        <Box
                            sx={{
                                position: "absolute",
                                top: "50%",
                                left: "50%",
                                transform: "translate(-50%, -50%)",
                                display: "flex",
                                flexDirection: "column",
                                alignItems: "center",
                                color: "white",
                            }}
                        >
                            <CircularProgress color="primary" sx={{ mb: 2 }} />
                            <Typography>Loading AI Model...</Typography>
                        </Box>
                    )}
                </Box>
            </Paper>
        );
    }
);

TensorFlowCamera.displayName = "TensorFlowCamera";

export default TensorFlowCamera;
