import React, { useState, useEffect, forwardRef, useImperativeHandle } from "react";
import {
    Box,
    Paper,
    Typography,
    TextField,
    Button,
    Alert,
    Chip,
    Divider,
    FormControlLabel,
    Switch,
    Accordion,
    AccordionSummary,
    AccordionDetails,
    List,
    ListItem,
    ListItemText,
    IconButton,
    Tooltip,
} from "@mui/material";
import RadarIcon from '@mui/icons-material/Radar';
import {
    Wifi as WifiIcon,
    WifiOff as WifiOffIcon,
    Send as SendIcon,
    Settings as SettingsIcon,
    ExpandMore as ExpandMoreIcon,
    ContentCopy as CopyIcon,
    Refresh as RefreshIcon,
} from "@mui/icons-material";
import mqtt from "mqtt";

const MQTTClient = forwardRef(({ userIP, setUserIP, mqttConnected, setMqttConnected, enabled }, ref) => {
    const [client, setClient] = useState(null);
    const [connectionStatus, setConnectionStatus] = useState("disconnected");
    const [lastMessage, setLastMessage] = useState(null);
    const [messageHistory, setMessageHistory] = useState([]);
    const [settingsExpanded, setSettingsExpanded] = useState(false);
    const [connectionError, setConnectionError] = useState(null);

    // MQTT Settings
    const [mqttSettings, setMqttSettings] = useState({
        broker: "beeblock.vn",
        port: 8883,
        username: "beeblock",
        password: "",
        useSSL: true,
        topic: `bee/AI/`,
    });

    // Update topic when IP changes
    useEffect(() => {
        setMqttSettings((prev) => ({
            ...prev,
            topic: userIP ? `bee/AI/${userIP}/` : 'bee/AI/',
        }));
    }, [userIP]);

    // Expose publish method to parent
    useImperativeHandle(ref, () => ({
        publish: (topic, message) => {
            if (client && mqttConnected) {
                try {
                    client.publish(topic || mqttSettings.topic, message);
                    const messageData = {
                        topic: topic || mqttSettings.topic,
                        message: message,
                        timestamp: new Date().toISOString(),
                        type: "sent",
                    };
                    setLastMessage(messageData);
                    setMessageHistory((prev) => [messageData, ...prev.slice(0, 9)]); // Keep last 10 messages
                    return true;
                } catch (error) {
                    console.error("MQTT publish error:", error);
                    return false;
                }
            }
            return false;
        },
        isConnected: () => mqttConnected,
    }));

    // Connect to MQTT broker
    const connectMQTT = async () => {
        try {
            setConnectionError(null);
            setConnectionStatus("connecting");

            const brokerUrl = mqttSettings.useSSL
                ? `mqtts://${mqttSettings.broker}:${mqttSettings.port}`
                : `mqtt://${mqttSettings.broker}:${mqttSettings.port}`;

            const options = {
                clientId: `bee_ai_${userIP}_${Date.now()}`,
                username: mqttSettings.username,
                password: mqttSettings.password,
                keepalive: 30,
                clean: true,
                reconnectPeriod: 5000,
                connectTimeout: 10000,
            };

            const mqttClient = mqtt.connect(brokerUrl, options);

            mqttClient.on("connect", () => {
                setConnectionStatus("connected");
                setMqttConnected(true);
                setClient(mqttClient);

                // Subscribe to response topic
                const responseTopic = `bee/AI/${userIP}/response`;
                mqttClient.subscribe(responseTopic, (err) => {
                    if (!err) {
                        console.log(`Subscribed to ${responseTopic}`);
                    }
                });
            });

            mqttClient.on("error", (error) => {
                console.error("MQTT connection error:", error);
                setConnectionError(`Connection failed: ${error.message}`);
                setConnectionStatus("error");
                setMqttConnected(false);
            });

            mqttClient.on("close", () => {
                setConnectionStatus("disconnected");
                setMqttConnected(false);
                setClient(null);
            });

            mqttClient.on("message", (topic, message) => {
                try {
                    const messageData = {
                        topic: topic,
                        message: message.toString(),
                        timestamp: new Date().toISOString(),
                        type: "received",
                    };
                    setLastMessage(messageData);
                    setMessageHistory((prev) => [messageData, ...prev.slice(0, 9)]);
                } catch (error) {
                    console.error("Error processing received message:", error);
                }
            });
            setSettingsExpanded(false);
        } catch (error) {
            console.error("MQTT setup error:", error);
            setConnectionError(`Setup failed: ${error.message}`);
            setConnectionStatus("error");
            setMqttConnected(false);
        }
    };

    // Disconnect from MQTT broker
    const disconnectMQTT = () => {
        if (client) {
            client.end();
            setClient(null);
            setMqttConnected(false);
            setConnectionStatus("disconnected");
        }
    };

    // Handle settings change
    const handleSettingChange = (setting, value) => {
        setMqttSettings((prev) => ({
            ...prev,
            [setting]: value,
        }));
    };

    // Copy topic to clipboard
    const copyTopic = () => {
        navigator.clipboard.writeText(mqttSettings.topic);
    };

    // Get status color
    const getStatusColor = () => {
        switch (connectionStatus) {
            case "connected":
                return "success";
            case "connecting":
                return "warning";
            case "error":
                return "error";
            default:
                return "default";
        }
    };

    // Get status icon
    const getStatusIcon = () => {
        return connectionStatus === "connected" ? <WifiIcon /> : <WifiOffIcon />;
    };

    return (
        <Paper
            elevation={12}
            sx={{
                background: "rgba(255, 255, 255, 0.05)",
                backdropFilter: "blur(10px)",
                borderRadius: "16px",
                border: "1px solid rgba(255, 255, 255, 0.1)",
                overflow: "hidden",
            }}
        >
            {/* Header */}
            <Box sx={{ p: 3, pb: 2 }}>
                <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 2 }}>
                    <Box sx={{ display: "flex", alignItems: "center" }}>
                        <RadarIcon sx={{ mr: 1, verticalAlign: "middle", color: "white" }} />
                        <Typography variant="h6" sx={{ color: "white" }}>
                            MQTT Client
                        </Typography>
                    </Box>
                    <Chip
                        icon={getStatusIcon()}
                        label={connectionStatus.toUpperCase()}
                        color={getStatusColor()}
                        size="small"
                    />
                </Box>

                {/* Connection Controls */}
                <Box sx={{ display: "flex", gap: 1, mb: 2 }}>
                    {!mqttConnected ? (
                        <Button
                            color="error"
                            variant="contained"
                            onClick={connectMQTT}
                            disabled={!enabled || connectionStatus === "connecting"}
                            startIcon={<WifiIcon />}
                            sx={{ borderRadius: "10px" }}
                            size="small"
                        >
                            {connectionStatus === "connecting" ? "Connecting..." : "Connect"}
                        </Button>
                    ) : (
                        <Button
                            color="success"
                            variant="contained"
                            onClick={disconnectMQTT}
                            startIcon={<WifiOffIcon />}
                            size="small"
                            sx={{ borderRadius: "10px" }}
                        >
                            Disconnect
                        </Button>
                    )}
                </Box>

                {/* Connection Error */}
                {connectionError && (
                    <Alert severity="error" sx={{ mb: 2 }}>
                        {connectionError}
                    </Alert>
                )}

                {/* Topic Display */}
                <Box sx={{ display: "flex", alignItems: "center", gap: 1, mb: 2 }}>
                    <Typography variant="caption" sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                        Topic:
                    </Typography>
                    <Typography variant="body2" sx={{ color: "white", fontFamily: "monospace" }}>
                        {mqttSettings.topic}
                    </Typography>
                    <Tooltip title="Copy topic">
                        <IconButton size="small" onClick={copyTopic} sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                            <CopyIcon fontSize="small" />
                        </IconButton>
                    </Tooltip>
                </Box>

                {/* Last Message */}
                {lastMessage && (
                    <Box
                        sx={{
                            p: 2,
                            background: "rgba(255, 255, 255, 0.05)",
                            borderRadius: "8px",
                            border: "1px solid rgba(255, 255, 255, 0.1)",
                        }}
                    >
                        <Typography variant="caption" sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                            Last {lastMessage.type}:
                        </Typography>
                        <Typography
                            variant="body2"
                            sx={{
                                color: "white",
                                fontFamily: "monospace",
                                fontSize: "0.75rem",
                                wordBreak: "break-all",
                            }}
                        >
                            {lastMessage.message.length > 100
                                ? lastMessage.message.substring(0, 100) + "..."
                                : lastMessage.message}
                        </Typography>
                        <Typography variant="caption" sx={{ color: "rgba(255, 255, 255, 0.5)" }}>
                            {new Date(lastMessage.timestamp).toLocaleTimeString()}
                        </Typography>
                    </Box>
                )}
            </Box>

            {/* Settings Panel */}
            <Accordion
                expanded={settingsExpanded}
                onChange={(event, isExpanded) => setSettingsExpanded(isExpanded)}
                sx={{
                    background: "rgba(255, 255, 255, 0.02)",
                    "&:before": { display: "none" },
                    boxShadow: "none",
                }}
            >
                <AccordionSummary
                    expandIcon={<ExpandMoreIcon sx={{ color: "white" }} />}
                    sx={{
                        borderTop: "1px solid rgba(255, 255, 255, 0.1)",
                        "& .MuiAccordionSummary-content": {
                            alignItems: "center",
                        },
                    }}
                >
                    <SettingsIcon sx={{ color: "white", mr: 1 }} />
                    <Typography sx={{ color: "white" }}>MQTT Settings</Typography>
                </AccordionSummary>
                <AccordionDetails sx={{ pt: 0 }}>
                    <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                        <TextField
                            label="Broker"
                            value={mqttSettings.broker}
                            onChange={(e) => handleSettingChange("broker", e.target.value)}
                            size="small"
                            disabled={mqttConnected}
                            sx={{
                                "& .MuiOutlinedInput-root": {
                                    color: "white",
                                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                                },
                                "& .MuiInputLabel-root": { color: "rgba(255, 255, 255, 0.7)" },
                            }}
                        />

                        <TextField
                            label="Port"
                            type="number"
                            value={mqttSettings.port}
                            onChange={(e) => handleSettingChange("port", parseInt(e.target.value))}
                            size="small"
                            disabled={mqttConnected}
                            sx={{
                                "& .MuiOutlinedInput-root": {
                                    color: "white",
                                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                                },
                                "& .MuiInputLabel-root": { color: "rgba(255, 255, 255, 0.7)" },
                            }}
                        />

                        <TextField
                            label="Username"
                            value={mqttSettings.username}
                            onChange={(e) => handleSettingChange("username", e.target.value)}
                            size="small"
                            disabled={mqttConnected}
                            sx={{
                                "& .MuiOutlinedInput-root": {
                                    color: "white",
                                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                                },
                                "& .MuiInputLabel-root": { color: "rgba(255, 255, 255, 0.7)" },
                            }}
                        />

                        <TextField
                            label="Password"
                            type="password"
                            value={mqttSettings.password}
                            onChange={(e) => handleSettingChange("password", e.target.value)}
                            size="small"
                            disabled={mqttConnected}
                            sx={{
                                "& .MuiOutlinedInput-root": {
                                    color: "white",
                                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                                },
                                "& .MuiInputLabel-root": { color: "rgba(255, 255, 255, 0.7)" },
                            }}
                        />
                        <TextField
                            label="BeE's IP"
                            value={userIP}
                            onChange={(e) => setUserIP(e.target.value)}
                            size="small"
                            disabled={mqttConnected}
                            sx={{
                                "& .MuiOutlinedInput-root": {
                                    color: "white",
                                    "& fieldset": { borderColor: "rgba(255, 255, 255, 0.3)" },
                                },
                                "& .MuiInputLabel-root": { color: "rgba(255, 255, 255, 0.7)" },
                            }}
                        />

                        <FormControlLabel
                            control={
                                <Switch
                                    checked={mqttSettings.useSSL}
                                    onChange={(e) => handleSettingChange("useSSL", e.target.checked)}
                                    disabled={mqttConnected}
                                />
                            }
                            label={
                                <Typography variant="body2" sx={{ color: "white" }}>
                                    Use SSL/TLS
                                </Typography>
                            }
                        />
                    </Box>
                </AccordionDetails>
            </Accordion>
        </Paper>
    );
});

MQTTClient.displayName = "MQTTClient";

export default MQTTClient;
