import React, { useState, useEffect } from "react";
import {
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON>ton,
    IconButton,
    Container,
    Box,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
} from "@mui/material";
import {
    SportsEsports as SportsEsportsIcon,
    Wifi as WifiIcon,
    WifiOff as WifiOffIcon,
} from "@mui/icons-material";
import CustomTextField from "../../Common/CustomTextField";
import { useNavigate } from "react-router-dom";

function BeeAppBarMQTT({ project, handleConnectMQTT, handleDisconnectMQTT, message, user }) {
    const navigate = useNavigate();
    const [drawerOpen, setDrawerOpen] = useState(false);
    const [mqttDialogOpen, setMqttDialogOpen] = useState(false);
    const [mqttConfig, setMqttConfig] = useState({
        brokerUrl: user.is_student ? 'beeblock.vn' : '',
        port: user.is_student ? '8883' : '',
        username: user.is_student ? import.meta.env.VITE_MQTT_USER : '',
        password: user.is_student ? import.meta.env.VITE_MQTT_PASS : '',
        topic: user.is_student ? 'bee/gamepad/commands/' : '',
        useAuth: true,
        target_ip: localStorage.getItem('bee_target_ip') || '***********'
    });

    useEffect(() => {
        if (!user.is_student) {
            alert("Please contact admin to get MQTT credentials!");
            navigate("/play");
        }
    }, [user]);

    // Load target_ip từ localStorage khi component mount
    useEffect(() => {
        const savedTargetIp = localStorage.getItem('bee_target_ip');
        if (savedTargetIp) {
            setMqttConfig(prev => ({
                ...prev,
                target_ip: savedTargetIp
            }));
        }
    }, []);

    // Save target_ip vào localStorage khi thay đổi
    useEffect(() => {
        localStorage.setItem('bee_target_ip', mqttConfig.target_ip);
    }, [mqttConfig.target_ip]);

    const handleMqttConnect = () => {
        handleConnectMQTT(mqttConfig);
        setMqttDialogOpen(false);
    };

    const handleMqttConfigChange = (field) => (event) => {
        setMqttConfig(prev => ({
            ...prev,
            [field]: event.target.value
        }));
    };

    return (
        <>
            <AppBar
                position="static"
                sx={{
                    background: "linear-gradient(135deg, #1a237e 0%, #0d47a1 100%)",
                }}
            >
                <Container maxWidth="xl">
                    <Toolbar disableGutters>
                        {/* Logo and title */}
                        <SportsEsportsIcon
                            sx={{ display: 'flex', mr: 1, cursor: 'pointer', fontSize: "3rem" }}
                            onClick={() => navigate("/play")} />
                        <Typography
                            variant="h6"
                            noWrap
                            component="a"
                            href="/"
                            sx={{
                                mr: 2,
                                display: "flex",
                                color: "inherit",
                                textDecoration: "none",
                                flexGrow: 1,
                            }}
                        >
                            BeE Gamepad
                        </Typography>

                        {/* Status message */}
                        {message && (
                            <Typography variant="body2" sx={{ mr: 2, color: "white", display: { xs: "none", md: "block" } }}>
                                {message}
                            </Typography>
                        )}

                        {/* MQTT Connection Button */}
                        <Box sx={{ flexGrow: 0 }}>
                            {project.mqttConnected ? (
                                <Button
                                    variant="contained"
                                    color="success"
                                    onClick={handleDisconnectMQTT}
                                    startIcon={<WifiOffIcon />}
                                    sx={{
                                        borderRadius: "10px"
                                    }}
                                >
                                    Disconnect
                                </Button>
                            ) : (
                                <Button
                                    variant="contained"
                                    color="error"
                                    onClick={() => setMqttDialogOpen(true)}
                                    startIcon={<WifiIcon />}
                                    sx={{
                                        borderRadius: "10px",
                                    }}
                                >
                                    Connect
                                </Button>
                            )}
                        </Box>
                    </Toolbar>
                </Container>
            </AppBar>

            {/* MQTT Connection Dialog */}
            <Dialog
                open={mqttDialogOpen}
                onClose={() => setMqttDialogOpen(false)}
                maxWidth="sm"
                fullWidth
            >
                <DialogTitle>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <WifiIcon />
                        Connect to your BeE board
                    </Box>
                </DialogTitle>
                <DialogContent>
                    <Box sx={{ display: 'flex', flexDirection: 'column', mt: 1 }}>
                        <CustomTextField
                            label="BeE's IP address"
                            value={mqttConfig.target_ip}
                            onChange={handleMqttConfigChange('target_ip')}
                            fullWidth
                            placeholder="***********"
                        // helperText="BeE board IP address"
                        />
                    </Box>
                </DialogContent>
                <DialogActions sx={{ display: 'flex', justifyContent: 'space-between', px: "20px", mb: "10px" }}>
                    <Button
                        variant="outlined"
                        color="inherit"
                        sx={{
                            borderRadius: "10px",
                            mr: 2,
                        }}
                        onClick={() => setMqttDialogOpen(false)}
                    >
                        Cancel
                    </Button>
                    <Button
                        sx={{ borderRadius: "10px" }}
                        onClick={handleMqttConnect}
                        variant="contained"
                        startIcon={<WifiIcon />}
                    >
                        Connect
                    </Button>
                </DialogActions>
            </Dialog>
        </>
    );
}

export default BeeAppBarMQTT;
