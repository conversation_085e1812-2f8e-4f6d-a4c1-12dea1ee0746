// TypeScript definitions for Bluetooth Adapter

export interface BluetoothDevice {
    id: string;
    name: string;
    gatt?: BluetoothRemoteGATTServer;
}

export interface BluetoothMessage {
    method: string;
    params: Record<string, any>;
    id: string | number;
}

export interface BluetoothResponse {
    id: string | number;
    success: boolean;
    data?: any;
    error?: string;
}

export interface BluetoothEvent {
    type: 'disconnected' | 'message';
    data?: string;
}

export interface FlutterWebViewGlobals {
    flutter_inappwebview?: {
        callHandler: (handlerName: string, message: BluetoothMessage) => void;
    };
    webkit?: {
        messageHandlers?: {
            flutterChannel?: {
                postMessage: (message: BluetoothMessage) => void;
            };
        };
    };
    handleBluetoothResponse?: (response: BluetoothResponse) => void;
    handleBluetoothEvent?: (event: BluetoothEvent) => void;
    _bluetoothCallbacks?: Record<string | number, {
        resolve: (value: any) => void;
        reject: (error: Error) => void;
    }>;
}

// Extend Window interface
declare global {
    interface Window extends FlutterWebViewGlobals {}
}

export abstract class BluetoothAdapter {
    abstract isConnected: boolean;
    abstract device: BluetoothDevice | null;
    abstract onDisconnected: ((event: any) => void) | null;
    abstract onMessage: ((message: string) => void) | null;

    abstract connect(): Promise<void>;
    abstract disconnect(): Promise<void>;
    abstract writeData(data: Uint8Array): Promise<void>;
    abstract writeASCII(text: string): Promise<void>;
    abstract writeBuf20B(buffer: Uint8Array): Promise<void>;
    abstract setOnDisconnected(callback: (event: any) => void): void;
    abstract setOnMessage(callback: (message: string) => void): void;
}

export interface WebBluetoothCharacteristic {
    writeValue(data: Uint8Array): Promise<void>;
    startNotifications(): Promise<void>;
    addEventListener(event: string, callback: (event: any) => void): void;
}

export interface WebBluetoothService {
    getCharacteristic(uuid: string): Promise<WebBluetoothCharacteristic>;
}

export interface WebBluetoothServer {
    getPrimaryService(uuid: string): Promise<WebBluetoothService>;
}

export interface WebBluetoothDevice {
    name: string;
    id: string;
    gatt: WebBluetoothServer & {
        connected: boolean;
        connect(): Promise<WebBluetoothServer>;
        disconnect(): Promise<void>;
    };
    addEventListener(event: string, callback: (event: any) => void): void;
}

export interface NavigatorBluetooth {
    requestDevice(options: {
        filters: Array<{ namePrefix: string }>;
        optionalServices: string[];
    }): Promise<WebBluetoothDevice>;
}

declare global {
    interface Navigator {
        bluetooth?: NavigatorBluetooth;
    }
}
