import React, { useState } from "react";
import {
    Mo<PERSON>,
    Box,
    Typography,
    List,
    ListItem,
    ListItemButton,
    ListItemIcon,
    ListItemText,
    Button,
    CircularProgress,
    Divider,
    <PERSON><PERSON>,
    Chip,
} from "@mui/material";
import BluetoothIcon from "@mui/icons-material/Bluetooth";
import SignalWifi4BarIcon from "@mui/icons-material/SignalWifi4Bar";
import SignalWifi3BarIcon from "@mui/icons-material/SignalWifi3Bar";
import SignalWifi2BarIcon from "@mui/icons-material/SignalWifi2Bar";
import SignalWifi1BarIcon from "@mui/icons-material/SignalWifi1Bar";
import RefreshIcon from "@mui/icons-material/Refresh";

const BluetoothDeviceModal = ({ 
    open, 
    onClose, 
    onDeviceSelect, 
    devices = [], 
    scanning = false, 
    onScan,
    error = null 
}) => {
    const [selectedDevice, setSelectedDevice] = useState(null);

    const style = {
        position: "absolute",
        top: "50%",
        left: "50%",
        transform: "translate(-50%, -50%)",
        width: 450,
        maxHeight: "80vh",
        bgcolor: "background.paper",
        borderRadius: "12px",
        boxShadow: 24,
        p: 0,
        display: "flex",
        flexDirection: "column",
        overflow: "hidden",
    };

    const getSignalIcon = (rssi) => {
        if (rssi >= -50) return <SignalWifi4BarIcon color="success" />;
        if (rssi >= -60) return <SignalWifi3BarIcon color="success" />;
        if (rssi >= -70) return <SignalWifi2BarIcon sx={{ color: "orange" }} />;
        return <SignalWifi1BarIcon color="error" />;
    };

    const getSignalStrength = (rssi) => {
        if (rssi >= -50) return "Excellent";
        if (rssi >= -60) return "Good";
        if (rssi >= -70) return "Fair";
        return "Weak";
    };

    const handleDeviceClick = (device) => {
        setSelectedDevice(device);
    };

    const handleConnect = () => {
        if (selectedDevice) {
            onDeviceSelect(selectedDevice);
            setSelectedDevice(null);
        }
    };

    const handleClose = () => {
        setSelectedDevice(null);
        onClose();
    };

    const handleScan = () => {
        setSelectedDevice(null);
        if (onScan) {
            onScan();
        }
    };

    return (
        <Modal
            open={open}
            onClose={handleClose}
            aria-labelledby="bluetooth-device-modal-title"
            aria-describedby="bluetooth-device-modal-description"
        >
            <Box sx={style}>
                {/* Header */}
                <Box sx={{ p: 3, pb: 2 }}>
                    <Typography 
                        id="bluetooth-device-modal-title" 
                        variant="h6" 
                        component="h2"
                        sx={{ display: "flex", alignItems: "center", gap: 1 }}
                    >
                        <BluetoothIcon color="primary" />
                        Select BeE Device
                    </Typography>
                    <Typography 
                        variant="body2" 
                        color="text.secondary" 
                        sx={{ mt: 1 }}
                    >
                        Choose a BeE device to connect to
                    </Typography>
                </Box>

                <Divider />

                {/* Content */}
                <Box sx={{ flexGrow: 1, overflow: "auto", minHeight: 200 }}>
                    {error && (
                        <Box sx={{ p: 2 }}>
                            <Alert severity="error" sx={{ mb: 2 }}>
                                {error}
                            </Alert>
                        </Box>
                    )}

                    {scanning && (
                        <Box sx={{ 
                            display: "flex", 
                            alignItems: "center", 
                            justifyContent: "center", 
                            p: 3,
                            gap: 2 
                        }}>
                            <CircularProgress size={24} />
                            <Typography variant="body2" color="text.secondary">
                                Scanning for BeE devices...
                            </Typography>
                        </Box>
                    )}

                    {!scanning && devices.length === 0 && !error && (
                        <Box sx={{ 
                            display: "flex", 
                            flexDirection: "column",
                            alignItems: "center", 
                            justifyContent: "center", 
                            p: 4,
                            textAlign: "center"
                        }}>
                            <BluetoothIcon sx={{ fontSize: 48, color: "text.disabled", mb: 2 }} />
                            <Typography variant="body1" color="text.secondary" sx={{ mb: 1 }}>
                                No BeE devices found
                            </Typography>
                            <Typography variant="body2" color="text.disabled">
                                Make sure your BeE device is powered on and in pairing mode
                            </Typography>
                        </Box>
                    )}

                    {!scanning && devices.length > 0 && (
                        <List sx={{ p: 0 }}>
                            {devices.map((device, index) => (
                                <ListItem key={device.id || index} disablePadding>
                                    <ListItemButton
                                        selected={selectedDevice?.id === device.id}
                                        onClick={() => handleDeviceClick(device)}
                                        sx={{ 
                                            py: 2,
                                            "&.Mui-selected": {
                                                backgroundColor: "primary.50",
                                                "&:hover": {
                                                    backgroundColor: "primary.100",
                                                }
                                            }
                                        }}
                                    >
                                        <ListItemIcon>
                                            <BluetoothIcon color="primary" />
                                        </ListItemIcon>
                                        <ListItemText
                                            primary={
                                                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                                                    <Typography variant="subtitle1">
                                                        {device.name || "Unknown Device"}
                                                    </Typography>
                                                    {device.name?.startsWith("BeE") && (
                                                        <Chip 
                                                            label="BeE" 
                                                            size="small" 
                                                            color="primary" 
                                                            variant="outlined"
                                                        />
                                                    )}
                                                </Box>
                                            }
                                            secondary={
                                                <Box sx={{ mt: 0.5 }}>
                                                    <Typography variant="body2" color="text.secondary">
                                                        ID: {device.id}
                                                    </Typography>
                                                    {device.rssi && (
                                                        <Box sx={{ display: "flex", alignItems: "center", gap: 0.5, mt: 0.5 }}>
                                                            {getSignalIcon(device.rssi)}
                                                            <Typography variant="caption" color="text.secondary">
                                                                {getSignalStrength(device.rssi)} ({device.rssi} dBm)
                                                            </Typography>
                                                        </Box>
                                                    )}
                                                </Box>
                                            }
                                        />
                                    </ListItemButton>
                                </ListItem>
                            ))}
                        </List>
                    )}
                </Box>

                <Divider />

                {/* Footer */}
                <Box sx={{ p: 3, pt: 2, display: "flex", gap: 2, justifyContent: "space-between" }}>
                    <Button
                        startIcon={<RefreshIcon />}
                        onClick={handleScan}
                        disabled={scanning}
                        variant="outlined"
                    >
                        {scanning ? "Scanning..." : "Scan Again"}
                    </Button>
                    
                    <Box sx={{ display: "flex", gap: 1 }}>
                        <Button onClick={handleClose} color="inherit">
                            Cancel
                        </Button>
                        <Button
                            onClick={handleConnect}
                            variant="contained"
                            disabled={!selectedDevice}
                        >
                            Connect
                        </Button>
                    </Box>
                </Box>
            </Box>
        </Modal>
    );
};

export default BluetoothDeviceModal;
