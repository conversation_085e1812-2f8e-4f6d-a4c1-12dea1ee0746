# Bluetooth Adapter for BeE IDE

## Overview

The Bluetooth Adapter provides a unified interface for connecting to BeE devices via Bluetooth from both web browsers (using Web Bluetooth API) and iOS devices (using CoreBluetooth through Flutter WebView).

## Architecture

### Adapter Pattern
- `BluetoothAdapter`: Abstract base class defining the interface
- `WebBluetoothAdapter`: Implementation for web browsers using Web Bluetooth API
- `iOSBluetoothAdapter`: Implementation for iOS devices through Flutter WebView communication

### Platform Detection
The system automatically detects the platform and creates the appropriate adapter:
- Web browsers: Uses `WebBluetoothAdapter`
- Flutter WebView (iOS): Uses `iOSBluetoothAdapter`

## Usage

### Web Browser
No additional setup required. The Web Bluetooth API is used directly.

### iOS Flutter App
To use the iOS Bluetooth adapter, you need to implement the Flutter side handlers.

#### 1. Add Dependencies to pubspec.yaml
```yaml
dependencies:
  flutter_bluetooth_serial: ^0.4.0
  # or
  flutter_blue_plus: ^1.12.13
```

#### 2. iOS Permissions (Info.plist)
```xml
<key>NSBluetoothAlwaysUsageDescription</key>
<string>This app needs Bluetooth access to connect to BeE devices</string>
<key>NSBluetoothPeripheralUsageDescription</key>
<string>This app needs Bluetooth access to connect to BeE devices</string>
```

#### 3. Flutter WebView Handler Implementation

```dart
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';

class BeeIDEWebView extends StatefulWidget {
  @override
  _BeeIDEWebViewState createState() => _BeeIDEWebViewState();
}

class _BeeIDEWebViewState extends State<BeeIDEWebView> {
  InAppWebViewController? webViewController;
  BluetoothDevice? connectedDevice;
  BluetoothCharacteristic? rxCharacteristic;
  BluetoothCharacteristic? txCharacteristic;

  // UUIDs for BeE device
  final String serviceUUID = "6e400001-b5a3-f393-e0a9-e50e24dcca9e";
  final String rxUUID = "6e400002-b5a3-f393-e0a9-e50e24dcca9e";
  final String txUUID = "6e400003-b5a3-f393-e0a9-e50e24dcca9e";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: InAppWebView(
          initialUrlRequest: URLRequest(
            url: Uri.parse("https://your-bee-ide-url.com")
          ),
          onWebViewCreated: (controller) {
            webViewController = controller;
            
            // Add handler for Bluetooth operations
            controller.addJavaScriptHandler(
              handlerName: 'bluetoothHandler',
              callback: (args) async {
                return await handleBluetoothMessage(args[0]);
              },
            );
          },
          onLoadStop: (controller, url) {
            // Setup global functions for WebView communication
            controller.evaluateJavascript(source: """
              window.handleBluetoothResponse = function(response) {
                // This will be called from Flutter
              };
              
              window.handleBluetoothEvent = function(event) {
                // This will be called from Flutter
              };
            """);
          },
        ),
      ),
    );
  }

  Future<Map<String, dynamic>> handleBluetoothMessage(Map<String, dynamic> message) async {
    try {
      String method = message['method'];
      Map<String, dynamic> params = message['params'] ?? {};
      String id = message['id'];

      switch (method) {
        case 'bluetooth_connect':
          return await connectToDevice(id);
        
        case 'bluetooth_disconnect':
          return await disconnectDevice(id);
        
        case 'bluetooth_write':
          return await writeData(params['data'], id);
        
        case 'bluetooth_write_chunked':
          return await writeDataChunked(
            params['data'], 
            params['chunkSize'] ?? 20,
            params['delay'] ?? 2,
            id
          );
        
        default:
          return {
            'id': id,
            'success': false,
            'error': 'Unknown method: $method'
          };
      }
    } catch (e) {
      return {
        'id': message['id'],
        'success': false,
        'error': e.toString()
      };
    }
  }

  Future<Map<String, dynamic>> connectToDevice(String id) async {
    try {
      // Check if Bluetooth is available
      if (await FlutterBluePlus.isAvailable == false) {
        throw Exception("Bluetooth not available");
      }

      // Start scanning for BeE devices
      await FlutterBluePlus.startScan(
        withNames: ["BeE"],
        timeout: Duration(seconds: 10),
      );

      // Listen for scan results
      BluetoothDevice? targetDevice;
      await for (ScanResult result in FlutterBluePlus.scanResults) {
        if (result.device.name.startsWith("BeE")) {
          targetDevice = result.device;
          break;
        }
      }

      await FlutterBluePlus.stopScan();

      if (targetDevice == null) {
        throw Exception("No BeE device found");
      }

      // Connect to device
      await targetDevice.connect();
      connectedDevice = targetDevice;

      // Discover services
      List<BluetoothService> services = await targetDevice.discoverServices();
      
      BluetoothService? targetService;
      for (BluetoothService service in services) {
        if (service.uuid.toString().toLowerCase() == serviceUUID.toLowerCase()) {
          targetService = service;
          break;
        }
      }

      if (targetService == null) {
        throw Exception("BeE service not found");
      }

      // Get characteristics
      for (BluetoothCharacteristic characteristic in targetService.characteristics) {
        String charUUID = characteristic.uuid.toString().toLowerCase();
        if (charUUID == rxUUID.toLowerCase()) {
          rxCharacteristic = characteristic;
        } else if (charUUID == txUUID.toLowerCase()) {
          txCharacteristic = characteristic;
          // Enable notifications
          await characteristic.setNotifyValue(true);
          characteristic.value.listen((value) {
            String message = String.fromCharCodes(value);
            // Send message to WebView
            webViewController?.evaluateJavascript(source: """
              if (window.handleBluetoothEvent) {
                window.handleBluetoothEvent({
                  type: 'message',
                  data: '$message'
                });
              }
            """);
          });
        }
      }

      // Listen for disconnection
      targetDevice.state.listen((state) {
        if (state == BluetoothDeviceState.disconnected) {
          connectedDevice = null;
          rxCharacteristic = null;
          txCharacteristic = null;
          
          // Notify WebView
          webViewController?.evaluateJavascript(source: """
            if (window.handleBluetoothEvent) {
              window.handleBluetoothEvent({
                type: 'disconnected'
              });
            }
          """);
        }
      });

      return {
        'id': id,
        'success': true,
        'data': {
          'deviceName': targetDevice.name,
          'deviceId': targetDevice.id.id,
        }
      };
    } catch (e) {
      return {
        'id': id,
        'success': false,
        'error': e.toString()
      };
    }
  }

  Future<Map<String, dynamic>> disconnectDevice(String id) async {
    try {
      if (connectedDevice != null) {
        await connectedDevice!.disconnect();
      }
      connectedDevice = null;
      rxCharacteristic = null;
      txCharacteristic = null;

      return {
        'id': id,
        'success': true,
        'data': null
      };
    } catch (e) {
      return {
        'id': id,
        'success': false,
        'error': e.toString()
      };
    }
  }

  Future<Map<String, dynamic>> writeData(List<int> data, String id) async {
    try {
      if (rxCharacteristic == null) {
        throw Exception("Not connected to device");
      }

      await rxCharacteristic!.write(data);

      return {
        'id': id,
        'success': true,
        'data': null
      };
    } catch (e) {
      return {
        'id': id,
        'success': false,
        'error': e.toString()
      };
    }
  }

  Future<Map<String, dynamic>> writeDataChunked(
    List<int> data, 
    int chunkSize, 
    int delay, 
    String id
  ) async {
    try {
      if (rxCharacteristic == null) {
        throw Exception("Not connected to device");
      }

      for (int i = 0; i < data.length; i += chunkSize) {
        int end = (i + chunkSize < data.length) ? i + chunkSize : data.length;
        List<int> chunk = data.sublist(i, end);
        
        await rxCharacteristic!.write(chunk);
        
        if (delay > 0) {
          await Future.delayed(Duration(milliseconds: delay));
        }
      }

      return {
        'id': id,
        'success': true,
        'data': null
      };
    } catch (e) {
      return {
        'id': id,
        'success': false,
        'error': e.toString()
      };
    }
  }
}
```

## API Reference

### BluetoothAdapter Methods

#### `connect()`
Connects to a BeE device. For web, shows device selection popup. For iOS, scans and connects automatically.

#### `disconnect()`
Disconnects from the current device.

#### `writeData(data: Uint8Array)`
Writes raw data to the device.

#### `writeASCII(text: string)`
Writes ASCII text to the device.

#### `writeBuf20B(buffer: Uint8Array)`
Writes data in 20-byte chunks with small delays.

### Events

#### `onDisconnected`
Called when device disconnects.

#### `onMessage`
Called when receiving data from device.

## Error Handling

The adapter handles various error scenarios:
- Device not found
- Connection failures
- Permission denied
- Bluetooth not available

Errors are propagated to the calling code with descriptive messages.
