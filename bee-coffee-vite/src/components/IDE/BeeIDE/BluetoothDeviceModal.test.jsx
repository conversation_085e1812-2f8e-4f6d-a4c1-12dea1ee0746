// Test component for Bluetooth Device Modal
import React, { useState } from 'react';
import { Button, Box } from '@mui/material';

// Mock devices for testing
const mockDevices = [
    { id: 'device1', name: 'BeE Board #1', rssi: -45 },
    { id: 'device2', name: 'BeE Board #2', rssi: -65 },
    { id: 'device3', name: 'BeE Board #3', rssi: -80 },
];

const BluetoothDeviceModalTest = () => {
    const [modalOpen, setModalOpen] = useState(false);
    const [selectedDevice, setSelectedDevice] = useState(null);

    const handleDeviceSelect = (device) => {
        setSelectedDevice(device);
        setModalOpen(false);
        console.log('Selected device:', device);
    };

    const handleModalClose = () => {
        setModalOpen(false);
        console.log('Modal closed');
    };

    return (
        <Box sx={{ p: 4 }}>
            <Button 
                variant="contained" 
                onClick={() => setModalOpen(true)}
                sx={{ mb: 2 }}
            >
                Test Bluetooth Device Modal
            </Button>
            
            {selectedDevice && (
                <Box sx={{ p: 2, border: '1px solid #ccc', borderRadius: 1 }}>
                    <h3>Selected Device:</h3>
                    <p>Name: {selectedDevice.name}</p>
                    <p>ID: {selectedDevice.id}</p>
                    <p>RSSI: {selectedDevice.rssi} dBm</p>
                </Box>
            )}

            {/* Import and use BluetoothDeviceModal here */}
            {/* 
            <BluetoothDeviceModal
                open={modalOpen}
                onClose={handleModalClose}
                devices={mockDevices}
                onDeviceSelect={handleDeviceSelect}
                scanning={false}
            />
            */}
        </Box>
    );
};

export default BluetoothDeviceModalTest;
