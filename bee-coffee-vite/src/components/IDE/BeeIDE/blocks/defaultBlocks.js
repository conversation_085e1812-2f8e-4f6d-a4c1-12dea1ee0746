import * as Blockly from "blockly";
import { pythonGenerator } from "blockly/python";

import StartIcon from "./images/start.png";

const PASS = "pass\n";

// Define the hat block for program start
Blockly.Blocks["start_program"] = {
    init: function () {
        // this.appendDummyInput()
        //     .appendField("when program start");
        // this.setNextStatement(true);
        // this.setStyle('hat_blocks');
        // this.setColour("#FFD500");
        this.jsonInit({
            type: "start_program",
            message0: "%{BKY_START_PROGRAM}",
            args0: [
                {
                    type: "field_image",
                    src: StartIcon,
                    width: 45,
                    height: 45,
                    alt: "start",
                },
            ],
            nextStatement: true,
            style: {
                hat: "cap",
            },
            colour: "#FFD500",
        });
    },
};

// Define Python generator for event_whenflagclicked
pythonGenerator.forBlock["start_program"] = function (block) {
    return "";
};

function add_indent(substack) {
    // Tự động thêm indent nếu substack không rỗng
    if (substack.trim()) {
        return substack
            .split("\n")
            .map((line) => (line.trim() ? pythonGenerator.INDENT + line : line))
            .join("\n");
    }
    return pythonGenerator.INDENT; // + "pass\n";
}

// Định nghĩa block điều kiện IF
Blockly.Blocks["control_if"] = {
    init: function () {
        this.jsonInit({
            type: "control_if",
            message0: "%{BKY_CONTROL_IF}", // Combines "if" and "then" in one line
            message1: "%1", // Statement stack
            args0: [
                {
                    type: "input_value",
                    name: "CONDITION",
                    check: "Boolean",
                },
            ],
            args1: [
                {
                    type: "input_statement",
                    name: "SUBSTACK",
                },
            ],
            category: "control",
            colour: "#FFAB19", // Scratch orange color for control blocks
            previousStatement: true,
            nextStatement: true,
        });
    },
};

// Python generator for control_if
pythonGenerator.forBlock["control_if"] = function (block) {
    const condition = pythonGenerator.valueToCode(block, "CONDITION", pythonGenerator.ORDER_NONE) || "False";
    let substack = add_indent(pythonGenerator.statementToCode(block, "SUBSTACK") || PASS);
    return `if ${condition}:\n${substack}`;
};

// You might also want to add control_if_else
Blockly.Blocks["control_if_else"] = {
    init: function () {
        this.jsonInit({
            type: "control_if_else",
            message0: "%{BKY_CONTROL_IF}", // First line
            message1: "%1", // First statement stack
            message2: "%{BKY_CONTROL_IF_ELSE}", // Else line
            message3: "%1", // Second statement stack
            args0: [
                {
                    type: "input_value",
                    name: "CONDITION",
                    check: "Boolean",
                },
            ],
            args1: [
                {
                    type: "input_statement",
                    name: "SUBSTACK",
                },
            ],
            args3: [
                {
                    type: "input_statement",
                    name: "SUBSTACK2",
                },
            ],
            category: "control",
            colour: "#FFAB19",
            previousStatement: true,
            nextStatement: true,
        });
    },
};

// Python generator for control_if_else
pythonGenerator.forBlock["control_if_else"] = function (block) {
    const condition = pythonGenerator.valueToCode(block, "CONDITION", pythonGenerator.ORDER_NONE) || "False";
    const substack = add_indent(pythonGenerator.statementToCode(block, "SUBSTACK") || PASS);
    const substack2 = add_indent(pythonGenerator.statementToCode(block, "SUBSTACK2") || PASS);
    return `if ${condition}:\n${substack}else:\n${substack2}`;
};

// Định nghĩa block print
Blockly.Blocks["text_print"] = {
    init: function () {
        this.appendValueInput("TEXT")
            .setCheck(null)
            .appendField(Blockly.Msg["PRINT"])
            .connection.setShadowDom(
                Blockly.utils.xml.textToDom('<shadow type="text"><field name="TEXT">Hello</field></shadow>')
            );
        this.setPreviousStatement(true);
        this.setNextStatement(true);
        this.setColour("#5BA58C");
    },
};

// Generator code Python cho block print
pythonGenerator.forBlock["text_print"] = function (block) {
    const text = pythonGenerator.valueToCode(block, "TEXT", pythonGenerator.ORDER_NONE) || "''";
    return `print(${text})\n`;
};

// Override the default text_join block definition
Blockly.Blocks["text_join"] = {
    init: function () {
        this.setColour(160);
        this.appendValueInput("A").setCheck(null);
        this.appendValueInput("B").setCheck(null).appendField(Blockly.Msg["TEXT_JOIN"]);
        this.setInputsInline(true);
        this.setOutput(true, "String");
        this.setTooltip("Join two pieces of text together.");
    },
};

// Generator for text_join
pythonGenerator.forBlock["text_join"] = function (block) {
    const a = pythonGenerator.valueToCode(block, "A", pythonGenerator.ORDER_NONE) || "''";
    const b = pythonGenerator.valueToCode(block, "B", pythonGenerator.ORDER_NONE) || "''";
    return [`str(${a}) + str(${b})`, pythonGenerator.ORDER_NONE];
};

// Định nghĩa block số
Blockly.Blocks["math_number"] = {
    init: function () {
        this.appendDummyInput().appendField(new Blockly.FieldNumber(0), "NUM");
        this.setOutput(true, "Number");
        this.setColour(230);
    },
};

// Generator code Python cho block số
pythonGenerator.forBlock["math_number"] = function (block) {
    const number = block.getFieldValue("NUM");
    return [number, pythonGenerator.ORDER_ATOMIC];
};

Blockly.Blocks["controls_repeat"] = {
    init: function () {
        this.appendValueInput("TIMES")
            .setCheck("Number")
            .appendField(Blockly.Msg["CONTROL_REPEAT"])
            .connection.setShadowDom(
                Blockly.utils.xml.textToDom('<shadow type="math_number"><field name="NUM">10</field></shadow>')
            );

        this.appendDummyInput().appendField(Blockly.Msg["CONTROL_TIMES"]);

        this.appendStatementInput("DO");
        // .appendField('do');

        this.setInputsInline(true);
        this.setPreviousStatement(true);
        this.setNextStatement(true);
        this.setColour("#FFAB19"); // Scratch orange color for control blocks
        this.setTooltip("Repeat the enclosed blocks a specified number of times.");
    },
};

Blockly.Blocks["controls_whileUntil"] = {
    init: function () {
        this.appendValueInput("BOOL")
            .appendField(Blockly.Msg["CONTROL_REPEAT"])
            .setCheck("Boolean")
            .appendField(
                new Blockly.FieldDropdown([
                    [Blockly.Msg["CONTROL_WHILE"], "WHILE"],
                    [Blockly.Msg["CONTROL_UNTIL"], "UNTIL"],
                ]),
                "MODE"
            );

        this.appendStatementInput("DO");
        // .appendField('do');

        this.setPreviousStatement(true);
        this.setNextStatement(true);
        this.setColour("#FFAB19"); // Scratch orange color for control blocks
        this.setInputsInline(true);
        this.setTooltip("While/Until loop with condition");
    },
};

pythonGenerator.forBlock["controls_whileUntil"] = function (block) {
    // Get the mode (WHILE or UNTIL)
    const mode = block.getFieldValue("MODE");
    // Get the condition
    let condition = pythonGenerator.valueToCode(block, "BOOL", pythonGenerator.ORDER_NONE) || "False";
    // Get the loop body
    const branch = add_indent(pythonGenerator.statementToCode(block, "DO") || PASS);

    // If mode is UNTIL, negate the condition
    if (mode === "UNTIL") {
        condition = `not (${condition})`;
    }

    return `while ${condition}:\n${branch}`;
};

Blockly.Blocks["controls_flow_statements"] = {
    init: function () {
        this.appendDummyInput().appendField(
            new Blockly.FieldDropdown([
                [Blockly.Msg["CONTROL_BREAK"], "BREAK"],
                [Blockly.Msg["CONTROL_CONTINUE"], "CONTINUE"],
                // [Blockly.Msg["CONTROL_RETURN"], "RETURN"],
            ]),
            "FLOW"
        );

        this.setPreviousStatement(true);
        this.setNextStatement(false);
        this.setColour("#FFAB19"); // Matching control block color
        this.setTooltip("Break exits a loop, Continue skips to the next iteration");

        // This block should only be placed inside loops
        // this.setOnlyInLoop(true);
    },
};

// Python generator for the flow statements
pythonGenerator.forBlock["controls_flow_statements"] = function (block) {
    const flow = block.getFieldValue("FLOW");
    let code = "";

    switch (flow) {
        case "BREAK":
            code = "break\n";
            break;
        case "CONTINUE":
            code = "continue\n";
            break;
    }

    return code;
};

Blockly.Blocks["wait_until"] = {
    init: function () {
        this.appendValueInput("CONDITION").setCheck("Boolean").appendField(Blockly.Msg["CONTROL_UNTIL"]);

        this.setInputsInline(true);
        this.setPreviousStatement(true);
        this.setNextStatement(true);
        this.setColour("#FFAB19"); // Scratch orange color for control blocks
        this.setTooltip("Wait until the condition becomes true");
    },
};

// Python generator for wait_until
pythonGenerator.forBlock["wait_until"] = function (block) {
    // Add time import to dependencies if not already added
    pythonGenerator.definitions_["import_time"] = "import time";

    // Get the condition
    const condition = pythonGenerator.valueToCode(block, "CONDITION", pythonGenerator.ORDER_NONE) || "False";

    // Generate the wait until loop
    return `while not (${condition}):\n${add_indent("time.sleep(0.01)")}\n`;
};

Blockly.Blocks["control_forever"] = {
    init: function () {
        this.appendDummyInput().appendField(Blockly.Msg["CONTROL_FOREVER"]);
        // .appendField(new Blockly.FieldImage(
        //     // You can replace this with your own icon path
        //     "icons/control_forever.svg",
        //     24,
        //     24,
        //     { alt: "*", flipRtl: true }
        // ));

        this.appendStatementInput("SUBSTACK").setCheck(null);

        this.setPreviousStatement(true);
        this.setNextStatement(true);
        this.setColour("#FFAB19"); // Scratch orange color for control blocks
        this.setTooltip("Execute the blocks inside forever");

        // Make the block look more like Scratch
        this.setInputsInline(true);
    },
};

// Python generator for forever block
pythonGenerator.forBlock["control_forever"] = function (block) {
    // Get the code inside the forever block
    const branch = pythonGenerator.statementToCode(block, "SUBSTACK") || PASS;

    if (branch === PASS) {
        // Add time import to dependencies if not already added
        pythonGenerator.definitions_["import_time"] = "import time";
        return `while True:\n${add_indent("time.sleep(0.01)")}\n`;
    }

    // Generate the while True loop with a small delay to prevent CPU overload
    return `while True:\n${add_indent(branch)}\n`;
};

// Định nghĩa block phép toán
Blockly.Blocks["math_arithmetic"] = {
    init: function () {
        this.appendValueInput("A").setCheck("Number").setAlign(Blockly.ALIGN_LEFT);

        this.appendDummyInput().appendField(
            new Blockly.FieldDropdown([
                ["+", "ADD"],
                ["-", "MINUS"],
                ["×", "MULTIPLY"],
                ["÷", "DIVIDE"],
            ]),
            "OP"
        );

        this.appendValueInput("B").setCheck("Number").setAlign(Blockly.ALIGN_RIGHT);

        this.setInputsInline(true);
        this.setOutput(true, "Number");
        this.setColour(230);
    },
};

// Generator code Python cho block phép toán
pythonGenerator.forBlock["math_arithmetic"] = function (block) {
    const operators = {
        ADD: [" + ", pythonGenerator.ORDER_ADDITIVE],
        MINUS: [" - ", pythonGenerator.ORDER_ADDITIVE],
        MULTIPLY: [" * ", pythonGenerator.ORDER_MULTIPLICATIVE],
        DIVIDE: [" / ", pythonGenerator.ORDER_MULTIPLICATIVE],
    };
    const tuple = operators[block.getFieldValue("OP")];
    const operator = tuple[0];
    const order = tuple[1];
    const argument0 = pythonGenerator.valueToCode(block, "A", order) || "0";
    const argument1 = pythonGenerator.valueToCode(block, "B", order) || "0";
    const code = argument0 + operator + argument1;
    return [code, order];
};

// Define map function
Blockly.Blocks["math_map"] = {
    init: function () {
        this.appendValueInput("VALUE").setCheck("Number").appendField("map");

        this.appendValueInput("FROM_LOW").setCheck("Number").appendField("from low");
        this.appendValueInput("FROM_HIGH").setCheck("Number").appendField("high");
        this.appendValueInput("TO_LOW").setCheck("Number").appendField("to low");
        this.appendValueInput("TO_HIGH").setCheck("Number").appendField("high");

        this.setInputsInline(true);
        this.setOutput(true, "Number");
        this.setColour(230);
    },
};

pythonGenerator.forBlock["math_map"] = function (block) {
    const value = pythonGenerator.valueToCode(block, "VALUE", pythonGenerator.ORDER_ATOMIC) || "0";
    const fromLow = pythonGenerator.valueToCode(block, "FROM_LOW", pythonGenerator.ORDER_ATOMIC) || "0";
    const fromHigh = pythonGenerator.valueToCode(block, "FROM_HIGH", pythonGenerator.ORDER_ATOMIC) || "0";
    const toLow = pythonGenerator.valueToCode(block, "TO_LOW", pythonGenerator.ORDER_ATOMIC) || "0";
    const toHigh = pythonGenerator.valueToCode(block, "TO_HIGH", pythonGenerator.ORDER_ATOMIC) || "0";

    const code = `(${toLow} + (${value} - ${fromLow}) * (${toHigh} - ${toLow}) / (${fromHigh} - ${fromLow}))`;
    return [code, pythonGenerator.ORDER_MULTIPLICATIVE];
};

// Define set range
Blockly.Blocks["math_set_range"] = {
    init: function () {
        this.appendValueInput("VALUE").setCheck("Number").appendField(Blockly.Msg["CONTROL_SET_RANGE"]);

        this.appendValueInput("LOW").setCheck("Number").appendField(Blockly.Msg["CONTROL_LOW"]);
        this.appendValueInput("HIGH").setCheck("Number").appendField(Blockly.Msg["CONTROL_HIGH"]);

        this.setInputsInline(true);
        this.setOutput(true, "Number");
        this.setColour(230);
    },
};

pythonGenerator.forBlock["math_set_range"] = function (block) {
    const value = pythonGenerator.valueToCode(block, "VALUE", pythonGenerator.ORDER_ATOMIC) || "0";
    const low = pythonGenerator.valueToCode(block, "LOW", pythonGenerator.ORDER_ATOMIC) || "0";
    const high = pythonGenerator.valueToCode(block, "HIGH", pythonGenerator.ORDER_ATOMIC) || "0";

    const code = `min(max(${value}, ${low}), ${high})`;
    return [code, pythonGenerator.ORDER_MULTIPLICATIVE];
};

// Định nghĩa block vòng lặp for
Blockly.Blocks["controls_for"] = {
    init: function () {
        this.appendDummyInput()
            .appendField(Blockly.Msg["CONTROL_FOR"])
            .appendField(new Blockly.FieldVariable("i"), "VAR");

        this.appendValueInput("FROM")
            .setCheck("Number")
            .appendField(Blockly.Msg["CONTROL_FROM"])
            .connection.setShadowDom(
                Blockly.utils.xml.textToDom('<shadow type="math_number"><field name="NUM">1</field></shadow>')
            );

        this.appendValueInput("TO")
            .setCheck("Number")
            .appendField(Blockly.Msg["CONTROL_TO"])
            .connection.setShadowDom(
                Blockly.utils.xml.textToDom('<shadow type="math_number"><field name="NUM">5</field></shadow>')
            );

        this.appendValueInput("BY")
            .setCheck("Number")
            .appendField(Blockly.Msg["CONTROL_BY"])
            .connection.setShadowDom(
                Blockly.utils.xml.textToDom('<shadow type="math_number"><field name="NUM">1</field></shadow>')
            );

        this.appendStatementInput("DO");

        this.setInputsInline(true);
        this.setPreviousStatement(true);
        this.setNextStatement(true);
        this.setColour("#FFAB19");
    },
};

// Define the wait_seconds block
Blockly.Blocks["wait_seconds"] = {
    init: function () {
        this.appendValueInput("SECONDS")
            .setCheck("Number")
            .appendField(Blockly.Msg["CONTROL_WAIT"])
            .connection.setShadowDom(
                Blockly.utils.xml.textToDom('<shadow type="math_number"><field name="NUM">0.1</field></shadow>')
            );
        this.appendDummyInput().appendField(Blockly.Msg["CONTROL_SECONDS"]);
        this.setInputsInline(true);
        this.setPreviousStatement(true);
        this.setNextStatement(true);
        this.setColour("#FFAB19"); // Scratch orange color for control blocks
    },
};

// Python generator for wait_seconds
pythonGenerator.forBlock["wait_seconds"] = function (block) {
    // Add time import to dependencies if not already added
    pythonGenerator.definitions_["import_time"] = "import time";

    // Get the number input
    const seconds = pythonGenerator.valueToCode(block, "SECONDS", pythonGenerator.ORDER_ATOMIC) || "1";

    // Generate the sleep code
    return `time.sleep(${seconds})\n`;
};

// Override the workspaceToCode method to only generate code from hat blocks
pythonGenerator.workspaceToCode = function (workspace) {
    // Reset definitions
    this.definitions_ = Object.create(null);

    // Get all blocks in the workspace
    const blocks = workspace.getTopBlocks(true);

    // Filter for hat blocks
    const hatBlocks = blocks.filter((block) => block.hat === "cap" || block.type === "start_program");

    if (hatBlocks.length === 0) {
        return '# Add a "Hat block" block to start your program\n';
    }

    // First pass: Generate all blocks to ensure all definitions are collected
    let codeBlocks = [];
    for (const block of hatBlocks) {
        codeBlocks.push(add_indent(this.blockToCode(block)));
    }

    // Generate definitions code
    let code = "";
    const definitions = [];
    for (let name in this.definitions_) {
        definitions.push(this.definitions_[name]);
    }
    if (definitions.length) {
        code = definitions.join("\n\n") + "\n\n";
    }

    // Add import BeeBrain
    if (!code.includes("from BeeBrain import bee")) {
        code += "from BeeBrain import bee\n\n";
    }

    // Add try
    code += "try:\n";

    // Add the block code after definitions
    const code_f = codeBlocks.join("\n");

    if (!code_f.includes(".oled")) {
        code += add_indent("bee.oled.clear()\n");
        code += add_indent("bee.oled.text('Hello, there', 15, 0, 1)\n");
        code += add_indent(`bee.oled.text("Let's play with", 5, 12, 1)\n`);
        code += add_indent("bee.oled.text('BeE board 1.24.0', 0, 35, 1)\n");
        code += add_indent("bee.oled.text('beeblock.vn', 20, 50, 1)\n");
        code += add_indent("bee.oled.show()\n");
    }

    code += code_f;
    // Add except
    code += "\nexcept Exception as ex:\n";
    code += add_indent("bee.oled.long_text('Error: ' + str(ex))\n");

    // Remove import Number when add variable
    code = code.replace("from numbers import Number\n\n", "");
    // code = code.replace("if isinstance(i, Number) else", "if isinstance(i, (int, float)) else");
    code = code.replace(/if isinstance\(([a-zA-Z_$][a-zA-Z0-9_$]*), Number\)/g, "if isinstance($1, (int, float))");

    if (code === "") return `""" === Code is empty === """\n`;
    return code;
};
