# Bluetooth Device Selection Modal

## Tổng quan

`BluetoothDeviceModal` là một component React Modal được thiết kế để hiển thị danh sách các thiết bị BeE có sẵn và cho phép người dùng chọn thiết bị để kết nối. Component này được sử dụng chủ yếu cho iOS app khi chạy trong Flutter WebView.

## Tính năng

- ✅ **Hiển thị danh sách thiết bị**: Liệt kê tất cả thiết bị BeE được tìm thấy
- ✅ **Signal strength indicator**: Hiển thị cường độ tín hiệu với icon và màu sắc
- ✅ **Device information**: <PERSON><PERSON><PERSON> thị tên, ID và RSSI của thiết bị
- ✅ **Scanning indicator**: Hiển thị trạng thái đang scan
- ✅ **Empty state**: Hiển thị thông báo khi không tìm thấy thiết bị
- ✅ **Responsive design**: <PERSON><PERSON>ơng thích với các kích thước màn hình khác nhau

## Props

### `open: boolean`
Điều khiển việc hiển thị/ẩn modal.

### `onClose: () => void`
Callback được gọi khi người dùng đóng modal (click Cancel hoặc backdrop).

### `devices: Array<Device>`
Mảng các thiết bị để hiển thị. Mỗi device object có cấu trúc:
```javascript
{
    id: string,        // Unique identifier
    name: string,      // Device name (e.g., "BeE Board #1")
    rssi: number       // Signal strength in dBm (e.g., -45)
}
```

### `onDeviceSelect: (device: Device) => void`
Callback được gọi khi người dùng chọn một thiết bị.

### `scanning: boolean`
Hiển thị loading indicator khi đang scan thiết bị.

## Cách sử dụng

### 1. Basic Usage

```jsx
import { BluetoothDeviceModal } from './BluetoothDeviceModal';

const MyComponent = () => {
    const [modalOpen, setModalOpen] = useState(false);
    const [devices, setDevices] = useState([]);
    const [scanning, setScanning] = useState(false);

    const handleDeviceSelect = (device) => {
        console.log('Selected device:', device);
        setModalOpen(false);
        // Connect to device logic here
    };

    const handleModalClose = () => {
        setModalOpen(false);
    };

    return (
        <>
            <Button onClick={() => setModalOpen(true)}>
                Connect Bluetooth
            </Button>
            
            <BluetoothDeviceModal
                open={modalOpen}
                onClose={handleModalClose}
                devices={devices}
                onDeviceSelect={handleDeviceSelect}
                scanning={scanning}
            />
        </>
    );
};
```

### 2. Integration với iOSBluetoothAdapter

```javascript
// Trong iOSBluetoothAdapter
class iOSBluetoothAdapter extends BluetoothAdapter {
    constructor() {
        super();
        this.showDeviceModal = null; // Will be set by BeeIDE
    }

    async connect() {
        // ... scan for devices
        const devices = await this._scanForDevices();
        
        // Show modal and wait for user selection
        const selectedDevice = await this._showDeviceSelectionModal(devices);
        
        // Connect to selected device
        await this._connectToDevice(selectedDevice);
    }

    async _showDeviceSelectionModal(devices) {
        return new Promise((resolve) => {
            if (this.showDeviceModal) {
                this.showDeviceModal(devices, resolve);
            } else {
                resolve(devices[0]); // Fallback
            }
        });
    }
}

// Trong BeeIDE component
const initializeBluetoothAdapter = () => {
    if (isFlutterApp) {
        bluetoothAdapter.current = new iOSBluetoothAdapter();
        
        // Setup modal callback
        bluetoothAdapter.current.showDeviceModal = (devices, callback) => {
            setAvailableDevices(devices);
            setDeviceSelectionCallback(() => callback);
            setBluetoothDeviceModalOpen(true);
        };
    }
};
```

## Signal Strength Indicator

Modal hiển thị cường độ tín hiệu với 4 mức:

- **4/4 bars (Green)**: RSSI > -50 dBm (Excellent)
- **3/4 bars (Green)**: RSSI > -60 dBm (Good)  
- **2/4 bars (Orange)**: RSSI > -70 dBm (Fair)
- **1/4 bars (Red)**: RSSI > -80 dBm (Poor)
- **0/4 bars (Red)**: RSSI ≤ -80 dBm (Very Poor)

## Styling

Component sử dụng Material-UI theming và có thể được customize:

```jsx
<BluetoothDeviceModal
    open={modalOpen}
    onClose={handleModalClose}
    devices={devices}
    onDeviceSelect={handleDeviceSelect}
    scanning={scanning}
    sx={{
        '& .MuiDialog-paper': {
            borderRadius: '16px',
            minHeight: '400px'
        }
    }}
/>
```

## States

### Empty State
Khi không có thiết bị nào được tìm thấy:
- Hiển thị Bluetooth icon lớn
- Thông báo "No BeE devices found"
- Hướng dẫn kiểm tra thiết bị
- Nút "Retry Scan" (nếu không đang scan)

### Scanning State
Khi đang scan thiết bị:
- Hiển thị loading spinner trong title
- Thông báo "Scanning for BeE devices..."
- Disable các interaction không cần thiết

### Device List State
Khi có thiết bị được tìm thấy:
- Hiển thị danh sách thiết bị dạng list
- Mỗi item có icon Bluetooth, tên, ID, RSSI và signal strength
- Click vào item để chọn thiết bị

## Error Handling

Modal xử lý các trường hợp lỗi:

1. **Không có thiết bị**: Hiển thị empty state với hướng dẫn
2. **Scan timeout**: Cho phép retry scan
3. **Connection error**: Được xử lý bởi parent component

## Testing

Sử dụng `BluetoothDeviceModal.test.jsx` để test component:

```bash
# Run test component
npm run dev
# Navigate to test page to see modal in action
```

## Browser Compatibility

- ✅ **iOS Safari**: Full support trong Flutter WebView
- ✅ **Chrome/Edge**: Fallback to Web Bluetooth API
- ❌ **Firefox**: Limited support (no Web Bluetooth)

## Performance

- Modal chỉ render khi `open={true}`
- Device list được virtualized cho danh sách lớn
- Debounced search nếu có search functionality
- Lazy loading cho device icons

## Accessibility

- Keyboard navigation support
- Screen reader friendly
- Focus management
- ARIA labels và descriptions

## Future Enhancements

- [ ] Search/filter devices
- [ ] Sort by signal strength
- [ ] Remember last connected device
- [ ] Device pairing status indicator
- [ ] Connection history
- [ ] Custom device names
