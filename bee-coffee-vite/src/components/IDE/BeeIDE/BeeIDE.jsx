import React, { useEffect, useRef, useState } from "react";
import * as Blockly from "blockly";
import "blockly/blocks";
import "blockly/python";
import { pythonGenerator } from "blockly/python";
import "./blocks/defaultBlocks"; // Import custom blocks
import BeeAppBar from "./BeeAppBar";
import { Box, Button, CircularProgress, Divider, IconButton, Modal, styled, Tooltip, Typography } from "@mui/material";
import { ContinuousToolbox, ContinuousFlyout, ContinuousMetrics } from "@blockly/continuous-toolbox";
import "./BeeIDE.css";
import { grey } from "@mui/material/colors";
import { EXTENSIONS } from "./extensions/index"; // Add this import at the top
import FileUploadIcon from "@mui/icons-material/FileUpload";
import ExtensionIcon from "@mui/icons-material/Extension";
import CableIcon from "@mui/icons-material/Cable";
import ExtensionModal from "./ExtensionModal";

import { <PERSON><PERSON><PERSON>oader, readLoop } from "./esptool";
import LinearProgress, { linearProgressClasses } from "@mui/material/LinearProgress";

import SentimentSatisfiedAltIcon from "@mui/icons-material/SentimentSatisfiedAlt";
import CelebrationIcon from "@mui/icons-material/Celebration";
import { useDocumentTitle } from "../../../hooks/useDocumentTitle";

import * as Vi from "blockly/msg/vi";
import * as En from "blockly/msg/en";
import ViMsg from "./languages/vi.json";
import EnMsg from "./languages/en.json";

import BluetoothIcon from "@mui/icons-material/Bluetooth";

const BeeBoardVersion = "1.24.0";

// Bluetooth Adapter Interface
class BluetoothAdapter {
    constructor() {
        this.isConnected = false;
        this.device = null;
        this.onDisconnected = null;
        this.onMessage = null;
    }

    async connect() {
        throw new Error("connect() method must be implemented");
    }

    async disconnect() {
        throw new Error("disconnect() method must be implemented");
    }

    async writeData(data) {
        throw new Error("writeData() method must be implemented");
    }

    async writeASCII(text) {
        throw new Error("writeASCII() method must be implemented");
    }

    async writeBuf20B(buffer) {
        throw new Error("writeBuf20B() method must be implemented");
    }

    setOnDisconnected(callback) {
        this.onDisconnected = callback;
    }

    setOnMessage(callback) {
        this.onMessage = callback;
    }
}

// Web Bluetooth Adapter
class WebBluetoothAdapter extends BluetoothAdapter {
    constructor() {
        super();
        this.SERVICE = "6e400001-b5a3-f393-e0a9-e50e24dcca9e";
        this.TX = "6e400003-b5a3-f393-e0a9-e50e24dcca9e";
        this.RX = "6e400002-b5a3-f393-e0a9-e50e24dcca9e";
        this.server = null;
        this.service = null;
        this.txCharacteristic = null;
        this.rxCharacteristic = null;
    }

    async connect() {
        if (!navigator.bluetooth) {
            throw new Error("Web Bluetooth is not supported in this browser");
        }

        try {
            // Try to connect to known device first
            if (this.device && this.device.gatt && this.device.gatt.connected) {
                return;
            }

            // If no device or not connected, request new device
            if (!this.device) {
                this.device = await navigator.bluetooth.requestDevice({
                    filters: [{ namePrefix: "BeE" }],
                    optionalServices: [this.SERVICE],
                });
                this.device.addEventListener("gattserverdisconnected", this._onDisconnected.bind(this));
            }

            // Connect to GATT server
            this.server = await this.device.gatt.connect();
            this.service = await this.server.getPrimaryService(this.SERVICE);
            this.txCharacteristic = await this.service.getCharacteristic(this.TX);
            this.rxCharacteristic = await this.service.getCharacteristic(this.RX);

            // Setup notifications
            await this.txCharacteristic.startNotifications();
            this.txCharacteristic.addEventListener("characteristicvaluechanged", this._onMessage.bind(this));

            this.isConnected = true;
        } catch (error) {
            this.isConnected = false;
            throw error;
        }
    }

    async disconnect() {
        try {
            if (this.device && this.device.gatt && this.device.gatt.connected) {
                await this.device.gatt.disconnect();
            }
            this._resetConnection();
        } catch (error) {
            console.error("Error disconnecting Web Bluetooth:", error);
            this._resetConnection();
        }
    }

    async writeData(data) {
        if (!this.isConnected || !this.rxCharacteristic) {
            throw new Error("Not connected to device");
        }
        await this.rxCharacteristic.writeValue(data);
    }

    async writeASCII(text) {
        const data = new TextEncoder().encode(text);
        await this.writeData(data);
    }

    async writeBuf20B(buffer) {
        for (let i = 0; i < buffer.length; i += 20) {
            const chunk = buffer.subarray(i, Math.min(i + 20, buffer.length));
            await this.writeData(chunk);
            await new Promise((resolve) => setTimeout(resolve, 2)); // Small delay
        }
    }

    _onDisconnected(event) {
        this.isConnected = false;
        this._resetConnection();
        if (this.onDisconnected) {
            this.onDisconnected(event);
        }
    }

    _onMessage(event) {
        const message = new TextDecoder().decode(event.target.value);
        if (this.onMessage) {
            this.onMessage(message);
        }
    }

    _resetConnection() {
        this.isConnected = false;
        this.server = null;
        this.service = null;
        this.txCharacteristic = null;
        this.rxCharacteristic = null;
    }
}

// iOS CoreBluetooth Adapter (communicates with Flutter WebView)
class iOSBluetoothAdapter extends BluetoothAdapter {
    constructor() {
        super();
        this.isFlutterApp = this._detectFlutterApp();
    }

    _detectFlutterApp() {
        // Check if we're running in a Flutter WebView
        return (
            window.flutter_inappwebview !== undefined ||
            window.webkit?.messageHandlers?.flutterChannel !== undefined ||
            navigator.userAgent.includes("Flutter")
        );
    }

    async connect() {
        if (!this.isFlutterApp) {
            throw new Error("iOS Bluetooth adapter can only be used in Flutter WebView");
        }

        try {
            // Send message to Flutter to start Bluetooth connection
            const result = await this._sendToFlutter("bluetooth_connect", {});

            if (result.success) {
                this.isConnected = true;
                this.device = { name: result.deviceName, id: result.deviceId };

                // Setup message listener from Flutter
                this._setupFlutterMessageListener();
            } else {
                throw new Error(result.error || "Failed to connect to Bluetooth device");
            }
        } catch (error) {
            this.isConnected = false;
            throw error;
        }
    }

    async disconnect() {
        try {
            if (this.isConnected) {
                await this._sendToFlutter("bluetooth_disconnect", {});
            }
            this.isConnected = false;
            this.device = null;
        } catch (error) {
            console.error("Error disconnecting iOS Bluetooth:", error);
            this.isConnected = false;
            this.device = null;
        }
    }

    async writeData(data) {
        if (!this.isConnected) {
            throw new Error("Not connected to device");
        }

        // Convert Uint8Array to Array for JSON serialization
        const dataArray = Array.from(data);
        await this._sendToFlutter("bluetooth_write", { data: dataArray });
    }

    async writeASCII(text) {
        const data = new TextEncoder().encode(text);
        await this.writeData(data);
    }

    async writeBuf20B(buffer) {
        // Send the entire buffer to Flutter, let Flutter handle chunking
        await this._sendToFlutter("bluetooth_write_chunked", {
            data: Array.from(buffer),
            chunkSize: 20,
            delay: 2,
        });
    }

    async _sendToFlutter(method, params) {
        return new Promise((resolve, reject) => {
            const message = {
                method: method,
                params: params,
                id: Date.now() + Math.random(),
            };

            // Store callback for response
            window._bluetoothCallbacks = window._bluetoothCallbacks || {};
            window._bluetoothCallbacks[message.id] = { resolve, reject };

            // Send to Flutter
            if (window.flutter_inappwebview) {
                window.flutter_inappwebview.callHandler("bluetoothHandler", message);
            } else if (window.webkit?.messageHandlers?.flutterChannel) {
                window.webkit.messageHandlers.flutterChannel.postMessage(message);
            } else {
                // Fallback: post message to parent
                window.parent.postMessage(
                    {
                        type: "bluetooth_message",
                        data: message,
                    },
                    "*"
                );
            }

            // Timeout after 10 seconds
            setTimeout(() => {
                if (window._bluetoothCallbacks[message.id]) {
                    delete window._bluetoothCallbacks[message.id];
                    reject(new Error("Bluetooth operation timeout"));
                }
            }, 10000);
        });
    }

    _setupFlutterMessageListener() {
        // Listen for messages from Flutter
        window.handleBluetoothResponse = (response) => {
            if (response.id && window._bluetoothCallbacks && window._bluetoothCallbacks[response.id]) {
                const callback = window._bluetoothCallbacks[response.id];
                delete window._bluetoothCallbacks[response.id];

                if (response.success) {
                    callback.resolve(response.data);
                } else {
                    callback.reject(new Error(response.error));
                }
            }
        };

        // Listen for Bluetooth events from Flutter
        window.handleBluetoothEvent = (event) => {
            switch (event.type) {
                case "disconnected":
                    this.isConnected = false;
                    this.device = null;
                    if (this.onDisconnected) {
                        this.onDisconnected(event);
                    }
                    break;
                case "message":
                    if (this.onMessage) {
                        this.onMessage(event.data);
                    }
                    break;
            }
        };
    }
}

// Define custom category styles to match Scratch colors
const defaultCategoryStyles = {
    event_category: {
        colour: "#FFD500",
    },
    text_category: {
        colour: "#5BA58C",
    },
    list_category: {
        colour: "#57447C",
    },
    control_category: {
        colour: "#FFAB19",
    },
    variable_category: {
        colour: "#FF8C1A",
    },
    procedure_category: {
        colour: "#FF6680",
    },
};

// Define categories with Scratch-like colors
const defaultToolbox = {
    kind: "categoryToolbox",
    contents: [
        {
            id: "event",
            kind: "category",
            name: "Events",
            categorystyle: "event_category",
            contents: [{ kind: "block", type: "start_program" }],
        },
        {
            id: "text",
            kind: "category",
            name: "Text",
            categorystyle: "text_category",
            contents: [
                { kind: "block", type: "text" },
                { kind: "block", type: "text_print" },
                { kind: "block", type: "text_join" },
            ],
        },
        {
            id: "control",
            kind: "category",
            name: "Control",
            categorystyle: "control_category",
            contents: [
                { kind: "block", type: "wait_seconds" },
                // Condition
                { kind: "block", type: "control_if" },
                { kind: "block", type: "control_if_else" },
                // Loop number
                { kind: "block", type: "controls_repeat" },
                { kind: "block", type: "controls_whileUntil" },
                { kind: "block", type: "controls_for" },
                { kind: "block", type: "control_forever" },
                { kind: "block", type: "controls_flow_statements" },
                { kind: "block", type: "wait_until" },
            ],
        },
        {
            id: "logic",
            kind: "category",
            name: "Operators",
            categorystyle: "math_category",
            contents: [
                { kind: "block", type: "logic_compare" },
                { kind: "block", type: "logic_operation" },
                { kind: "block", type: "logic_negate" },
                { kind: "block", type: "logic_boolean" },
                { kind: "block", type: "logic_null" },
                // { kind: "block", type: "logic_ternary" },
            ],
        },
        {
            id: "math",
            kind: "category",
            name: "Math",
            categorystyle: "math_category",
            contents: [
                { kind: "block", type: "math_number" },
                { kind: "block", type: "math_arithmetic" },
                { kind: "block", type: "math_map" },
                { kind: "block", type: "math_set_range" },
                { kind: "block", type: "math_single" },
                { kind: "block", type: "math_trig" },
                { kind: "block", type: "math_constant" },
                { kind: "block", type: "math_round" },
                { kind: "block", type: "math_random_int" },
                { kind: "block", type: "math_modulo" },
            ],
        },
        {
            id: "procedure",
            kind: "category",
            name: "Procedure",
            categorystyle: "procedure_category",
            custom: "PROCEDURE", // Change this line
            contents: [], // Remove the static blocks
        },
        {
            id: "variable",
            kind: "category",
            name: "Variables",
            categorystyle: "variable_category",
            custom: "VARIABLE",
        },
    ],
};

const FlashFirmwareModel = ({ project, setProject, open, handleClose }) => {
    const [message, setMessage] = useState("Bạn có chắc muốn cập nhật Firmware?");
    const [value, setValue] = useState(-1);
    const [flashing, setFlashing] = useState(false);

    const style = {
        position: "absolute",
        top: "50%",
        left: "50%",
        transform: "translate(-50%, -50%)",
        width: 400,
        height: 400,
        bgcolor: "background.paper",
        borderRadius: "20px",
        // border: '2px solid #000',
        boxShadow: 24,
        p: 4,
        display: "flex",
        flexDirection: "column",
    };

    const BorderLinearProgress = styled(LinearProgress)(({ theme }) => ({
        height: 10,
        borderRadius: 5,
        [`&.${linearProgressClasses.colorPrimary}`]: {
            backgroundColor: theme.palette.grey[200],
            ...theme.applyStyles("dark", {
                backgroundColor: theme.palette.grey[800],
            }),
        },
        [`& .${linearProgressClasses.bar}`]: {
            borderRadius: 5,
            backgroundColor: "#1a90ff",
            ...theme.applyStyles("dark", {
                backgroundColor: "#308fe8",
            }),
        },
    }));

    const handleFlashFirmware = async () => {
        if (!window.confirm("Are you sure want to flash BeeBoard firmware?")) {
            return;
        }

        setFlashing(true);

        try {
            // Close existing connection
            if (project.writer) {
                await project.writer.close();
            }
            if (project.reader) {
                await project.reader.cancel();
                await project.reader.releaseLock();
            }
            if (project.port) {
                await project.port.close();
            }

            // Update state to show disconnected
            setProject((prev) => ({
                ...prev,
                serialConnected: false,
                port: null,
                writer: null,
                reader: null,
            }));

            // Initialize ESPLoader
            const logMsg = (text) => {
                setMessage(text);
            };

            const espLoader = new EspLoader({
                logMsg: logMsg,
            });

            if (espLoader.connected()) {
                await espLoader.disconnect();
            }
            // Start to flash
            setValue(0);

            // Connect and sync
            await espLoader.connect();

            readLoop().catch((error) => {
                setMessage(error);
                console.error(error);
            });

            await espLoader.sync();
            setMessage("Connected to Board");

            const chipInfo = await espLoader.chipName();
            setMessage("Chip:", chipInfo);

            // setMessage("Erase Flash...");
            // await espLoader.eraseFlash();

            // Get the firmware binary
            setMessage("Loading firmware...");
            const BeeBoardFirmware = `/assets/BeeIDE/beeboard/BeeBrain-v${BeeBoardVersion}.bin`;

            const firmwareResponse = await fetch(BeeBoardFirmware);
            if (!firmwareResponse.ok) {
                setMessage("Failed to load firmware");
                throw new Error(`Failed to load firmware: ${firmwareResponse.statusText}`);
            }
            const firmwareBuffer = await firmwareResponse.arrayBuffer();
            const firmwareUint8 = new Uint8Array(firmwareBuffer);

            // Begin flash process
            setMessage("Starting flash process...");
            const flashSize = firmwareUint8.length;
            const offset = 0x1000; // Standard MicroPython offset

            // Begin flash operation
            const numBlocks = await espLoader.flashBegin(flashSize, offset);
            setMessage(`Flash operation started. Number of blocks: ${numBlocks}`);

            // Flash data in blocks
            const flashWriteSize = espLoader.getFlashWriteSize();
            let written = 0;
            let position = 0;
            let seq = 0;

            while (position < flashSize) {
                const percentage = Math.floor((100 * (seq + 1)) / numBlocks);
                setValue(percentage);
                setMessage(`Writing at 0x${(offset + seq * flashWriteSize).toString(16)}... (${percentage}%)`);

                let block;
                if (flashSize - position >= flashWriteSize) {
                    block = Array.from(firmwareUint8.slice(position, position + flashWriteSize));
                } else {
                    block = Array.from(firmwareUint8.slice(position));
                    // Pad the last block
                    block = block.concat(new Array(flashWriteSize - block.length).fill(0xff));
                }

                await espLoader.flashBlock(block, seq);
                seq += 1;
                written += block.length;
                position += flashWriteSize;
            }

            // Finish flash process
            await espLoader.disconnect();
            setMessage("Flash complete");

            // Close connection
            setMessage("Firmware flashed successfully");
        } catch (error) {
            setMessage("Error while flashing firmware! Try to press reset button while flashing.");
            setValue(-1);
            console.error("Error flashing firmware:", error);
            alert("Error flashing firmware: " + JSON.stringify(error) + "\nTry to press reset button while flashing.");
        } finally {
            setFlashing(false);
        }
    };

    return (
        <Modal
            open={open}
            // onClose={handleClose}
            onClose={(event, reason) => {
                if (reason === "backdropClick") return; // Ignore backdrop clicks
                handleClose();
            }}
            aria-labelledby="modal-modal-flash"
            aria-describedby="modal-modal-flash-firmware"
        >
            <Box sx={style}>
                <Typography id="modal-modal-title" variant="h6" component="h2">
                    Firmware Upgrade
                </Typography>
                <Divider sx={{ mt: 1 }} />
                <Box
                    sx={{
                        mt: 1,
                        display: "flex",
                        justifyContent: "center",
                        flexDirection: "column",
                        alignItems: "center",
                        flexGrow: 1,
                    }}
                >
                    {message === "Firmware flashed successfully" ? (
                        <CelebrationIcon sx={{ fontSize: "200px" }} color="success" />
                    ) : value >= 0 ? (
                        <CircularProgress size="150px" color="primary" />
                    ) : (
                        <SentimentSatisfiedAltIcon sx={{ fontSize: "200px", color: "#F1C40F" }} />
                    )}
                    <Typography id="modal-modal-description" sx={{ mt: 2, mb: 2, color: grey[600] }}>
                        {message}
                    </Typography>
                    {value >= 0 && (
                        <>
                            {value < 100 && (
                                <Typography color="error">
                                    Please don't close this window or unplug the board!
                                </Typography>
                            )}
                            <Box
                                sx={{
                                    display: "flex",
                                    alignItems: "center",
                                    width: "100%",
                                }}
                            >
                                <Box sx={{ mt: 1, mb: 1, flexGrow: 1 }}>
                                    <BorderLinearProgress
                                        variant="determinate"
                                        value={value}
                                        sx={{ width: "100%", height: "10px" }}
                                    />
                                </Box>
                            </Box>
                        </>
                    )}
                </Box>
                <Box sx={{ display: "flex", justifyContent: "center" }}>
                    {message === "Firmware flashed successfully" ? (
                        <Button
                            sx={{ mr: 2, borderRadius: "10px", width: "200px" }}
                            color="primary"
                            variant="contained"
                            onClick={handleClose}
                        >
                            DONE
                        </Button>
                    ) : (
                        <>
                            <Button
                                loading={flashing}
                                disabled={flashing}
                                sx={{
                                    mr: 2,
                                    borderRadius: "10px",
                                    width: "200px",
                                }}
                                color="error"
                                variant="contained"
                                onClick={handleFlashFirmware}
                            >
                                Flash Image
                            </Button>
                            <Button
                                disabled={flashing}
                                sx={{ borderRadius: "10px" }}
                                color="inherit"
                                onClick={handleClose}
                            >
                                Cancel
                            </Button>
                        </>
                    )}
                </Box>
            </Box>
        </Modal>
    );
};

const BeeIDE = ({ user, setUser }) => {
    useDocumentTitle("BeE IDE");

    // Bluetooth adapter - will be initialized based on platform
    const bluetoothAdapter = useRef(null);

    function delay(ms) {
        return new Promise((r) => setTimeout(r, ms));
    }

    // Initialize Bluetooth adapter based on platform
    const initializeBluetoothAdapter = () => {
        if (!bluetoothAdapter.current) {
            // Detect platform and create appropriate adapter
            const isFlutterApp =
                window.flutter_inappwebview !== undefined ||
                window.webkit?.messageHandlers?.flutterChannel !== undefined ||
                navigator.userAgent.includes("Flutter");

            if (isFlutterApp) {
                bluetoothAdapter.current = new iOSBluetoothAdapter();
            } else {
                bluetoothAdapter.current = new WebBluetoothAdapter();
            }

            // Setup event handlers
            bluetoothAdapter.current.setOnDisconnected(onBluetoothDisconnected);
            bluetoothAdapter.current.setOnMessage((message) => {
                if (import.meta.env.VITE_ENV === "dev") {
                    console.log("[ESP32]", message.trim());
                }
            });
        }
        return bluetoothAdapter.current;
    };

    const blocklyDiv = useRef(null);
    const [project, setProject] = useState(() => {
        // Load project from localStorage
        const savedProject = localStorage.getItem("BeeIDEProject");
        const defaultProject = {
            name: "myProject",
            blocksJson: "",
            pythonCode: "",
            serialConnected: false,
            port: null,
            writer: null,
            reader: null,
        };

        return savedProject ? JSON.parse(savedProject) : defaultProject;
    });

    const workspaceRef = useRef(null);
    const toolboxRef = useRef(defaultToolbox); // Thay useState bằng useRef
    const [categoryStyles, setCategoryStyles] = useState(defaultCategoryStyles);
    const [loadedExtensions, setLoadedExtensions] = useState(new Set());
    const [firstLoad, setFirstLoad] = useState(true);
    const [showUploadFirmware, setShowUploadFirmware] = useState(true);
    const [extensionModalOpen, setExtensionModalOpen] = useState(false);
    const [openFlashFirmwareModal, setOpenFlashFirmwareModal] = React.useState(false);
    const [message, setMessage] = useState("");
    // Thêm state để lưu ID của interval
    const [serialMonitorInterval, setSerialMonitorInterval] = useState(null);
    const [language, setLanguage] = useState(() => {
        // Lấy ngôn ngữ từ localStorage, nếu không có thì mặc định là "vi"
        return localStorage.getItem("BeeIDELanguage") || "vi";
    });

    // Thêm state để theo dõi trạng thái Bluetooth
    const [bluetoothConnected, setBluetoothConnected] = useState(false);
    const [bluetoothUploading, setBluetoothUploading] = useState(false);
    const [serialUploading, setSerialUploading] = useState(false);

    // Thêm useEffect để lưu ngôn ngữ vào localStorage khi nó thay đổi
    useEffect(() => {
        localStorage.setItem("BeeIDELanguage", language);
    }, [language]);

    // Kiểm tra trạng thái Bluetooth khi component khởi tạo
    useEffect(() => {
        const initializeBluetooth = () => {
            // Initialize the adapter
            initializeBluetoothAdapter();

            // Khôi phục thông tin thiết bị từ localStorage (chỉ cho Web Bluetooth)
            if (bluetoothAdapter.current instanceof WebBluetoothAdapter) {
                const savedDevice = getSavedBluetoothDevice();
                if (savedDevice) {
                    setMessage(`Saved device found: ${savedDevice.name}`);
                }
            }
        };

        const checkBluetoothStatus = () => {
            if (bluetoothAdapter.current) {
                setBluetoothConnected(bluetoothAdapter.current.isConnected);
            } else {
                setBluetoothConnected(false);
            }
        };

        // Khởi tạo và kiểm tra ngay khi component mount
        initializeBluetooth();
        checkBluetoothStatus();

        // Kiểm tra định kỳ mỗi 5 giây
        const intervalId = setInterval(checkBluetoothStatus, 5000);

        return () => clearInterval(intervalId);
    }, []);

    const updateWorkspaceToolbox = () => {
        if (workspaceRef.current) {
            workspaceRef.current.updateToolbox(toolboxRef.current);
            workspaceRef.current.render();
        }
    };

    const [localExtensions, setLocalExtensions] = useState(() => {
        // Load extensions from localStorage
        const savedExtensions = localStorage.getItem("BeeIDELoadedExtensions");
        return savedExtensions ? new Set(JSON.parse(savedExtensions)) : new Set();
    });

    // Save project to localStorage whenever it changes
    useEffect(() => {
        const projectToSave = {
            name: project.name,
            blocksJson: project.blocksJson,
            pythonCode: project.pythonCode,
        };
        localStorage.setItem("BeeIDEProject", JSON.stringify(projectToSave));
    }, [project.name, project.blocksJson, project.pythonCode]);

    // Save loaded extensions to localStorage
    useEffect(() => {
        localStorage.setItem("BeeIDELoadedExtensions", JSON.stringify(Array.from(loadedExtensions)));
    }, [loadedExtensions]);

    // Load and install saved extensions on startup
    useEffect(() => {
        const loadSavedExtensions = async () => {
            // Load saved extensions
            const savedExtensions = Array.from(localExtensions);
            const availableExtensions = EXTENSIONS;
            for (const extensionId of savedExtensions) {
                const extension = availableExtensions.find((ext) => ext.id === extensionId);
                if (extension) {
                    await handleExtensionSelect(extension);
                    // Thêm delay nhỏ để đảm bảo state được cập nhật
                    await new Promise((resolve) => setTimeout(resolve, 100));
                }
            }
            // Reset localExtensions state
            setLocalExtensions(new Set());
        };

        if (workspaceRef.current && localExtensions.size > 0) {
            loadSavedExtensions();
        }
    }, [workspaceRef.current]); // Only run when workspace is initialized

    // Load workspace state after workspace and extensions are ready
    useEffect(() => {
        if (workspaceRef.current && project.blocksJson) {
            try {
                // Clear existing workspace
                workspaceRef.current.clear();

                // Load saved blocks - check if blocksJson is string or object
                const blocksData =
                    typeof project.blocksJson === "string" ? JSON.parse(project.blocksJson) : project.blocksJson;

                Blockly.serialization.workspaces.load(blocksData, workspaceRef.current);
            } catch (error) {
                console.error("Error loading workspace:", error);
                setMessage("Error loading workspace state");
            }
        }
    }, [workspaceRef.current]);

    const handleOpenFlashFirmwareModal = () => setOpenFlashFirmwareModal(true);
    const handleCloseFlashFirmwareModal = () => setOpenFlashFirmwareModal(false);

    useEffect(() => {
        // Add jQuery
        const jqueryScript = document.createElement("script");
        jqueryScript.src = "https://code.jquery.com/jquery-3.7.1.min.js";
        jqueryScript.id = "jquery-script";

        // Add Font Awesome CSS
        const link = document.createElement("link");
        link.rel = "stylesheet";
        link.href = "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css";
        link.id = "font-awesome-css";

        document.head.appendChild(jqueryScript);
        document.head.appendChild(link);

        // Cleanup function
        return () => {
            const existingScript = document.getElementById("jquery-script");
            const existingLink = document.getElementById("font-awesome-css");
            if (existingScript) existingScript.remove();
            if (existingLink) existingLink.remove();
        };
    }, []); // Empty dependency array means this runs once on mount

    const handleExtensionSelect = async (extension) => {
        if (loadedExtensions.has(extension.id)) {
            setMessage("Extension already loaded");
            return;
        }

        try {
            // Register new block definitions
            for (const [blockName, blockDef] of Object.entries(extension.blocks)) {
                Blockly.Blocks[blockName] = blockDef;
            }

            // Register BeeBoard generators
            for (const [blockName, genDef] of Object.entries(extension.generators)) {
                pythonGenerator[blockName] = genDef;
            }

            // Update toolbox
            const workspace = workspaceRef.current;

            let extension_toolbox = extension.toolbox;
            if (!extension_toolbox) {
                const toolboxModule = await import(`./extensions/${extension.id}/toolbox.js`);
                extension_toolbox = toolboxModule.default || toolboxModule.toolbox;
            }

            // Update toolbox reference directly
            toolboxRef.current = {
                ...toolboxRef.current,
                contents: [...toolboxRef.current.contents, extension_toolbox],
            };

            // Update workspace with new toolbox
            updateWorkspaceToolbox();

            // Refresh theme to maintain hat blocks
            const theme = workspace.getTheme();
            workspace.setTheme(theme);

            // Update start_program block style
            const startBlocks = workspace.getAllBlocks(false).filter((block) => block.type.includes("start_program"));

            if (startBlocks.length > 0) {
                for (const block of startBlocks) {
                    block.setStyle("hat_blocks");
                    block.initSvg();
                    block.render();
                }
            }

            workspace.scrollCenter();

            // Mark extension as loaded
            setLoadedExtensions((prev) => new Set([...prev, extension.id]));
            // setExtensionModalOpen(false);
            setMessage(`Extension ${extension.name} is installed`);
        } catch (error) {
            console.error("Error installing extension:", error);
            setMessage(`Error installing extension: ${error.message}`);
        }
    };

    const handleExtensionUninstall = (extension) => {
        // Remove all blocks of this extension from workspace
        const workspace = workspaceRef.current;
        const allBlocks = workspace.getAllBlocks(false);
        const blocksToRemove = allBlocks.filter((block) => block.type.startsWith(extension.id + "_"));

        // Remove blocks one by one
        Blockly.Events.setGroup(true); // Group these events together
        try {
            blocksToRemove.forEach((block) => {
                block.dispose(true); // true to dispose of all connected blocks
            });
        } finally {
            Blockly.Events.setGroup(false);
        }

        // Remove blocks from Blockly.Blocks
        for (const blockName in Blockly.Blocks) {
            if (blockName.startsWith(extension.id + "_")) {
                delete Blockly.Blocks[blockName];
            }
        }

        // Remove generators
        for (const blockName in pythonGenerator) {
            if (blockName.startsWith(extension.id + "_")) {
                delete pythonGenerator[blockName];
            }
        }

        // Update toolbox reference
        toolboxRef.current = {
            ...toolboxRef.current,
            contents: toolboxRef.current.contents.filter((category) => !category.id || !(category.id === extension.id)),
        };

        // Update workspace toolbox
        updateWorkspaceToolbox();

        // Refresh theme to maintain hat blocks
        const theme = workspace.getTheme();
        workspace.setTheme(theme);

        // Remove from loaded extensions
        setLoadedExtensions((prev) => {
            const newSet = new Set(prev);
            newSet.delete(extension.id);
            return newSet;
        });
        setMessage(`Extension ${extension.name} is uninstalled`);
    };

    useEffect(() => {
        if (blocklyDiv.current && window.Blockly) {
            // Set language
            if (language === "vi") {
                Blockly.setLocale(Vi);
                Object.keys(ViMsg).forEach((key) => {
                    Blockly.Msg[key] = ViMsg[key];
                });
            } else {
                Blockly.setLocale(En);
                Object.keys(EnMsg).forEach((key) => {
                    Blockly.Msg[key] = EnMsg[key];
                });
            }

            // Create custom theme with hat support
            const customTheme = Blockly.Theme.defineTheme("scratch", {
                base: Blockly.Themes.Classic,
                blockStyles: {
                    hat_blocks: {
                        colourPrimary: "#FFD500",
                        hat: "cap",
                    },
                    procedure_blocks: {
                        colourPrimary: "#FF6680",
                        hat: "cap",
                    },
                },
                componentStyles: {
                    workspaceBackgroundColour: "#ffffff",
                    toolboxBackgroundColour: "#F9F9F9",
                    toolboxForegroundColour: "#575E75",
                    flyoutBackgroundColour: "#F9F9F9",
                    flyoutForegroundColour: "#575E75",
                    flyoutOpacity: 1,
                    scrollbarColour: "#CECDCE",
                    insertionMarkerColour: "#000000",
                    insertionMarkerOpacity: 0.2,
                    scrollbarOpacity: 0.4,
                    cursorColour: "#000000",
                },
                categoryStyles: categoryStyles,
            });

            workspaceRef.current = Blockly.inject(blocklyDiv.current, {
                toolbox: toolboxRef.current, // Use ref instead of state
                theme: customTheme,
                renderer: "zelos", // Use Zelos renderer for better hat block appearance
                plugins: {
                    toolbox: ContinuousToolbox,
                    flyoutsVerticalToolbox: ContinuousFlyout,
                    metricsManager: ContinuousMetrics,
                },
                zoom: {
                    controls: true,
                    // wheel: true,
                    startScale: 0.7,
                    maxScale: Infinity,
                    minScale: 0.3,
                    scaleSpeed: 1.2,
                },
                scrollbars: true,
                comments: true,
                maxBlocks: Infinity,
                rtl: false,
                sounds: true,
                move: {
                    drag: true,
                    wheel: true,
                },
                trashcan: true,
            });

            // Add default start block
            const startBlock = workspaceRef.current.newBlock("start_program");
            startBlock.initSvg();
            startBlock.render();
            // Position the block in the center of workspace
            startBlock.moveBy(-200, -200);

            // Optional: Make it undeletable
            startBlock.setDeletable(false);

            // Gắn workspace vào pythonGenerator và gọi init
            pythonGenerator.workspace_ = workspaceRef.current; // Gán workspace thủ công
            pythonGenerator.init(workspaceRef.current); // Khởi tạo generator với workspace

            // Add change listener to generate Python code and prevent multiple start_program blocks
            workspaceRef.current.addChangeListener((event) => {
                // Prevent multiple start_program blocks
                if (event.type === Blockly.Events.BLOCK_CREATE) {
                    // Use setTimeout to ensure the block is fully created before checking
                    setTimeout(() => {
                        const createdBlock = workspaceRef.current.getBlockById(event.blockId);
                        if (createdBlock && createdBlock.type === "start_program") {
                            // Check if there's already a start_program block in the workspace
                            const allBlocks = workspaceRef.current.getAllBlocks(false);
                            const startBlocks = allBlocks.filter((block) => block.type === "start_program");

                            if (startBlocks.length > 1) {
                                // Remove the newly created start_program block
                                createdBlock.dispose(true);
                                setMessage("Only one 'start program' block is allowed in the workspace");
                                return;
                            }
                        }
                    }, 0);
                }

                // Only update code when a block is moved, deleted, or changed
                if (
                    event.type === Blockly.Events.BLOCK_MOVE ||
                    event.type === Blockly.Events.BLOCK_DELETE ||
                    event.type === Blockly.Events.BLOCK_CHANGE ||
                    event.type === Blockly.Events.BLOCK_CREATE
                ) {
                    // Generate Python code
                    const code = pythonGenerator.workspaceToCode(workspaceRef.current);
                    setProject((prevProject) => ({
                        ...prevProject,
                        pythonCode: code,
                        blocksJson: Blockly.serialization.workspaces.save(workspaceRef.current),
                    }));
                }
            });

            // Optional: Add a helper function to check if a block is connected to a hat block
            workspaceRef.current.isDescendantOfHat = function (block) {
                while (block) {
                    if (block.hat === "cap") {
                        return true;
                    }
                    block = block.getParent();
                }
                return false;
            };

            if (firstLoad) {
                // Center on the start block
                workspaceRef.current.scrollCenter();
                setFirstLoad(false);
            }

            setMessage("Workspace is initialized");

            return () => {
                workspaceRef.current.dispose();
            };
        }
    }, [blocklyDiv, language]);

    const closeSerial = async () => {
        try {
            // Giải phóng writer nếu đang có
            if (project.writer) {
                await project.writer.close();
                setProject((prev) => ({
                    ...prev,
                    writer: null,
                }));
            }

            // Giải phóng reader nếu đang có
            if (project.reader) {
                await project.reader.cancel();
                await project.reader.releaseLock();
                setProject((prev) => ({
                    ...prev,
                    reader: null,
                }));
            }

            // Đóng port
            if (project.port) {
                await project.port.close();
                setProject((prev) => ({
                    ...prev,
                    port: null,
                }));
            }

            setProject((prev) => ({
                ...prev,
                serialConnected: false,
            }));

            alert("Serial disconnected");
            setMessage("Serial disconnected");
        } catch (error) {
            console.error("Cannot disconnect serial:", error);
            alert("Cannot disconnect serial: " + error.message);
            setMessage("Error: Cannot disconnect serial");
        }
        return;
    };

    // Lưu trữ thông tin thiết bị Bluetooth trong localStorage
    const saveBluetoothDevice = (device) => {
        if (device && device.id) {
            const deviceInfo = {
                id: device.id,
                name: device.name,
                timestamp: Date.now(),
            };
            localStorage.setItem("BeeIDE_BluetoothDevice", JSON.stringify(deviceInfo));
        }
    };

    const getSavedBluetoothDevice = () => {
        try {
            const saved = localStorage.getItem("BeeIDE_BluetoothDevice");
            return saved ? JSON.parse(saved) : null;
        } catch (error) {
            console.error("Error reading saved Bluetooth device:", error);
            return null;
        }
    };

    const clearSavedBluetoothDevice = () => {
        localStorage.removeItem("BeeIDE_BluetoothDevice");
    };

    // Hàm để pair thiết bị Bluetooth lần đầu (hiển thị popup)

    // Hàm kết nối Bluetooth thông minh
    async function ensureBluetoothConnected() {
        const adapter = initializeBluetoothAdapter();

        // Kiểm tra xem đã kết nối chưa
        if (adapter.isConnected) {
            setMessage("Bluetooth already connected");
            setBluetoothConnected(true);
            return;
        }

        try {
            setMessage("Connecting to Bluetooth device...");
            await adapter.connect();

            setMessage("Bluetooth connected successfully");
            setBluetoothConnected(true);
        } catch (error) {
            console.error("Error ensuring Bluetooth connection:", error);
            setBluetoothConnected(false);

            if (error.name === "NotFoundError") {
                setMessage("No BeE device found");
            } else if (error.name === "SecurityError") {
                setMessage("Bluetooth access denied");
            } else if (error.name === "NetworkError") {
                setMessage("Failed to connect to device");
                // Xóa thiết bị đã lưu nếu không thể kết nối (chỉ cho Web Bluetooth)
                if (adapter instanceof WebBluetoothAdapter) {
                    clearSavedBluetoothDevice();
                }
            } else if (error.name === "NotAllowedError") {
                setMessage("User cancelled device selection");
            } else {
                setMessage("Bluetooth connection failed: " + error.message);
            }

            throw error;
        }
    }

    async function onBluetoothDisconnected(event) {
        console.warn("BLE disconnected, attempting to reconnect…");
        setMessage("Bluetooth disconnected, reconnecting...");
        setBluetoothConnected(false);

        const adapter = bluetoothAdapter.current;
        if (!adapter) return;

        // For Web Bluetooth, try to reconnect automatically
        if (adapter instanceof WebBluetoothAdapter) {
            for (let i = 0; i < 6; i++) {
                try {
                    setMessage(`Reconnecting... (${i + 1}/6)`);
                    await adapter.connect();
                    setMessage("Bluetooth reconnected successfully");
                    setBluetoothConnected(true);
                    return;
                } catch (e) {
                    console.warn(`Reconnection attempt ${i + 1} failed:`, e.message);
                    await delay(500 * 2 ** i);
                }
            }

            console.error("Failed to reconnect after 6 attempts. Manual reconnection required.");
            setMessage("Bluetooth reconnection failed. Please click Connect again.");
        } else {
            // For iOS, just notify that reconnection is needed
            setMessage("Bluetooth disconnected. Please reconnect manually.");
        }

        setBluetoothConnected(false);
    }

    // Hàm ngắt kết nối Bluetooth
    const disconnectBluetooth = async () => {
        try {
            const adapter = bluetoothAdapter.current;
            if (adapter && adapter.isConnected) {
                await adapter.disconnect();
            }
            setBluetoothConnected(false);
            setMessage("Bluetooth disconnected");
        } catch (error) {
            console.error("Error disconnecting Bluetooth:", error);
            setMessage("Error disconnecting Bluetooth");
        }
    };

    const handleConnectSerial = async () => {
        if (!navigator.serial) {
            alert("Browser does not support WebSerial API!\nPlease use Chrome or Edge!");
            return;
        }

        // Disconnect if serial already connected
        if (project.serialConnected) {
            closeSerial();
            return;
        }

        try {
            const filters = [
                // Silicon Labs CP210x (ESP32 DevKit, NodeMCU)
                { usbVendorId: 0x10c4, usbProductId: 0xea60 },

                // WCH CH340/CH341
                { usbVendorId: 0x1a86, usbProductId: 0x7523 },
                { usbVendorId: 0x1a86, usbProductId: 0x5523 },

                // Espressif native USB (ESP32-S2 / S3)
                { usbVendorId: 0x303a }, // Không filter PID để bắt mọi firmware Espressif
            ];
            const port = await navigator.serial.requestPort({ filters });
            await port.open({ baudRate: 115200 });
            const writer = port.writable.getWriter();
            setProject((prev) => ({
                ...prev,
                serialConnected: true,
                port: port,
                writer: writer,
            }));
            // alert("Serial connected");
            setMessage("Serial connected");

            // Check BeeBrain firmware
            if (import.meta.env.VITE_ENV !== "dev") {
                const firmwareCheck = await checkHardware(port, writer);
                if (!firmwareCheck.success || !firmwareCheck.isBeeBrain) {
                    alert("Please use BeeBrain firmware to upload code!");
                    setMessage("Please use BeeBrain firmware to upload code!");
                    closeSerial();
                    return;
                }
            }
        } catch (error) {
            console.error("Connection error:", error, project.port);
            if (project.port === undefined || project.port === null) {
                setProject((prev) => ({
                    ...prev,
                    serialConnected: false,
                    port: null,
                    writer: null,
                    reader: null,
                }));
                setMessage("Error: Cannot connect serial");
            } else {
                alert("Connection error: " + error.message);
                setMessage("Error: Cannot connect serial");
            }
        }
    };

    function extractIP(code) {
        const regex = /(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})/;
        const match = code.match(regex);
        return match ? match[0] : null;
    }

    const handleUploadCodeOTA = async () => {
        const ipAddress = extractIP(project.pythonCode);
        const url = `http://${ipAddress}:80`;
        setMessage(`Upload throught OTA ${url}`);
        try {
            // Upload module files first if they exist for loaded extensions
            for (const extensionId of loadedExtensions) {
                try {
                    const extension = EXTENSIONS.find((ext) => ext.id === extensionId);
                    const moduleFiles = extension?.modules || [];

                    if (moduleFiles.length === 0) continue;

                    for (const file of moduleFiles) {
                        const moduleContent = await fetch(
                            `/src/components/IDE/BeeIDE/extensions/${extensionId}/modules/${file}`
                        ).then((response) => response.text());

                        setMessage(`Uploading module: ${file}`);

                        const response = await fetch(`${url}/${file}`, {
                            method: "POST",
                            headers: { "Content-Type": "text/plain" },
                            body: moduleContent,
                            timeout: 2000,
                        });

                        if (response.status !== 200) {
                            throw new Error(`Failed to upload module ${file}`);
                        }
                    }
                } catch (error) {
                    console.error(`Error uploading module for ${extensionId}:`, error);
                    throw new Error(`Module upload failed: ${error.message}`);
                }
            }

            // Upload main code
            const response = await fetch(`${url}/main.py`, {
                method: "POST",
                headers: { "Content-Type": "text/plain" },
                body: project.pythonCode,
                timeout: 2000,
            });
            if (response.status === 200) {
                setMessage("Code uploaded successfully!");
            } else {
                setMessage("Error: Cannot upload code!");
            }
        } catch (error) {
            console.error("Lỗi:", error);
            setMessage("Error: Cannot connect to BeeBoard. Check IP and network!");
        }
    };

    const checkHardware = async (port, writer) => {
        if (!port || !writer) {
            return {
                success: false,
                message: "Please connect to the board first!",
            };
        }

        try {
            const encoder = new TextEncoder();

            // Reset REPL và gửi Ctrl-B
            await writer.write(encoder.encode("\x03\x03")); // Ctrl-C twice
            await new Promise((resolve) => setTimeout(resolve, 100));
            await writer.write(encoder.encode("\x02")); // Ctrl-B
            await new Promise((resolve) => setTimeout(resolve, 100));

            // Create new reader
            const reader = port.readable.getReader();

            // Read response with timeout
            const response = await Promise.race([
                readResponseCheckHW(reader),
                new Promise((_, reject) => setTimeout(() => reject(new Error("Timeout checking firmware")), 2000)),
            ]);

            // Ensure any existing reader is released
            if (reader) {
                await reader.cancel();
                await reader.releaseLock();
            }

            // Kiểm tra response có chứa "BeeBrain"
            if (response.includes("BeeBrain")) {
                const match = response.match(/v(\d+\.\d+\.\d+)/);
                if (match) {
                    setMessage("BeeBoard detected with version: " + match[1]);
                    // Found current version of BeeBrain
                    const currentVersion = match[1];
                    if (currentVersion !== BeeBoardVersion) {
                        alert("New firmware is available!\nPlease upgrade your BeE board!");
                    }
                } else {
                    alert("BeE board version not found!");
                    setMessage("BeE board version not found!");
                }
                return {
                    success: true,
                    isBeeBrain: true,
                    message: "BeeBrain firmware detected",
                    version: response.trim(),
                };
            } else {
                return {
                    success: true,
                    isBeeBrain: false,
                    message: "This is not BeeBrain firmware",
                    version: response.trim(),
                };
            }
        } catch (error) {
            console.error("Error checking hardware:", error);
            return {
                success: false,
                isBeeBrain: false,
                message: "Error checking firmware: " + error.message,
            };
        }
    };

    const handleUploadCodeBluetooth = async () => {
        // Kiểm tra nếu đang upload thì không cho upload nữa
        if (bluetoothUploading) {
            setMessage("Upload is already in progress...");
            return;
        }

        const main_path = "main.py";
        const code = project.pythonCode;

        try {
            setBluetoothUploading(true);
            setMessage("Connecting to Bluetooth...");
            await ensureBluetoothConnected();

            const adapter = bluetoothAdapter.current;
            if (!adapter || !adapter.isConnected) {
                throw new Error("Bluetooth adapter not connected");
            }

            const BLOCK = 2000;

            // Khởi tạo kết nối
            setMessage("Initializing Bluetooth upload...");
            await adapter.writeASCII("HELLO\n");
            await adapter.writeASCII("STATUS\n");
            await adapter.writeASCII("STOP\n");
            await delay(1000);

            // Upload module files từ các extensions trước
            for (const extensionId of loadedExtensions) {
                try {
                    const extension = EXTENSIONS.find((ext) => ext.id === extensionId);
                    const moduleFiles = extension?.modules || [];

                    if (moduleFiles.length === 0) continue;

                    setMessage(`Uploading modules for extension: ${extension.name}`);
                    console.log(`Uploading modules for extension ${extensionId}:`, moduleFiles);

                    for (const file of moduleFiles) {
                        try {
                            // Fetch module content
                            const moduleResponse = await fetch(
                                `/assets/BeeIDE/extensions/${extensionId}/modules/${file}`
                            );

                            if (!moduleResponse.ok) {
                                console.warn(`Failed to fetch module ${file} for extension ${extensionId}`);
                                continue;
                            }

                            const moduleContent = await moduleResponse.text();
                            const moduleData = new TextEncoder().encode(moduleContent);

                            setMessage(`Uploading module: ${file}`);
                            console.log(`Uploading module: ${file} (${moduleData.length} bytes)`);

                            // Upload module file
                            await adapter.writeASCII(`PUT ${file} ${moduleData.length} 0\n`);
                            for (let i = 0; i < moduleData.length; i += BLOCK) {
                                const slice = moduleData.subarray(i, Math.min(i + BLOCK, moduleData.length));
                                await adapter.writeASCII(`DATA ${slice.length}\n`);
                                await adapter.writeBuf20B(slice);
                            }
                            await adapter.writeASCII("END\n");
                            await delay(100); // Delay nhỏ giữa các file
                        } catch (fileError) {
                            console.error(`Error uploading module ${file}:`, fileError);
                            setMessage(`Warning: Failed to upload module ${file}`);
                        }
                    }
                } catch (extensionError) {
                    console.error(`Error processing extension ${extensionId}:`, extensionError);
                    setMessage(`Warning: Failed to process extension ${extensionId}`);
                }
            }

            // Upload main.py cuối cùng
            setMessage("Uploading main.py...");
            const mainData = new TextEncoder().encode(code);
            console.log(`Uploading main.py (${mainData.length} bytes)`);

            await adapter.writeASCII(`PUT ${main_path} ${mainData.length} 0\n`);
            for (let i = 0; i < mainData.length; i += BLOCK) {
                const slice = mainData.subarray(i, Math.min(i + BLOCK, mainData.length));
                await adapter.writeASCII(`DATA ${slice.length}\n`);
                await adapter.writeBuf20B(slice);
            }
            await adapter.writeASCII("END\n");

            // Chạy main.py
            setMessage("Running main.py...");
            await adapter.writeASCII(`RUN ${main_path}\n`);

            setMessage("Code and modules uploaded successfully via Bluetooth!");
        } catch (error) {
            console.error("Bluetooth upload error:", error);
            setMessage("Bluetooth upload failed: " + error.message);
            alert("Bluetooth upload failed: " + error.message);
        } finally {
            setBluetoothUploading(false);
        }
    };

    const handleUploadCodeSerial = async () => {
        // Kiểm tra nếu đang upload thì không cho upload nữa
        if (serialUploading) {
            setMessage("Upload is already in progress...");
            return;
        }

        setMessage("Uploading code");

        // Upload code through OTA
        if (project.pythonCode.includes("== OTA IP:")) {
            handleUploadCodeOTA();
            return;
        }

        // Check serial connection
        if (!project.serialConnected) {
            alert("Need to connect to serial first!");
            setMessage("Error: Need to connect to serial first!");
            return;
        }

        try {
            setSerialUploading(true);
            // Ensure any existing reader is released first
            if (project.reader) {
                await project.reader.cancel();
                await project.reader.releaseLock();
            }

            const code = project.pythonCode;
            const encoder = new TextEncoder();
            const decoder = new TextDecoder();

            // Reset REPL
            await project.writer.write(encoder.encode("\x03\x03")); // Ctrl-C twice to ensure we exit any running program
            await new Promise((resolve) => setTimeout(resolve, 100));

            // Upload module files if they exist for loaded extensions
            for (const extensionId of loadedExtensions) {
                try {
                    const modulesPath = `./extensions/${extensionId}/modules/`;
                    const extension = EXTENSIONS.find((ext) => ext.id === extensionId);
                    const moduleFiles = extension?.modules || [];

                    if (moduleFiles.length === 0) {
                        continue;
                    } else {
                        // Only log in development environment
                        if (import.meta.env.VITE_ENV === "dev") {
                            console.log(`Modules found for extension ${extensionId}`);
                        }
                    }

                    for (const file of moduleFiles) {
                        const moduleContent = await fetch(
                            `/assets/BeeIDE/extensions/${extensionId}/modules/${file}`
                        ).then((response) => response.text());
                        let writeModuleCommand = "";
                        const fileContent = moduleContent.default || moduleContent;
                        writeModuleCommand = `f = open('${file}', 'w')\nf.write(${JSON.stringify(fileContent)})\nf.close()\n`;
                        setMessage(`Uploading module: ${file}`);
                        const lines = writeModuleCommand.split("\n");
                        for (const line of lines) {
                            await project.writer.write(encoder.encode(line + "\r\n"));
                            await new Promise((resolve) => setTimeout(resolve, 100));
                        }
                    }
                } catch (error) {
                    console.error(`Found extension ${extensionId} but error: ${error.message}`);
                }
            }

            let writeFileCommand = "";

            if (project.pythonCode.includes("== OTA ==") || project.pythonCode.includes("== Serial ==")) {
                // Create and write boot.py file, set OTA/Serial upload
                setMessage("Upload to boot.py file");
                writeFileCommand = `f = open('boot.py', 'w')\nf.write(${JSON.stringify(code.replaceAll("%", "\\r").replaceAll("@", "\\n"))})\nf.close()\n`;
            } else {
                // Create and write main.py file
                setMessage("Upload to main.py file");
                writeFileCommand = `f = open('main.py', 'w')\nf.write(${JSON.stringify(code)})\nf.close()\n`;
            }

            // Split code into lines and send
            const lines = writeFileCommand.split("\n");
            for (const line of lines) {
                await project.writer.write(encoder.encode(line + "\r\n"));
                await new Promise((resolve) => setTimeout(resolve, 100));
            }
            // Soft reset to execute main.py
            await project.writer.write(encoder.encode("\x04")); // Ctrl-D for soft reset
            await new Promise((resolve) => setTimeout(resolve, 500));

            // Create new reader
            const newReader = project.port.readable.getReader();
            setProject((prev) => ({
                ...prev,
                reader: newReader,
            }));

            // Read response with timeout
            const response = await Promise.race([
                readResponse(),
                new Promise((_, reject) => setTimeout(() => reject(new Error("Timeout waiting for response")), 5000)),
            ]);

            // Kiểm tra response
            if (response.trim()) {
                // Chỉ hiển thị alert nếu có lỗi hoặc có output thực sự
                if (response.includes("Traceback")) {
                    alert(`Python Error:\n${response}`);
                    setMessage("Error: Python error");
                } else if (response !== "") {
                    alert(`Code executed successfully!\nOutput:\n${response}`);
                    setMessage("Code executed successfully");
                } else {
                    console.info("Code executed successfully (no output)");
                    setMessage("Code executed successfully");
                }
            } else {
                console.info("Code executed successfully (no output)");
                setMessage("Code executed successfully");
            }
        } catch (error) {
            console.error("Upload error:", error);
            // Chỉ hiển thị alert cho lỗi thực sự
            if (error.message.includes("Traceback")) {
                alert(`Python Error:\n${error.message}`);
            } else if (!error.message.includes("no output")) {
                alert(`Upload Error: ${error.message}`);
            }
            setMessage("Error: Cannot upload code");
        } finally {
            // Release the reader
            if (project.reader) {
                await project.reader.releaseLock();
            }
            setSerialUploading(false);
        }
    };

    const readResponseCheckHW = async (reader) => {
        let response = "";
        let errorFound = false;
        const decoder = new TextDecoder();

        try {
            while (true) {
                const { value, done } = await reader.read();
                if (done) break;

                // Decode và thêm vào response
                const text = decoder.decode(value);
                response += text;

                // Kiểm tra các loại lỗi Python phổ biến
                if (
                    text.includes("Traceback") ||
                    text.includes("NameError") ||
                    text.includes("SyntaxError") ||
                    text.includes("AttributeError") ||
                    text.includes("TypeError") ||
                    text.includes("ValueError")
                ) {
                    errorFound = true;
                }

                // Điều kiện dừng khi gặp prompt Python hoặc sau khi đã thu thập đủ thông tin lỗi
                if (response.includes(">>>") && (!errorFound || response.split(">>>").length > 1)) {
                    break;
                }

                // Timeout cho mỗi lần đọc
                await new Promise((resolve) => setTimeout(resolve, 10));
            }

            // Xử lý và format response
            response = response
                .trim()
                .replace(/>>>/g, "") // Loại bỏ prompt Python
                .replace(/\r\n/g, "\n") // Chuẩn hóa line endings
                .trim();

            // Chỉ throw error nếu thực sự có lỗi
            if (errorFound) {
                console.error("Python Error:", response);
                throw new Error(response);
            }

            return response;
        } catch (error) {
            if (error.message.includes("Traceback")) {
                console.error("Python Error:", error);
                // throw  error;
            }

            return response;
        }
    };

    const readResponse = async () => {
        let response = "";
        let errorFound = false;
        const decoder = new TextDecoder();

        try {
            while (true) {
                const { value, done } = await project.reader.read();
                if (done) break;

                // Decode và thêm vào response
                const text = decoder.decode(value);
                response += text;

                // Kiểm tra các loại lỗi Python phổ biến
                if (
                    text.includes("Traceback") ||
                    text.includes("NameError") ||
                    text.includes("SyntaxError") ||
                    text.includes("AttributeError") ||
                    text.includes("TypeError") ||
                    text.includes("ValueError")
                ) {
                    errorFound = true;
                }

                // Điều kiện dừng khi gặp prompt Python hoặc sau khi đã thu thập đủ thông tin lỗi
                if (response.includes(">>>") && (!errorFound || response.split(">>>").length > 1)) {
                    break;
                }

                // Timeout cho mỗi lần đọc
                await new Promise((resolve) => setTimeout(resolve, 10));
            }

            // Xử lý và format response
            response = response
                .trim()
                .replace(/>>>/g, "") // Loại bỏ prompt Python
                .replace(/\r\n/g, "\n") // Chuẩn hóa line endings
                .trim();

            // Chỉ throw error nếu thực sự có lỗi
            if (errorFound) {
                console.error("Python Error:", response);
                throw new Error(response);
            }

            return response;
        } catch (error) {
            if (error.message.includes("Traceback")) {
                console.error("Python Error:", error);
                // throw  error;
            }

            return response;
        }
    };

    const handleSave = () => {
        if (!project.blocksJson) {
            alert("Chưa có block nào được tạo!");
            return;
        }

        // Get the workspace state
        const workspaceState = {
            blocks: project.blocksJson,
            pythonCode: project.pythonCode,
            project: project.name,
        };

        // Convert to JSON string
        const jsonStr = JSON.stringify(workspaceState, null, 2);

        // Create blob and download link
        const blob = new Blob([jsonStr], { type: "application/json" });
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = `${project.name || "bee_project"}.json`;

        // Trigger download
        document.body.appendChild(link);
        link.click();

        // Cleanup
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
        setMessage("Project saved successfully");
    };

    const handleLoad = (event) => {
        const file = event.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const json = JSON.parse(e.target.result);
                if (json.blocks && workspaceRef.current) {
                    // Clear the current workspace
                    workspaceRef.current.clear();

                    // Load the saved blocks
                    Blockly.serialization.workspaces.load(json.blocks, workspaceRef.current);

                    // Update project name if it exists in the file
                    if (json.project) {
                        setProject((prev) => ({
                            ...prev,
                            name: json.project,
                            blocksJson: json.blocks,
                            pythonCode: json.pythonCode,
                        }));
                    }
                }
                setMessage("Project loaded successfully");
            } catch (error) {
                console.error("Error loading file:", error);
                // You might want to add some user feedback here
                setMessage("Error: Cannot load project");
            }
        };
        reader.readAsText(file);
    };

    if (import.meta.env.VITE_ENV === "dev") {
        console.log(project.pythonCode);
    }

    const setupSerialDisconnectMonitor = () => {
        if (!project.port || !project.serialConnected) return;

        // Tạo một hàm kiểm tra trạng thái kết nối
        const checkConnection = async () => {
            try {
                // Kiểm tra xem port có còn tồn tại không
                if (!navigator.serial.getPorts) {
                    alert("getPorts API is not supported, please change to Chrome or Edge!");
                    return;
                }

                const ports = await navigator.serial.getPorts();
                const portExists = ports.some((p) => p === project.port);

                if (!portExists && project.serialConnected) {
                    // Đóng kết nối hiện tại
                    try {
                        // Giải phóng writer nếu đang có
                        if (project.writer) {
                            await project.writer.close();
                        }

                        // Giải phóng reader nếu đang có
                        if (project.reader) {
                            await project.reader.cancel();
                            await project.reader.releaseLock();
                        }

                        // Đóng port
                        if (project.port) {
                            await project.port.close();
                        }
                    } catch (error) {
                        console.warn("Error closing serial connection:", error);
                    }

                    // Cập nhật trạng thái
                    setProject((prev) => ({
                        ...prev,
                        serialConnected: false,
                        port: null,
                        writer: null,
                        reader: null,
                    }));

                    setMessage("Serial disconnected");
                }
            } catch (error) {
                console.error("Error checking port status:", error);
            }
        };

        // Kiểm tra mỗi 2 giây
        const intervalId = setInterval(checkConnection, 2000);

        // Lưu ID của interval để có thể xóa khi cần
        return intervalId;
    };

    // Sửa đổi useEffect để theo dõi thay đổi trạng thái kết nối
    useEffect(() => {
        // Nếu kết nối được thiết lập, bắt đầu giám sát
        if (project.serialConnected && project.port) {
            const intervalId = setupSerialDisconnectMonitor();
            setSerialMonitorInterval(intervalId);
        } else {
            // Nếu ngắt kết nối, dừng giám sát
            if (serialMonitorInterval) {
                clearInterval(serialMonitorInterval);
                setSerialMonitorInterval(null);
            }
        }

        // Cleanup khi component unmount
        return () => {
            if (serialMonitorInterval) {
                clearInterval(serialMonitorInterval);
            }
        };
    }, [project.serialConnected, project.port]);

    return (
        <Box
            sx={{
                height: "100vh",
                display: "flex",
                flexDirection: "column",
                backgroundColor: "white",
                overflow: "hidden",
            }}
        >
            <FlashFirmwareModel
                project={project}
                setProject={setProject}
                open={openFlashFirmwareModal}
                handleClose={handleCloseFlashFirmwareModal}
            />
            <BeeAppBar
                user={user}
                setUser={setUser}
                project={project}
                setProject={setProject}
                handleConnectSerial={handleConnectSerial}
                handleConnectBluetooth={
                    bluetoothConnected
                        ? disconnectBluetooth
                        : () => {
                              // Chỉ hiển thị popup khi user click vào nút Connect
                              ensureBluetoothConnected().catch((error) => {
                                  console.error("Failed to connect Bluetooth:", error);
                              });
                          }
                }
                bluetoothConnected={bluetoothConnected}
                handleSave={handleSave}
                handleLoad={handleLoad}
                setOpenFlashFirmwareModal={setOpenFlashFirmwareModal}
                BeeBoardVersion={BeeBoardVersion}
                language={language}
                setLanguage={setLanguage}
            />
            <Box
                sx={{
                    display: "flex",
                    flexDirection: "column",
                    flexGrow: 1,
                    mt: "64px",
                    overflow: "hidden",
                }}
            >
                <Box
                    ref={blocklyDiv}
                    sx={{
                        flexGrow: 1,
                        minHeight: 0,
                    }}
                />
                <Tooltip title={serialUploading ? "Uploading..." : "Upload via Serial"}>
                    <IconButton
                        onClick={handleUploadCodeSerial}
                        disabled={!project.serialConnected || serialUploading}
                        sx={{
                            display: project.serialConnected ? "block" : "none",
                            position: "absolute",
                            top: "105px",
                            right: "30px",
                            width: "40px",
                            height: "40px",
                            transform: "scale(2)",
                            boxShadow: "0 0 6px rgb(0 0 0 / 60%)",
                            backgroundColor: "#212F3C",
                            "&:hover": {
                                backgroundColor: "#212F3C",
                            },
                            "&.Mui-disabled": {
                                backgroundColor: "#212F3C",
                                opacity: 0.1,
                                cursor: "not-allowed",
                            },
                        }}
                    >
                        {serialUploading ? (
                            <CircularProgress size={20} sx={{ color: "white" }} />
                        ) : (
                            <CableIcon sx={{ fontSize: "20px", color: "white" }} />
                        )}
                    </IconButton>
                </Tooltip>
                <Tooltip title={bluetoothUploading ? "Uploading..." : "Upload Bluetooth"}>
                    <IconButton
                        onClick={handleUploadCodeBluetooth}
                        disabled={!bluetoothConnected || bluetoothUploading}
                        sx={{
                            display: bluetoothConnected ? "block" : "none",
                            position: "absolute",
                            top: project.serialConnected ? "205px" : "105px",
                            right: "30px",
                            width: "40px",
                            height: "40px",
                            transform: "scale(2)",
                            boxShadow: "0 0 6px rgb(0 0 0 / 60%)",
                            backgroundColor: "#212F3C",
                            "&:hover": {
                                backgroundColor: "#212F3C",
                            },
                            "&.Mui-disabled": {
                                backgroundColor: "#212F3C",
                                opacity: 0.1,
                                cursor: "not-allowed",
                            },
                        }}
                    >
                        {bluetoothUploading ? (
                            <CircularProgress size={20} sx={{ color: "white" }} />
                        ) : (
                            <BluetoothIcon sx={{ fontSize: "20px", color: "white" }} />
                        )}
                    </IconButton>
                </Tooltip>
                <ExtensionModal
                    open={extensionModalOpen}
                    onClose={() => setExtensionModalOpen(false)}
                    onSelectExtension={handleExtensionSelect}
                    onUninstallExtension={handleExtensionUninstall}
                    loadedExtensions={loadedExtensions}
                />
                <Button
                    onClick={() => setExtensionModalOpen(true)}
                    sx={{
                        position: "fixed",
                        bottom: "21px",
                        left: "0px",
                        minWidth: "62px",
                        height: "62px",
                        backgroundColor: "#1a237e",
                        paddingLeft: "0px",
                        paddingRight: "0px",
                        zIndex: 100,
                        borderRadius: 0,
                        color: "white",
                    }}
                >
                    <ExtensionIcon />
                </Button>
                <Box
                    sx={{
                        height: "20px",
                        borderTop: "1px solid #ddd",
                        display: "flex",
                        alignItems: "center",
                        justifyItems: "center",
                        px: 2,
                        backgroundColor: "#f5f5f5",
                    }}
                >
                    <Typography variant="body2" sx={{ color: grey[500], fontSize: "12px" }}>
                        Serial:{" "}
                        {project.serialConnected ? (serialUploading ? "Uploading..." : "Connected") : "Disconnected"}
                    </Typography>
                    <Divider orientation="vertical" sx={{ mr: "10px", ml: "10px" }} flexItem />
                    <Typography variant="body2" sx={{ color: grey[500], fontSize: "12px" }}>
                        Bluetooth:{" "}
                        {bluetoothConnected ? (bluetoothUploading ? "Uploading..." : "Connected") : "Disconnected"}
                    </Typography>
                    <Divider orientation="vertical" sx={{ mr: "10px", ml: "10px" }} flexItem />
                    <Typography variant="body2" sx={{ color: grey[500], fontSize: "12px" }}>
                        Board: BeE board
                    </Typography>
                    <Divider orientation="vertical" sx={{ mr: "10px", ml: "10px" }} flexItem />
                    <Typography variant="body2" sx={{ color: grey[500], fontSize: "12px" }}>
                        Log: {message}
                    </Typography>
                </Box>
            </Box>
        </Box>
    );
};

export default BeeIDE;
