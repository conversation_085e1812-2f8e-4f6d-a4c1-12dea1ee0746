import React, { useState } from 'react';
import {
    <PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    Card,
    CardContent,
    CircularProgress,
    Alert,
    Grid,
    TextField,
    Snackbar
} from '@mui/material';
import { useTeacherAPI, useStudentAPI, useCommonAPI } from '../../../hooks/useAPI';

/**
 * Example component demonstrating how to use the API hooks
 * This is for demonstration purposes and can be removed in production
 */
function APIUsageExample() {
    const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' });
    const [courseId, setCourseId] = useState('');
    const [courseData, setCourseData] = useState({
        title: '',
        description: '',
        subject: 'Toán học',
        grade: 'Lớp 8',
        price: 0
    });

    // Teacher API hooks
    const teacherAPI = useTeacherAPI();
    const {
        data: courses,
        loading: coursesLoading,
        error: coursesError,
        execute: fetchCourses
    } = teacherAPI.useCourses();

    const {
        data: dashboardStats,
        loading: statsLoading,
        execute: fetchStats
    } = teacherAPI.useDashboardStats();

    const {
        loading: createLoading,
        execute: createCourse
    } = teacherAPI.useCreateCourse({
        onSuccess: (result) => {
            setSnackbar({
                open: true,
                message: 'Tạo khóa học thành công!',
                severity: 'success'
            });
            fetchCourses(); // Refresh courses list
        },
        onError: (error) => {
            setSnackbar({
                open: true,
                message: 'Tạo khóa học thất bại!',
                severity: 'error'
            });
        }
    });

    // Student API hooks
    const studentAPI = useStudentAPI();
    const {
        data: availableCourses,
        loading: availableCoursesLoading,
        execute: fetchAvailableCourses
    } = studentAPI.useAvailableCourses();

    const {
        data: enrolledCourses,
        loading: enrolledCoursesLoading,
        execute: fetchEnrolledCourses
    } = studentAPI.useEnrolledCourses();

    const {
        loading: purchaseLoading,
        execute: purchaseCourse
    } = studentAPI.usePurchaseCourse({
        onSuccess: (result) => {
            setSnackbar({
                open: true,
                message: 'Mua khóa học thành công!',
                severity: 'success'
            });
            fetchEnrolledCourses(); // Refresh enrolled courses
        }
    });

    // Common API hooks
    const { uploadFile, uploadProgress } = useCommonAPI();

    // Event handlers
    const handleCreateCourse = async () => {
        try {
            await createCourse(courseData);
        } catch (error) {
            console.error('Create course error:', error);
        }
    };

    const handlePurchaseCourse = async (courseId) => {
        try {
            await purchaseCourse(courseId, {
                payment_method: 'credit_card',
                // Add other payment data as needed
            });
        } catch (error) {
            console.error('Purchase course error:', error);
        }
    };

    const handleFileUpload = async (event) => {
        const file = event.target.files[0];
        if (file) {
            try {
                const result = await uploadFile(file, 'course_thumbnails');
                setSnackbar({
                    open: true,
                    message: 'Tải file lên thành công!',
                    severity: 'success'
                });
                console.log('Upload result:', result);
            } catch (error) {
                setSnackbar({
                    open: true,
                    message: 'Tải file lên thất bại!',
                    severity: 'error'
                });
            }
        }
    };

    return (
        <Box sx={{ p: 3 }}>
            <Typography variant="h4" gutterBottom>
                API Usage Examples
            </Typography>
            <Typography variant="body1" color="textSecondary" sx={{ mb: 4 }}>
                Đây là các ví dụ về cách sử dụng API hooks trong ứng dụng
            </Typography>

            <Grid container spacing={3}>
                {/* Teacher API Examples */}
                <Grid item xs={12} md={6}>
                    <Card sx={{ borderRadius: '10px' }}>
                        <CardContent>
                            <Typography variant="h6" gutterBottom>
                                Teacher API Examples
                            </Typography>

                            {/* Dashboard Stats */}
                            <Box sx={{ mb: 3 }}>
                                <Typography variant="subtitle1" gutterBottom>
                                    Dashboard Statistics
                                </Typography>
                                <Button
                                    variant="outlined"
                                    onClick={fetchStats}
                                    disabled={statsLoading}
                                    sx={{ mb: 2 }}
                                >
                                    {statsLoading ? <CircularProgress size={20} /> : 'Fetch Stats'}
                                </Button>
                                {dashboardStats && (
                                    <Alert severity="info">
                                        Total Courses: {dashboardStats.total_courses || 'N/A'}
                                    </Alert>
                                )}
                            </Box>

                            {/* Courses List */}
                            <Box sx={{ mb: 3 }}>
                                <Typography variant="subtitle1" gutterBottom>
                                    Courses List
                                </Typography>
                                <Button
                                    variant="outlined"
                                    onClick={fetchCourses}
                                    disabled={coursesLoading}
                                    sx={{ mb: 2 }}
                                >
                                    {coursesLoading ? <CircularProgress size={20} /> : 'Fetch Courses'}
                                </Button>
                                {coursesError && (
                                    <Alert severity="error" sx={{ mb: 2 }}>
                                        {coursesError}
                                    </Alert>
                                )}
                                {courses && (
                                    <Alert severity="success">
                                        Found {courses.length} courses
                                    </Alert>
                                )}
                            </Box>

                            {/* Create Course */}
                            <Box sx={{ mb: 3 }}>
                                <Typography variant="subtitle1" gutterBottom>
                                    Create Course
                                </Typography>
                                <TextField
                                    fullWidth
                                    label="Course Title"
                                    value={courseData.title}
                                    onChange={(e) => setCourseData({ ...courseData, title: e.target.value })}
                                    sx={{ mb: 2 }}
                                />
                                <TextField
                                    fullWidth
                                    label="Description"
                                    multiline
                                    rows={3}
                                    value={courseData.description}
                                    onChange={(e) => setCourseData({ ...courseData, description: e.target.value })}
                                    sx={{ mb: 2 }}
                                />
                                <TextField
                                    fullWidth
                                    label="Price (VND)"
                                    type="number"
                                    value={courseData.price}
                                    onChange={(e) => setCourseData({ ...courseData, price: parseInt(e.target.value) || 0 })}
                                    sx={{ mb: 2 }}
                                />
                                <Button
                                    variant="contained"
                                    onClick={handleCreateCourse}
                                    disabled={createLoading || !courseData.title}
                                    fullWidth
                                >
                                    {createLoading ? <CircularProgress size={20} /> : 'Create Course'}
                                </Button>
                            </Box>
                        </CardContent>
                    </Card>
                </Grid>

                {/* Student API Examples */}
                <Grid item xs={12} md={6}>
                    <Card sx={{ borderRadius: '10px' }}>
                        <CardContent>
                            <Typography variant="h6" gutterBottom>
                                Student API Examples
                            </Typography>

                            {/* Available Courses */}
                            <Box sx={{ mb: 3 }}>
                                <Typography variant="subtitle1" gutterBottom>
                                    Available Courses
                                </Typography>
                                <Button
                                    variant="outlined"
                                    onClick={fetchAvailableCourses}
                                    disabled={availableCoursesLoading}
                                    sx={{ mb: 2 }}
                                >
                                    {availableCoursesLoading ? <CircularProgress size={20} /> : 'Fetch Available'}
                                </Button>
                                {availableCourses && (
                                    <Alert severity="info">
                                        Found {availableCourses.length} available courses
                                    </Alert>
                                )}
                            </Box>

                            {/* Enrolled Courses */}
                            <Box sx={{ mb: 3 }}>
                                <Typography variant="subtitle1" gutterBottom>
                                    Enrolled Courses
                                </Typography>
                                <Button
                                    variant="outlined"
                                    onClick={fetchEnrolledCourses}
                                    disabled={enrolledCoursesLoading}
                                    sx={{ mb: 2 }}
                                >
                                    {enrolledCoursesLoading ? <CircularProgress size={20} /> : 'Fetch Enrolled'}
                                </Button>
                                {enrolledCourses && (
                                    <Alert severity="success">
                                        Enrolled in {enrolledCourses.length} courses
                                    </Alert>
                                )}
                            </Box>

                            {/* Purchase Course */}
                            <Box sx={{ mb: 3 }}>
                                <Typography variant="subtitle1" gutterBottom>
                                    Purchase Course
                                </Typography>
                                <TextField
                                    fullWidth
                                    label="Course ID"
                                    value={courseId}
                                    onChange={(e) => setCourseId(e.target.value)}
                                    sx={{ mb: 2 }}
                                />
                                <Button
                                    variant="contained"
                                    onClick={() => handlePurchaseCourse(courseId)}
                                    disabled={purchaseLoading || !courseId}
                                    fullWidth
                                >
                                    {purchaseLoading ? <CircularProgress size={20} /> : 'Purchase Course'}
                                </Button>
                            </Box>
                        </CardContent>
                    </Card>
                </Grid>

                {/* File Upload Example */}
                <Grid item xs={12}>
                    <Card sx={{ borderRadius: '10px' }}>
                        <CardContent>
                            <Typography variant="h6" gutterBottom>
                                File Upload Example
                            </Typography>
                            <input
                                type="file"
                                onChange={handleFileUpload}
                                style={{ marginBottom: '16px' }}
                            />
                            {uploadProgress > 0 && (
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                                    <CircularProgress variant="determinate" value={uploadProgress} />
                                    <Typography variant="body2">
                                        {uploadProgress}%
                                    </Typography>
                                </Box>
                            )}
                        </CardContent>
                    </Card>
                </Grid>
            </Grid>

            {/* Snackbar for notifications */}
            <Snackbar
                open={snackbar.open}
                autoHideDuration={6000}
                onClose={() => setSnackbar({ ...snackbar, open: false })}
            >
                <Alert
                    onClose={() => setSnackbar({ ...snackbar, open: false })}
                    severity={snackbar.severity}
                    sx={{ width: '100%' }}
                >
                    {snackbar.message}
                </Alert>
            </Snackbar>
        </Box>
    );
}

export default APIUsageExample;
