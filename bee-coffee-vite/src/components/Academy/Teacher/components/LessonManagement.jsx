import React, { useState, useEffect } from 'react';
import {
    <PERSON>,
    <PERSON>po<PERSON>,
    Button,
    Paper,
    Card,
    CardContent,
    CardActions,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    TextField,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Chip,
    IconButton,
    Tooltip,
    Alert,
    Snackbar,
    Tabs,
    Tab,
    List,
    ListItem,
    ListItemText,
    ListItemSecondaryAction,
    Divider,
    Avatar,
    Badge,
    FormControlLabel,
    Switch,
    LinearProgress
} from '@mui/material';
import Grid from "@mui/material/Grid2";
import {
    Add as AddIcon,
    Edit as EditIcon,
    Delete as DeleteIcon,
    Visibility as ViewIcon,
    VideoLibrary as VideoIcon,
    AttachFile as FileIcon,
    Create as CreateIcon,
    Publish as PublishIcon,
    Schedule as ScheduleIcon,
    Class as ClassIcon,
    PlayArrow as PlayIcon,
    Download as DownloadIcon,
    Share as ShareIcon
} from '@mui/icons-material';

function TabPanel({ children, value, index, ...other }) {
    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`lesson-tabpanel-${index}`}
            aria-labelledby={`lesson-tab-${index}`}
            {...other}
        >
            {value === index && (
                <Box sx={{ p: 3 }}>
                    {children}
                </Box>
            )}
        </div>
    );
}

function LessonManagement({ user }) {
    const [lessons, setLessons] = useState([]);
    const [classes, setClasses] = useState([]);
    const [openDialog, setOpenDialog] = useState(false);
    const [editingLesson, setEditingLesson] = useState(null);
    const [tabValue, setTabValue] = useState(0);
    const [contentType, setContentType] = useState('text');
    const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

    // Form state
    const [formData, setFormData] = useState({
        title: '',
        description: '',
        classId: '',
        contentType: 'text', // text, video, file
        content: '',
        videoUrl: '',
        fileName: '',
        fileUrl: '',
        duration: 60,
        isPublished: false,
        scheduledDate: '',
        tags: ''
    });

    const contentTypes = [
        { value: 'text', label: 'Đánh trực tiếp', icon: <CreateIcon /> },
        { value: 'video', label: 'Link video', icon: <VideoIcon /> },
        { value: 'file', label: 'Upload file', icon: <FileIcon /> }
    ];

    useEffect(() => {
        // Giả lập dữ liệu lớp học
        const mockClasses = [
            { id: 1, name: 'Toán học nâng cao 8A', subject: 'Toán học' },
            { id: 2, name: 'Vật lý thí nghiệm 9B', subject: 'Vật lý' },
            { id: 3, name: 'Lập trình Scratch 7C', subject: 'Tin học' }
        ];
        setClasses(mockClasses);

        // Giả lập dữ liệu bài giảng
        const mockLessons = [
            {
                id: 1,
                title: 'Phương trình bậc hai',
                description: 'Học cách giải phương trình bậc hai và ứng dụng',
                classId: 1,
                className: 'Toán học nâng cao 8A',
                contentType: 'text',
                content: 'Phương trình bậc hai có dạng ax² + bx + c = 0...',
                duration: 45,
                isPublished: true,
                scheduledDate: '2024-01-15',
                tags: 'toán học, phương trình',
                createdAt: '2024-01-10',
                views: 25,
                status: 'published'
            },
            {
                id: 2,
                title: 'Định luật Newton',
                description: 'Tìm hiểu về 3 định luật Newton trong vật lý',
                classId: 2,
                className: 'Vật lý thí nghiệm 9B',
                contentType: 'video',
                videoUrl: 'https://youtube.com/watch?v=example',
                duration: 30,
                isPublished: true,
                scheduledDate: '2024-01-16',
                tags: 'vật lý, định luật',
                createdAt: '2024-01-11',
                views: 18,
                status: 'published'
            },
            {
                id: 3,
                title: 'Lập trình game đơn giản',
                description: 'Hướng dẫn tạo game đơn giản với Scratch',
                classId: 3,
                className: 'Lập trình Scratch 7C',
                contentType: 'file',
                fileName: 'scratch-game-tutorial.pdf',
                fileUrl: '/files/scratch-game-tutorial.pdf',
                duration: 60,
                isPublished: false,
                scheduledDate: '2024-01-20',
                tags: 'scratch, game, lập trình',
                createdAt: '2024-01-12',
                views: 0,
                status: 'draft'
            }
        ];
        setLessons(mockLessons);
    }, []);

    const handleOpenDialog = (lesson = null) => {
        if (lesson) {
            setEditingLesson(lesson);
            setFormData({
                title: lesson.title,
                description: lesson.description,
                classId: lesson.classId,
                contentType: lesson.contentType,
                content: lesson.content || '',
                videoUrl: lesson.videoUrl || '',
                fileName: lesson.fileName || '',
                fileUrl: lesson.fileUrl || '',
                duration: lesson.duration,
                isPublished: lesson.isPublished,
                scheduledDate: lesson.scheduledDate,
                tags: lesson.tags
            });
            setContentType(lesson.contentType);
        } else {
            setEditingLesson(null);
            setFormData({
                title: '',
                description: '',
                classId: '',
                contentType: 'text',
                content: '',
                videoUrl: '',
                fileName: '',
                fileUrl: '',
                duration: 60,
                isPublished: false,
                scheduledDate: '',
                tags: ''
            });
            setContentType('text');
        }
        setOpenDialog(true);
    };

    const handleCloseDialog = () => {
        setOpenDialog(false);
        setEditingLesson(null);
    };

    const handleFormChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const handleContentTypeChange = (type) => {
        setContentType(type);
        setFormData(prev => ({
            ...prev,
            contentType: type
        }));
    };

    const handleSaveLesson = () => {
        if (!formData.title || !formData.classId) {
            setSnackbar({
                open: true,
                message: 'Vui lòng điền đầy đủ thông tin bắt buộc!',
                severity: 'error'
            });
            return;
        }

        // Validate content based on type
        if (formData.contentType === 'text' && !formData.content) {
            setSnackbar({
                open: true,
                message: 'Vui lòng nhập nội dung bài giảng!',
                severity: 'error'
            });
            return;
        }

        if (formData.contentType === 'video' && !formData.videoUrl) {
            setSnackbar({
                open: true,
                message: 'Vui lòng nhập link video!',
                severity: 'error'
            });
            return;
        }

        const selectedClass = classes.find(c => c.id === formData.classId);
        const lessonData = {
            ...formData,
            id: editingLesson ? editingLesson.id : Date.now(),
            className: selectedClass?.name || '',
            createdAt: editingLesson ? editingLesson.createdAt : new Date().toISOString().split('T')[0],
            views: editingLesson ? editingLesson.views : 0,
            status: formData.isPublished ? 'published' : 'draft'
        };

        if (editingLesson) {
            setLessons(prev => prev.map(l => l.id === editingLesson.id ? lessonData : l));
            setSnackbar({
                open: true,
                message: 'Cập nhật bài giảng thành công!',
                severity: 'success'
            });
        } else {
            setLessons(prev => [...prev, lessonData]);
            setSnackbar({
                open: true,
                message: 'Tạo bài giảng thành công!',
                severity: 'success'
            });
        }

        handleCloseDialog();
    };

    const handleDeleteLesson = (id) => {
        if (window.confirm('Bạn có chắc chắn muốn xóa bài giảng này?')) {
            setLessons(prev => prev.filter(l => l.id !== id));
            setSnackbar({
                open: true,
                message: 'Xóa bài giảng thành công!',
                severity: 'success'
            });
        }
    };

    const handleTogglePublish = (id) => {
        setLessons(prev => prev.map(l =>
            l.id === id
                ? {
                    ...l,
                    isPublished: !l.isPublished,
                    status: !l.isPublished ? 'published' : 'draft'
                }
                : l
        ));
        setSnackbar({
            open: true,
            message: 'Cập nhật trạng thái thành công!',
            severity: 'success'
        });
    };

    const getStatusChip = (status) => {
        return status === 'published'
            ? <Chip label="Đã xuất bản" color="success" size="small" />
            : <Chip label="Bản nháp" color="warning" size="small" />;
    };

    const getContentTypeIcon = (type) => {
        const typeInfo = contentTypes.find(t => t.value === type);
        return typeInfo?.icon || <CreateIcon />;
    };

    const getContentTypeLabel = (type) => {
        const typeInfo = contentTypes.find(t => t.value === type);
        return typeInfo?.label || 'Không xác định';
    };

    const renderContentInput = () => {
        switch (contentType) {
            case 'text':
                return (
                    <TextField
                        fullWidth
                        label="Nội dung bài giảng *"
                        multiline
                        rows={8}
                        value={formData.content}
                        onChange={(e) => handleFormChange('content', e.target.value)}
                        placeholder="Nhập nội dung bài giảng..."
                    />
                );
            case 'video':
                return (
                    <TextField
                        fullWidth
                        label="Link video *"
                        value={formData.videoUrl}
                        onChange={(e) => handleFormChange('videoUrl', e.target.value)}
                        placeholder="https://youtube.com/watch?v=..."
                        helperText="Hỗ trợ YouTube, Vimeo, và các link video khác"
                    />
                );
            case 'file':
                return (
                    <Box>
                        <TextField
                            fullWidth
                            label="Tên file"
                            value={formData.fileName}
                            onChange={(e) => handleFormChange('fileName', e.target.value)}
                            sx={{ mb: 2 }}
                        />
                        <Button
                            variant="outlined"
                            component="label"
                            startIcon={<FileIcon />}
                            fullWidth
                        >
                            Chọn file
                            <input
                                type="file"
                                hidden
                                accept=".pdf,.doc,.docx,.ppt,.pptx"
                                onChange={(e) => {
                                    const file = e.target.files[0];
                                    if (file) {
                                        handleFormChange('fileName', file.name);
                                        // Trong thực tế, sẽ upload file lên server
                                        handleFormChange('fileUrl', `/files/${file.name}`);
                                    }
                                }}
                            />
                        </Button>
                        {formData.fileName && (
                            <Alert severity="info" sx={{ mt: 2 }}>
                                File đã chọn: {formData.fileName}
                            </Alert>
                        )}
                    </Box>
                );
            default:
                return null;
        }
    };

    return (
        <Box>
            {/* Header */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h4">
                    Quản lý bài giảng
                </Typography>
                <Button
                    sx={{ borderRadius: "10px" }}
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={() => handleOpenDialog()}
                >
                    Tạo bài giảng mới
                </Button>
            </Box>

            {/* Statistics */}
            <Grid container spacing={3} sx={{ mb: 3 }}>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Card sx={{ borderRadius: "10px" }}>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Tổng bài giảng
                            </Typography>
                            <Typography variant="h4">
                                {lessons.length}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Card sx={{ borderRadius: "10px" }}>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Đã xuất bản
                            </Typography>
                            <Typography variant="h4" color="success.main">
                                {lessons.filter(l => l.status === 'published').length}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Card sx={{ borderRadius: "10px" }}>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Tổng lượt xem
                            </Typography>
                            <Typography variant="h4">
                                {lessons.reduce((sum, l) => sum + l.views, 0)}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Card sx={{ borderRadius: "10px" }}>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Thời lượng TB
                            </Typography>
                            <Typography variant="h4">
                                {lessons.length > 0
                                    ? Math.round(lessons.reduce((sum, l) => sum + l.duration, 0) / lessons.length)
                                    : 0
                                } phút
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
            </Grid>

            {/* Lessons Grid */}
            <Grid container spacing={3}>
                {lessons.map((lesson) => (
                    <Grid size={{ xs: 12, md: 6, lg: 4 }} key={lesson.id}>
                        <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column', borderRadius: "10px" }}>
                            <CardContent sx={{ flexGrow: 1 }}>
                                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                                    <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
                                        {lesson.title}
                                    </Typography>
                                    {getContentTypeIcon(lesson.contentType)}
                                </Box>

                                <Box sx={{ mb: 2 }}>
                                    <Chip
                                        label={lesson.className}
                                        variant="outlined"
                                        size="small"
                                        sx={{ mr: 1, mb: 1 }}
                                    />
                                    {getStatusChip(lesson.status)}
                                </Box>

                                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                                    {lesson.description}
                                </Typography>

                                <Box sx={{ mb: 2 }}>
                                    <Typography variant="body2" sx={{ mb: 1 }}>
                                        <strong>Loại:</strong> {getContentTypeLabel(lesson.contentType)}
                                    </Typography>
                                    <Typography variant="body2" sx={{ mb: 1 }}>
                                        <strong>Thời lượng:</strong> {lesson.duration} phút
                                    </Typography>
                                    <Typography variant="body2" sx={{ mb: 1 }}>
                                        <strong>Lượt xem:</strong> {lesson.views}
                                    </Typography>
                                </Box>

                                {lesson.tags && (
                                    <Typography variant="caption" color="textSecondary">
                                        Tags: {lesson.tags}
                                    </Typography>
                                )}
                            </CardContent>

                            <CardActions>
                                <Tooltip title="Xem">
                                    <IconButton size="small">
                                        <ViewIcon />
                                    </IconButton>
                                </Tooltip>
                                <Tooltip title="Chỉnh sửa">
                                    <IconButton
                                        size="small"
                                        onClick={() => handleOpenDialog(lesson)}
                                    >
                                        <EditIcon />
                                    </IconButton>
                                </Tooltip>
                                <Tooltip title={lesson.status === 'published' ? 'Hủy xuất bản' : 'Xuất bản'}>
                                    <IconButton
                                        size="small"
                                        color={lesson.status === 'published' ? 'warning' : 'success'}
                                        onClick={() => handleTogglePublish(lesson.id)}
                                    >
                                        <PublishIcon />
                                    </IconButton>
                                </Tooltip>
                                <Tooltip title="Xóa">
                                    <IconButton
                                        size="small"
                                        color="error"
                                        onClick={() => handleDeleteLesson(lesson.id)}
                                    >
                                        <DeleteIcon />
                                    </IconButton>
                                </Tooltip>
                            </CardActions>
                        </Card>
                    </Grid>
                ))}
            </Grid>

            {/* Add/Edit Lesson Dialog */}
            <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="lg" fullWidth>
                <DialogTitle>
                    {editingLesson ? 'Chỉnh sửa bài giảng' : 'Tạo bài giảng mới'}
                </DialogTitle>
                <DialogContent>
                    <Grid container spacing={2} sx={{ mt: 1 }}>
                        <Grid size={{ xs: 12 }}>
                            <TextField
                                fullWidth
                                label="Tiêu đề bài giảng *"
                                value={formData.title}
                                onChange={(e) => handleFormChange('title', e.target.value)}
                            />
                        </Grid>

                        <Grid size={{ xs: 12 }}>
                            <TextField
                                fullWidth
                                label="Mô tả"
                                multiline
                                rows={2}
                                value={formData.description}
                                onChange={(e) => handleFormChange('description', e.target.value)}
                            />
                        </Grid>

                        <Grid size={{ xs: 12, sm: 6 }}>
                            <FormControl fullWidth>
                                <InputLabel>Lớp học *</InputLabel>
                                <Select
                                    value={formData.classId}
                                    onChange={(e) => handleFormChange('classId', e.target.value)}
                                    label="Lớp học *"
                                >
                                    {classes.map(cls => (
                                        <MenuItem key={cls.id} value={cls.id}>{cls.name}</MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        </Grid>

                        <Grid size={{ xs: 12, sm: 6 }}>
                            <TextField
                                fullWidth
                                label="Thời lượng (phút)"
                                type="number"
                                value={formData.duration}
                                onChange={(e) => handleFormChange('duration', parseInt(e.target.value))}
                                inputProps={{ min: 1, max: 300 }}
                            />
                        </Grid>

                        {/* Content Type Selection */}
                        <Grid size={{ xs: 12 }}>
                            <Typography variant="h6" gutterBottom>
                                Loại nội dung
                            </Typography>
                            <Grid container spacing={2}>
                                {contentTypes.map((type) => (
                                    <Grid size={{ xs: 12, sm: 4 }} key={type.value}>
                                        <Card
                                            sx={{
                                                cursor: 'pointer',
                                                border: contentType === type.value ? 2 : 1,
                                                borderColor: contentType === type.value ? 'primary.main' : 'divider',
                                                bgcolor: contentType === type.value ? 'primary.50' : 'background.paper'
                                            }}
                                            onClick={() => handleContentTypeChange(type.value)}
                                        >
                                            <CardContent sx={{ textAlign: 'center', py: 2 }}>
                                                {type.icon}
                                                <Typography variant="body2" sx={{ mt: 1 }}>
                                                    {type.label}
                                                </Typography>
                                            </CardContent>
                                        </Card>
                                    </Grid>
                                ))}
                            </Grid>
                        </Grid>

                        {/* Content Input */}
                        <Grid size={{ xs: 12 }}>
                            {renderContentInput()}
                        </Grid>

                        <Grid size={{ xs: 12, sm: 6 }}>
                            <TextField
                                fullWidth
                                label="Ngày xuất bản"
                                type="date"
                                value={formData.scheduledDate}
                                onChange={(e) => handleFormChange('scheduledDate', e.target.value)}
                                InputLabelProps={{ shrink: true }}
                            />
                        </Grid>

                        <Grid size={{ xs: 12, sm: 6 }}>
                            <TextField
                                fullWidth
                                label="Tags (phân cách bằng dấu phẩy)"
                                value={formData.tags}
                                onChange={(e) => handleFormChange('tags', e.target.value)}
                                placeholder="ví dụ: toán học, cơ bản"
                            />
                        </Grid>

                        <Grid size={{ xs: 12 }}>
                            <FormControlLabel
                                control={
                                    <Switch
                                        checked={formData.isPublished}
                                        onChange={(e) => handleFormChange('isPublished', e.target.checked)}
                                    />
                                }
                                label="Xuất bản ngay"
                            />
                        </Grid>
                    </Grid>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseDialog} sx={{ borderRadius: "10px" }}>Hủy</Button>
                    <Button onClick={handleSaveLesson} variant="contained" sx={{ borderRadius: "10px" }}>
                        {editingLesson ? 'Cập nhật' : 'Tạo bài giảng'}
                    </Button>
                </DialogActions>
            </Dialog>

            {/* Snackbar */}
            <Snackbar
                open={snackbar.open}
                autoHideDuration={6000}
                onClose={() => setSnackbar({ ...snackbar, open: false })}
            >
                <Alert
                    onClose={() => setSnackbar({ ...snackbar, open: false })}
                    severity={snackbar.severity}
                    sx={{ width: '100%' }}
                >
                    {snackbar.message}
                </Alert>
            </Snackbar>
        </Box>
    );
}

export default LessonManagement;
