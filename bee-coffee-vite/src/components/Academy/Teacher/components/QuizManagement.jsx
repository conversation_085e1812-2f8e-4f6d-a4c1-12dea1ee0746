import React, { useState, useEffect } from 'react';
import {
    <PERSON>,
    Typo<PERSON>,
    Button,
    Paper,
    Grid,
    Card,
    CardContent,
    CardActions,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    TextField,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Chip,
    IconButton,
    Tooltip,
    Alert,
    Snackbar,
    List,
    ListItem,
    ListItemText,
    ListItemSecondaryAction,
    Checkbox,
    Divider,
    Avatar,
    Badge,
    FormControlLabel,
    Switch,
    Stepper,
    Step,
    StepLabel,
    StepContent
} from '@mui/material';
import {
    Add as AddIcon,
    Edit as EditIcon,
    Delete as DeleteIcon,
    Quiz as QuizIcon,
    QuestionAnswer as QuestionIcon,
    Timer as TimerIcon,
    People as PeopleIcon,
    Assessment as AssessmentIcon,
    Visibility as ViewIcon,
    PlayArrow as StartIcon,
    Stop as StopIcon,
    Schedule as ScheduleIcon,
    CheckCircle as CheckIcon
} from '@mui/icons-material';

function QuizManagement({ user }) {
    const [quizzes, setQuizzes] = useState([]);
    const [questions, setQuestions] = useState([]);
    const [classes, setClasses] = useState([]);
    const [openDialog, setOpenDialog] = useState(false);
    const [editingQuiz, setEditingQuiz] = useState(null);
    const [selectedQuestions, setSelectedQuestions] = useState([]);
    const [activeStep, setActiveStep] = useState(0);
    const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

    // Form state
    const [formData, setFormData] = useState({
        title: '',
        description: '',
        classId: '',
        duration: 60,
        totalQuestions: 10,
        passingScore: 60,
        isRandomOrder: true,
        isPublished: false,
        scheduledDate: '',
        endDate: '',
        allowRetake: false,
        showResults: true
    });

    const steps = ['Thông tin cơ bản', 'Chọn câu hỏi', 'Cài đặt', 'Xem trước'];

    useEffect(() => {
        // Giả lập dữ liệu lớp học
        const mockClasses = [
            { id: 1, name: 'Toán học nâng cao 8A', subject: 'Toán học' },
            { id: 2, name: 'Vật lý thí nghiệm 9B', subject: 'Vật lý' },
            { id: 3, name: 'Lập trình Scratch 7C', subject: 'Tin học' }
        ];
        setClasses(mockClasses);

        // Giả lập ngân hàng câu hỏi
        const mockQuestions = [
            {
                id: 1,
                question: 'Kết quả của phép tính 2 + 2 là gì?',
                options: ['3', '4', '5', '6'],
                correctAnswer: 1,
                subject: 'Toán học',
                difficulty: 'easy'
            },
            {
                id: 2,
                question: 'Công thức tính diện tích hình tròn là gì?',
                options: ['πr²', '2πr', 'πd', 'r²'],
                correctAnswer: 0,
                subject: 'Toán học',
                difficulty: 'medium'
            },
            {
                id: 3,
                question: 'Đơn vị đo lực trong hệ SI là gì?',
                options: ['Joule', 'Newton', 'Watt', 'Pascal'],
                correctAnswer: 1,
                subject: 'Vật lý',
                difficulty: 'medium'
            }
        ];
        setQuestions(mockQuestions);

        // Giả lập dữ liệu bài kiểm tra
        const mockQuizzes = [
            {
                id: 1,
                title: 'Kiểm tra Toán học giữa kỳ',
                description: 'Bài kiểm tra toán học cho học sinh lớp 8A',
                classId: 1,
                className: 'Toán học nâng cao 8A',
                duration: 45,
                totalQuestions: 10,
                passingScore: 70,
                isPublished: true,
                scheduledDate: '2024-01-20',
                endDate: '2024-01-20',
                participants: 22,
                completed: 18,
                avgScore: 75.5,
                status: 'active',
                createdAt: '2024-01-15'
            },
            {
                id: 2,
                title: 'Bài kiểm tra Vật lý',
                description: 'Kiểm tra kiến thức vật lý cơ bản',
                classId: 2,
                className: 'Vật lý thí nghiệm 9B',
                duration: 30,
                totalQuestions: 8,
                passingScore: 60,
                isPublished: false,
                scheduledDate: '2024-01-25',
                endDate: '2024-01-25',
                participants: 0,
                completed: 0,
                avgScore: 0,
                status: 'draft',
                createdAt: '2024-01-16'
            }
        ];
        setQuizzes(mockQuizzes);
    }, []);

    const handleOpenDialog = (quiz = null) => {
        if (quiz) {
            setEditingQuiz(quiz);
            setFormData({
                title: quiz.title,
                description: quiz.description,
                classId: quiz.classId,
                duration: quiz.duration,
                totalQuestions: quiz.totalQuestions,
                passingScore: quiz.passingScore,
                isRandomOrder: quiz.isRandomOrder || true,
                isPublished: quiz.isPublished,
                scheduledDate: quiz.scheduledDate,
                endDate: quiz.endDate,
                allowRetake: quiz.allowRetake || false,
                showResults: quiz.showResults !== false
            });
        } else {
            setEditingQuiz(null);
            setFormData({
                title: '',
                description: '',
                classId: '',
                duration: 60,
                totalQuestions: 10,
                passingScore: 60,
                isRandomOrder: true,
                isPublished: false,
                scheduledDate: '',
                endDate: '',
                allowRetake: false,
                showResults: true
            });
        }
        setSelectedQuestions([]);
        setActiveStep(0);
        setOpenDialog(true);
    };

    const handleCloseDialog = () => {
        setOpenDialog(false);
        setEditingQuiz(null);
        setActiveStep(0);
    };

    const handleFormChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const handleNext = () => {
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
    };

    const handleBack = () => {
        setActiveStep((prevActiveStep) => prevActiveStep - 1);
    };

    const handleQuestionToggle = (questionId) => {
        setSelectedQuestions(prev => {
            if (prev.includes(questionId)) {
                return prev.filter(id => id !== questionId);
            } else {
                return [...prev, questionId];
            }
        });
    };

    const handleSaveQuiz = () => {
        if (!formData.title || !formData.classId || selectedQuestions.length === 0) {
            setSnackbar({
                open: true,
                message: 'Vui lòng điền đầy đủ thông tin và chọn ít nhất 1 câu hỏi!',
                severity: 'error'
            });
            return;
        }

        const selectedClass = classes.find(c => c.id === formData.classId);
        const quizData = {
            ...formData,
            id: editingQuiz ? editingQuiz.id : Date.now(),
            className: selectedClass?.name || '',
            questions: selectedQuestions,
            participants: editingQuiz ? editingQuiz.participants : 0,
            completed: editingQuiz ? editingQuiz.completed : 0,
            avgScore: editingQuiz ? editingQuiz.avgScore : 0,
            status: formData.isPublished ? 'active' : 'draft',
            createdAt: editingQuiz ? editingQuiz.createdAt : new Date().toISOString().split('T')[0]
        };

        if (editingQuiz) {
            setQuizzes(prev => prev.map(q => q.id === editingQuiz.id ? quizData : q));
            setSnackbar({
                open: true,
                message: 'Cập nhật bài kiểm tra thành công!',
                severity: 'success'
            });
        } else {
            setQuizzes(prev => [...prev, quizData]);
            setSnackbar({
                open: true,
                message: 'Tạo bài kiểm tra thành công!',
                severity: 'success'
            });
        }

        handleCloseDialog();
    };

    const handleDeleteQuiz = (id) => {
        if (window.confirm('Bạn có chắc chắn muốn xóa bài kiểm tra này?')) {
            setQuizzes(prev => prev.filter(q => q.id !== id));
            setSnackbar({
                open: true,
                message: 'Xóa bài kiểm tra thành công!',
                severity: 'success'
            });
        }
    };

    const handleTogglePublish = (id) => {
        setQuizzes(prev => prev.map(q => 
            q.id === id 
                ? { 
                    ...q, 
                    isPublished: !q.isPublished,
                    status: !q.isPublished ? 'active' : 'draft'
                }
                : q
        ));
        setSnackbar({
            open: true,
            message: 'Cập nhật trạng thái thành công!',
            severity: 'success'
        });
    };

    const getStatusChip = (status) => {
        const statusMap = {
            'active': { label: 'Đang diễn ra', color: 'success' },
            'draft': { label: 'Bản nháp', color: 'warning' },
            'completed': { label: 'Đã kết thúc', color: 'info' }
        };
        const statusInfo = statusMap[status] || statusMap.draft;
        return <Chip label={statusInfo.label} color={statusInfo.color} size="small" />;
    };

    const getDifficultyColor = (difficulty) => {
        const colors = {
            'easy': 'success',
            'medium': 'warning',
            'hard': 'error'
        };
        return colors[difficulty] || 'default';
    };

    const renderStepContent = (step) => {
        switch (step) {
            case 0:
                return (
                    <Grid container spacing={2}>
                        <Grid item xs={12}>
                            <TextField
                                fullWidth
                                label="Tiêu đề bài kiểm tra *"
                                value={formData.title}
                                onChange={(e) => handleFormChange('title', e.target.value)}
                            />
                        </Grid>
                        <Grid item xs={12}>
                            <TextField
                                fullWidth
                                label="Mô tả"
                                multiline
                                rows={3}
                                value={formData.description}
                                onChange={(e) => handleFormChange('description', e.target.value)}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                            <FormControl fullWidth>
                                <InputLabel>Lớp học *</InputLabel>
                                <Select
                                    value={formData.classId}
                                    onChange={(e) => handleFormChange('classId', e.target.value)}
                                    label="Lớp học *"
                                >
                                    {classes.map(cls => (
                                        <MenuItem key={cls.id} value={cls.id}>{cls.name}</MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                            <TextField
                                fullWidth
                                label="Thời gian làm bài (phút)"
                                type="number"
                                value={formData.duration}
                                onChange={(e) => handleFormChange('duration', parseInt(e.target.value))}
                                inputProps={{ min: 5, max: 300 }}
                            />
                        </Grid>
                    </Grid>
                );
            case 1:
                const filteredQuestions = formData.classId 
                    ? questions.filter(q => {
                        const selectedClass = classes.find(c => c.id === formData.classId);
                        return selectedClass && q.subject === selectedClass.subject;
                    })
                    : questions;

                return (
                    <Box>
                        <Typography variant="h6" gutterBottom>
                            Chọn câu hỏi từ ngân hàng ({selectedQuestions.length} đã chọn)
                        </Typography>
                        <List>
                            {filteredQuestions.map((question) => (
                                <React.Fragment key={question.id}>
                                    <ListItem>
                                        <Checkbox
                                            checked={selectedQuestions.includes(question.id)}
                                            onChange={() => handleQuestionToggle(question.id)}
                                        />
                                        <ListItemText
                                            primary={question.question}
                                            secondary={
                                                <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
                                                    <Chip 
                                                        label={question.subject} 
                                                        size="small" 
                                                        variant="outlined" 
                                                    />
                                                    <Chip 
                                                        label={question.difficulty} 
                                                        size="small" 
                                                        color={getDifficultyColor(question.difficulty)}
                                                    />
                                                </Box>
                                            }
                                        />
                                    </ListItem>
                                    <Divider />
                                </React.Fragment>
                            ))}
                        </List>
                        {filteredQuestions.length === 0 && (
                            <Alert severity="info">
                                Không có câu hỏi nào phù hợp với môn học của lớp đã chọn.
                            </Alert>
                        )}
                    </Box>
                );
            case 2:
                return (
                    <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                            <TextField
                                fullWidth
                                label="Điểm đạt (%)"
                                type="number"
                                value={formData.passingScore}
                                onChange={(e) => handleFormChange('passingScore', parseInt(e.target.value))}
                                inputProps={{ min: 0, max: 100 }}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                            <TextField
                                fullWidth
                                label="Ngày bắt đầu"
                                type="datetime-local"
                                value={formData.scheduledDate}
                                onChange={(e) => handleFormChange('scheduledDate', e.target.value)}
                                InputLabelProps={{ shrink: true }}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                            <TextField
                                fullWidth
                                label="Ngày kết thúc"
                                type="datetime-local"
                                value={formData.endDate}
                                onChange={(e) => handleFormChange('endDate', e.target.value)}
                                InputLabelProps={{ shrink: true }}
                            />
                        </Grid>
                        <Grid item xs={12}>
                            <FormControlLabel
                                control={
                                    <Switch
                                        checked={formData.isRandomOrder}
                                        onChange={(e) => handleFormChange('isRandomOrder', e.target.checked)}
                                    />
                                }
                                label="Trộn thứ tự câu hỏi"
                            />
                        </Grid>
                        <Grid item xs={12}>
                            <FormControlLabel
                                control={
                                    <Switch
                                        checked={formData.allowRetake}
                                        onChange={(e) => handleFormChange('allowRetake', e.target.checked)}
                                    />
                                }
                                label="Cho phép làm lại"
                            />
                        </Grid>
                        <Grid item xs={12}>
                            <FormControlLabel
                                control={
                                    <Switch
                                        checked={formData.showResults}
                                        onChange={(e) => handleFormChange('showResults', e.target.checked)}
                                    />
                                }
                                label="Hiển thị kết quả ngay"
                            />
                        </Grid>
                        <Grid item xs={12}>
                            <FormControlLabel
                                control={
                                    <Switch
                                        checked={formData.isPublished}
                                        onChange={(e) => handleFormChange('isPublished', e.target.checked)}
                                    />
                                }
                                label="Xuất bản ngay"
                            />
                        </Grid>
                    </Grid>
                );
            case 3:
                return (
                    <Box>
                        <Typography variant="h6" gutterBottom>
                            Xem trước bài kiểm tra
                        </Typography>
                        <Paper sx={{ p: 2, mb: 2 }}>
                            <Typography variant="h6">{formData.title}</Typography>
                            <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
                                {formData.description}
                            </Typography>
                            <Grid container spacing={2}>
                                <Grid item xs={6}>
                                    <Typography variant="body2">
                                        <strong>Lớp:</strong> {classes.find(c => c.id === formData.classId)?.name}
                                    </Typography>
                                </Grid>
                                <Grid item xs={6}>
                                    <Typography variant="body2">
                                        <strong>Thời gian:</strong> {formData.duration} phút
                                    </Typography>
                                </Grid>
                                <Grid item xs={6}>
                                    <Typography variant="body2">
                                        <strong>Số câu hỏi:</strong> {selectedQuestions.length}
                                    </Typography>
                                </Grid>
                                <Grid item xs={6}>
                                    <Typography variant="body2">
                                        <strong>Điểm đạt:</strong> {formData.passingScore}%
                                    </Typography>
                                </Grid>
                            </Grid>
                        </Paper>
                        <Typography variant="subtitle1" gutterBottom>
                            Câu hỏi đã chọn:
                        </Typography>
                        <List>
                            {selectedQuestions.map((questionId, index) => {
                                const question = questions.find(q => q.id === questionId);
                                return question ? (
                                    <ListItem key={questionId}>
                                        <ListItemText
                                            primary={`${index + 1}. ${question.question}`}
                                            secondary={
                                                <Chip 
                                                    label={question.difficulty} 
                                                    size="small" 
                                                    color={getDifficultyColor(question.difficulty)}
                                                />
                                            }
                                        />
                                    </ListItem>
                                ) : null;
                            })}
                        </List>
                    </Box>
                );
            default:
                return null;
        }
    };

    return (
        <Box>
            {/* Header */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h4">
                    Bài kiểm tra trắc nghiệm
                </Typography>
                <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={() => handleOpenDialog()}
                >
                    Tạo bài kiểm tra
                </Button>
            </Box>

            {/* Statistics */}
            <Grid container spacing={3} sx={{ mb: 3 }}>
                <Grid item xs={12} sm={6} md={3}>
                    <Card>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Tổng bài kiểm tra
                            </Typography>
                            <Typography variant="h4">
                                {quizzes.length}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                    <Card>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Đang diễn ra
                            </Typography>
                            <Typography variant="h4" color="success.main">
                                {quizzes.filter(q => q.status === 'active').length}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                    <Card>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Tổng thí sinh
                            </Typography>
                            <Typography variant="h4">
                                {quizzes.reduce((sum, q) => sum + q.participants, 0)}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                    <Card>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Điểm TB
                            </Typography>
                            <Typography variant="h4">
                                {quizzes.length > 0 
                                    ? (quizzes.reduce((sum, q) => sum + q.avgScore, 0) / quizzes.length).toFixed(1)
                                    : '0.0'
                                }
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
            </Grid>

            {/* Quizzes Grid */}
            <Grid container spacing={3}>
                {quizzes.map((quiz) => (
                    <Grid item xs={12} md={6} lg={4} key={quiz.id}>
                        <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                            <CardContent sx={{ flexGrow: 1 }}>
                                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                                    <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
                                        {quiz.title}
                                    </Typography>
                                    <QuizIcon />
                                </Box>
                                
                                <Box sx={{ mb: 2 }}>
                                    <Chip
                                        label={quiz.className}
                                        variant="outlined"
                                        size="small"
                                        sx={{ mr: 1, mb: 1 }}
                                    />
                                    {getStatusChip(quiz.status)}
                                </Box>

                                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                                    {quiz.description}
                                </Typography>

                                <Box sx={{ mb: 2 }}>
                                    <Typography variant="body2" sx={{ mb: 1 }}>
                                        <TimerIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                        {quiz.duration} phút • {quiz.totalQuestions} câu hỏi
                                    </Typography>
                                    <Typography variant="body2" sx={{ mb: 1 }}>
                                        <PeopleIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                        {quiz.completed}/{quiz.participants} đã hoàn thành
                                    </Typography>
                                    <Typography variant="body2">
                                        <AssessmentIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                        Điểm TB: {quiz.avgScore.toFixed(1)}
                                    </Typography>
                                </Box>
                            </CardContent>
                            
                            <CardActions>
                                <Tooltip title="Xem kết quả">
                                    <IconButton size="small">
                                        <ViewIcon />
                                    </IconButton>
                                </Tooltip>
                                <Tooltip title="Chỉnh sửa">
                                    <IconButton
                                        size="small"
                                        onClick={() => handleOpenDialog(quiz)}
                                    >
                                        <EditIcon />
                                    </IconButton>
                                </Tooltip>
                                <Tooltip title={quiz.status === 'active' ? 'Dừng' : 'Bắt đầu'}>
                                    <IconButton
                                        size="small"
                                        color={quiz.status === 'active' ? 'warning' : 'success'}
                                        onClick={() => handleTogglePublish(quiz.id)}
                                    >
                                        {quiz.status === 'active' ? <StopIcon /> : <StartIcon />}
                                    </IconButton>
                                </Tooltip>
                                <Tooltip title="Xóa">
                                    <IconButton
                                        size="small"
                                        color="error"
                                        onClick={() => handleDeleteQuiz(quiz.id)}
                                    >
                                        <DeleteIcon />
                                    </IconButton>
                                </Tooltip>
                            </CardActions>
                        </Card>
                    </Grid>
                ))}
            </Grid>

            {/* Create/Edit Quiz Dialog */}
            <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="lg" fullWidth>
                <DialogTitle>
                    {editingQuiz ? 'Chỉnh sửa bài kiểm tra' : 'Tạo bài kiểm tra mới'}
                </DialogTitle>
                <DialogContent>
                    <Stepper activeStep={activeStep} orientation="vertical">
                        {steps.map((label, index) => (
                            <Step key={label}>
                                <StepLabel>{label}</StepLabel>
                                <StepContent>
                                    {renderStepContent(index)}
                                    <Box sx={{ mb: 2, mt: 2 }}>
                                        <Button
                                            variant="contained"
                                            onClick={index === steps.length - 1 ? handleSaveQuiz : handleNext}
                                            sx={{ mr: 1 }}
                                        >
                                            {index === steps.length - 1 ? 'Tạo bài kiểm tra' : 'Tiếp tục'}
                                        </Button>
                                        <Button
                                            disabled={index === 0}
                                            onClick={handleBack}
                                        >
                                            Quay lại
                                        </Button>
                                    </Box>
                                </StepContent>
                            </Step>
                        ))}
                    </Stepper>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseDialog}>Hủy</Button>
                </DialogActions>
            </Dialog>

            {/* Snackbar */}
            <Snackbar
                open={snackbar.open}
                autoHideDuration={6000}
                onClose={() => setSnackbar({ ...snackbar, open: false })}
            >
                <Alert
                    onClose={() => setSnackbar({ ...snackbar, open: false })}
                    severity={snackbar.severity}
                    sx={{ width: '100%' }}
                >
                    {snackbar.message}
                </Alert>
            </Snackbar>
        </Box>
    );
}

export default QuizManagement;
