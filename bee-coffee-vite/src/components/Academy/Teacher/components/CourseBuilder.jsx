import React, { useState, useEffect } from 'react';
import RichTextEditor from './RichTextEditor';
import CoursePreviewPage from './CoursePreviewPage';
import {
    Box,
    Typography,
    Button,
    Paper,
    Grid,
    Card,
    CardContent,
    CardActions,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    TextField,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Chip,
    IconButton,
    Tooltip,
    Alert,
    Snackbar,
    List,
    ListItem,
    ListItemText,
    ListItemIcon,
    ListItemSecondaryAction,
    Divider,
    Stepper,
    Step,
    StepLabel,
    StepContent,
    Accordion,
    AccordionSummary,
    AccordionDetails,
    Tab,
    Tabs
} from '@mui/material';
import {
    Add as AddIcon,
    Edit as EditIcon,
    Delete as DeleteIcon,
    DragIndicator as DragIcon,
    MenuBook as LessonIcon,
    Quiz as QuizIcon,
    Games as TurboWarpIcon,
    VideoLibrary as VideoIcon,
    AttachFile as FileIcon,
    Create as TextIcon,
    ExpandMore as ExpandMoreIcon,
    Save as SaveIcon,
    Preview as PreviewIcon,
    ArrowUpward as UpIcon,
    ArrowDownward as DownIcon
} from '@mui/icons-material';

function TabPanel({ children, value, index, ...other }) {
    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`builder-tabpanel-${index}`}
            aria-labelledby={`builder-tab-${index}`}
            {...other}
        >
            {value === index && (
                <Box sx={{ p: 3 }}>
                    {children}
                </Box>
            )}
        </div>
    );
}

function CourseBuilder({ courseId, onClose }) {
    const [course, setCourse] = useState(null);
    const [courseContent, setCourseContent] = useState([]);
    const [openDialog, setOpenDialog] = useState(false);
    const [dialogType, setDialogType] = useState('lesson'); // lesson, quiz, turbowarp
    const [editingItem, setEditingItem] = useState(null);
    const [tabValue, setTabValue] = useState(0);
    const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
    const [showPreview, setShowPreview] = useState(false);

    // Form state for content items
    const [formData, setFormData] = useState({
        title: '',
        description: '',
        contentType: 'text', // text, video, file
        content: '',
        videoUrl: '',
        fileName: '',
        fileUrl: '',
        duration: 30,
        questions: [],
        requiredFeatures: [],
        gradingCriteria: []
    });

    const contentTypes = [
        { value: 'text', label: 'Nội dung văn bản', icon: <TextIcon /> },
        { value: 'video', label: 'Video', icon: <VideoIcon /> },
        { value: 'file', label: 'Tài liệu', icon: <FileIcon /> }
    ];

    useEffect(() => {
        // Giả lập load dữ liệu khóa học
        const mockCourse = {
            id: courseId,
            title: 'Toán học nâng cao lớp 8',
            description: 'Khóa học toán nâng cao dành cho học sinh khá giỏi lớp 8'
        };

        const mockContent = [
            {
                id: 1,
                type: 'lesson',
                title: 'Giới thiệu phương trình bậc hai',
                description: 'Tìm hiểu khái niệm và dạng tổng quát của phương trình bậc hai',
                contentType: 'text',
                content: `
                    <h2>Phương trình bậc hai</h2>
                    <p>Phương trình bậc hai là phương trình có dạng tổng quát:</p>
                    <p style="text-align: center; font-size: 18px; font-weight: bold; color: #1976d2;">
                        ax² + bx + c = 0 (với a ≠ 0)
                    </p>
                    <h3>Các thành phần:</h3>
                    <ul>
                        <li><strong>a, b, c</strong>: là các hệ số (a ≠ 0)</li>
                        <li><strong>x</strong>: là ẩn số cần tìm</li>
                    </ul>
                    <h3>Công thức nghiệm:</h3>
                    <p>Δ = b² - 4ac</p>
                    <ul>
                        <li>Nếu <strong>Δ > 0</strong>: phương trình có 2 nghiệm phân biệt</li>
                        <li>Nếu <strong>Δ = 0</strong>: phương trình có nghiệm kép</li>
                        <li>Nếu <strong>Δ < 0</strong>: phương trình vô nghiệm</li>
                    </ul>
                    <h3>Ví dụ:</h3>
                    <p>Giải phương trình: x² - 5x + 6 = 0</p>
                    <p>Ta có: a = 1, b = -5, c = 6</p>
                    <p>Δ = (-5)² - 4.1.6 = 25 - 24 = 1 > 0</p>
                    <p>Vậy phương trình có 2 nghiệm phân biệt:</p>
                    <p>x₁ = (5 + 1)/2 = 3</p>
                    <p>x₂ = (5 - 1)/2 = 2</p>
                `,
                duration: 45,
                order: 1,
                isPublished: true
            },
            {
                id: 2,
                type: 'lesson',
                title: 'Video hướng dẫn giải phương trình',
                description: 'Video minh họa cách giải phương trình bậc hai',
                contentType: 'video',
                videoUrl: 'https://youtube.com/watch?v=example',
                duration: 30,
                order: 2,
                isPublished: true
            },
            {
                id: 3,
                type: 'quiz',
                title: 'Kiểm tra phương trình bậc hai',
                description: 'Bài kiểm tra kiến thức về phương trình bậc hai',
                questions: [
                    {
                        question: 'Phương trình x² - 5x + 6 = 0 có nghiệm là?',
                        options: ['x = 2, x = 3', 'x = 1, x = 6', 'x = -2, x = -3', 'Vô nghiệm'],
                        correctAnswer: 0
                    }
                ],
                duration: 20,
                order: 3,
                isPublished: false
            },
            {
                id: 4,
                type: 'turbowarp',
                title: 'Tạo máy tính giải phương trình',
                description: 'Lập trình máy tính giải phương trình bậc hai bằng Scratch',
                requiredFeatures: ['User input', 'Mathematical calculations', 'Result display'],
                gradingCriteria: ['Functionality', 'User interface', 'Code organization'],
                order: 4,
                isPublished: false
            }
        ];

        setCourse(mockCourse);
        setCourseContent(mockContent);
    }, [courseId]);

    const handleAddContent = (type) => {
        setDialogType(type);
        setEditingItem(null);
        setFormData({
            title: '',
            description: '',
            contentType: 'text',
            content: '',
            videoUrl: '',
            fileName: '',
            fileUrl: '',
            duration: 30,
            questions: [],
            requiredFeatures: [],
            gradingCriteria: []
        });
        setTabValue(0);
        setOpenDialog(true);
    };

    const handleEditContent = (item) => {
        setDialogType(item.type);
        setEditingItem(item);
        setFormData({
            title: item.title,
            description: item.description,
            contentType: item.contentType || 'text',
            content: item.content || '',
            videoUrl: item.videoUrl || '',
            fileName: item.fileName || '',
            fileUrl: item.fileUrl || '',
            duration: item.duration || 30,
            questions: item.questions || [],
            requiredFeatures: item.requiredFeatures || [],
            gradingCriteria: item.gradingCriteria || []
        });
        setTabValue(0);
        setOpenDialog(true);
    };

    const handleCloseDialog = () => {
        setOpenDialog(false);
        setEditingItem(null);
    };

    const handleFormChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const handleSaveContent = () => {
        if (!formData.title || !formData.description) {
            setSnackbar({
                open: true,
                message: 'Vui lòng điền đầy đủ thông tin!',
                severity: 'error'
            });
            return;
        }

        const contentData = {
            ...formData,
            id: editingItem ? editingItem.id : Date.now(),
            type: dialogType,
            order: editingItem ? editingItem.order : courseContent.length + 1,
            isPublished: editingItem ? editingItem.isPublished : false
        };

        if (editingItem) {
            setCourseContent(prev => prev.map(item =>
                item.id === editingItem.id ? contentData : item
            ));
            setSnackbar({
                open: true,
                message: 'Cập nhật nội dung thành công!',
                severity: 'success'
            });
        } else {
            setCourseContent(prev => [...prev, contentData]);
            setSnackbar({
                open: true,
                message: 'Thêm nội dung thành công!',
                severity: 'success'
            });
        }

        handleCloseDialog();
    };

    const handleDeleteContent = (id) => {
        if (window.confirm('Bạn có chắc chắn muốn xóa nội dung này?')) {
            setCourseContent(prev => prev.filter(item => item.id !== id));
            setSnackbar({
                open: true,
                message: 'Xóa nội dung thành công!',
                severity: 'success'
            });
        }
    };

    const handleMoveContent = (id, direction) => {
        const currentIndex = courseContent.findIndex(item => item.id === id);
        if (
            (direction === 'up' && currentIndex === 0) ||
            (direction === 'down' && currentIndex === courseContent.length - 1)
        ) {
            return;
        }

        const newContent = [...courseContent];
        const targetIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;

        // Swap items
        [newContent[currentIndex], newContent[targetIndex]] = [newContent[targetIndex], newContent[currentIndex]];

        // Update order
        newContent.forEach((item, index) => {
            item.order = index + 1;
        });

        setCourseContent(newContent);
    };

    const getContentIcon = (type) => {
        switch (type) {
            case 'lesson': return <LessonIcon />;
            case 'quiz': return <QuizIcon />;
            case 'turbowarp': return <TurboWarpIcon />;
            default: return <LessonIcon />;
        }
    };

    const getContentTypeIcon = (contentType) => {
        const typeInfo = contentTypes.find(t => t.value === contentType);
        return typeInfo?.icon || <TextIcon />;
    };

    const renderContentForm = () => {
        switch (dialogType) {
            case 'lesson':
                return (
                    <Box>
                        <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
                            <Tab label="Thông tin cơ bản" />
                            <Tab label="Nội dung" />
                        </Tabs>

                        <TabPanel value={tabValue} index={0}>
                            <Grid container spacing={2}>
                                <Grid item xs={12}>
                                    <TextField
                                        fullWidth
                                        label="Tiêu đề bài học *"
                                        value={formData.title}
                                        onChange={(e) => handleFormChange('title', e.target.value)}
                                    />
                                </Grid>
                                <Grid item xs={12}>
                                    <TextField
                                        fullWidth
                                        label="Mô tả"
                                        multiline
                                        rows={2}
                                        value={formData.description}
                                        onChange={(e) => handleFormChange('description', e.target.value)}
                                    />
                                </Grid>
                                <Grid item xs={12} sm={6}>
                                    <TextField
                                        fullWidth
                                        label="Thời lượng (phút)"
                                        type="number"
                                        value={formData.duration}
                                        onChange={(e) => handleFormChange('duration', parseInt(e.target.value))}
                                    />
                                </Grid>
                                <Grid item xs={12} sm={6}>
                                    <FormControl fullWidth>
                                        <InputLabel>Loại nội dung</InputLabel>
                                        <Select
                                            value={formData.contentType}
                                            onChange={(e) => handleFormChange('contentType', e.target.value)}
                                            label="Loại nội dung"
                                        >
                                            {contentTypes.map(type => (
                                                <MenuItem key={type.value} value={type.value}>
                                                    {type.label}
                                                </MenuItem>
                                            ))}
                                        </Select>
                                    </FormControl>
                                </Grid>
                            </Grid>
                        </TabPanel>

                        <TabPanel value={tabValue} index={1}>
                            {formData.contentType === 'text' && (
                                <RichTextEditor
                                    label="Nội dung bài học"
                                    value={formData.content}
                                    onChange={(data) => handleFormChange('content', data)}
                                    height={400}
                                />
                            )}
                            {formData.contentType === 'video' && (
                                <TextField
                                    fullWidth
                                    label="URL Video"
                                    value={formData.videoUrl}
                                    onChange={(e) => handleFormChange('videoUrl', e.target.value)}
                                    placeholder="https://youtube.com/watch?v=..."
                                />
                            )}
                            {formData.contentType === 'file' && (
                                <Box>
                                    <TextField
                                        fullWidth
                                        label="Tên file"
                                        value={formData.fileName}
                                        onChange={(e) => handleFormChange('fileName', e.target.value)}
                                        sx={{ mb: 2 }}
                                    />
                                    <Button variant="outlined" component="label">
                                        Chọn file
                                        <input type="file" hidden />
                                    </Button>
                                </Box>
                            )}
                        </TabPanel>
                    </Box>
                );

            case 'quiz':
                return (
                    <Grid container spacing={2}>
                        <Grid item xs={12}>
                            <TextField
                                fullWidth
                                label="Tiêu đề bài kiểm tra *"
                                value={formData.title}
                                onChange={(e) => handleFormChange('title', e.target.value)}
                            />
                        </Grid>
                        <Grid item xs={12}>
                            <TextField
                                fullWidth
                                label="Mô tả"
                                multiline
                                rows={2}
                                value={formData.description}
                                onChange={(e) => handleFormChange('description', e.target.value)}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                            <TextField
                                fullWidth
                                label="Thời gian (phút)"
                                type="number"
                                value={formData.duration}
                                onChange={(e) => handleFormChange('duration', parseInt(e.target.value))}
                            />
                        </Grid>
                        <Grid item xs={12}>
                            <Alert severity="info">
                                Câu hỏi sẽ được chọn từ ngân hàng câu hỏi khi tạo bài kiểm tra
                            </Alert>
                        </Grid>
                    </Grid>
                );

            case 'turbowarp':
                return (
                    <Grid container spacing={2}>
                        <Grid item xs={12}>
                            <TextField
                                fullWidth
                                label="Tiêu đề bài tập *"
                                value={formData.title}
                                onChange={(e) => handleFormChange('title', e.target.value)}
                            />
                        </Grid>
                        <Grid item xs={12}>
                            <TextField
                                fullWidth
                                label="Mô tả"
                                multiline
                                rows={3}
                                value={formData.description}
                                onChange={(e) => handleFormChange('description', e.target.value)}
                            />
                        </Grid>
                        <Grid item xs={12}>
                            <TextField
                                fullWidth
                                label="Tính năng yêu cầu"
                                multiline
                                rows={2}
                                value={formData.requiredFeatures.join('\n')}
                                onChange={(e) => handleFormChange('requiredFeatures', e.target.value.split('\n').filter(f => f.trim()))}
                                placeholder="Mỗi tính năng một dòng..."
                            />
                        </Grid>
                        <Grid item xs={12}>
                            <TextField
                                fullWidth
                                label="Tiêu chí chấm điểm"
                                multiline
                                rows={2}
                                value={formData.gradingCriteria.join('\n')}
                                onChange={(e) => handleFormChange('gradingCriteria', e.target.value.split('\n').filter(c => c.trim()))}
                                placeholder="Mỗi tiêu chí một dòng..."
                            />
                        </Grid>
                    </Grid>
                );

            default:
                return null;
        }
    };

    if (!course) {
        return <Typography>Đang tải...</Typography>;
    }

    // Hiển thị CoursePreviewPage nếu được chọn
    if (showPreview) {
        return (
            <CoursePreviewPage
                courseId={courseId}
                onClose={() => setShowPreview(false)}
            />
        );
    }

    return (
        <Box>
            {/* Header */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Box>
                    <Typography variant="h4" gutterBottom>
                        Xây dựng khóa học
                    </Typography>
                    <Typography variant="h6" color="textSecondary">
                        {course.title}
                    </Typography>
                </Box>
                <Box>
                    <Button
                        variant="outlined"
                        onClick={onClose}
                        sx={{ mr: 2, borderRadius: '10px' }}
                    >
                        Quay lại
                    </Button>
                    <Button
                        variant="outlined"
                        startIcon={<PreviewIcon />}
                        onClick={() => setShowPreview(true)}
                        sx={{ mr: 2, borderRadius: '10px' }}
                    >
                        Xem trước
                    </Button>
                    <Button
                        variant="contained"
                        startIcon={<SaveIcon />}
                        sx={{ borderRadius: '10px' }}
                    >
                        Lưu khóa học
                    </Button>
                </Box>
            </Box>

            {/* Add Content Buttons */}
            <Paper sx={{ p: 2, mb: 3, borderRadius: '10px' }}>
                <Typography variant="h6" gutterBottom>
                    Thêm nội dung
                </Typography>
                <Box sx={{ display: 'flex', gap: 2 }}>
                    <Button
                        variant="outlined"
                        startIcon={<LessonIcon />}
                        onClick={() => handleAddContent('lesson')}
                        sx={{ borderRadius: '10px' }}
                    >
                        Bài học
                    </Button>
                    <Button
                        variant="outlined"
                        startIcon={<QuizIcon />}
                        onClick={() => handleAddContent('quiz')}
                        sx={{ borderRadius: '10px' }}
                    >
                        Bài kiểm tra
                    </Button>
                    <Button
                        variant="outlined"
                        startIcon={<TurboWarpIcon />}
                        onClick={() => handleAddContent('turbowarp')}
                        sx={{ borderRadius: '10px' }}
                    >
                        Bài tập TurboWarp
                    </Button>
                </Box>
            </Paper>

            {/* Course Content */}
            <Paper sx={{ p: 2, borderRadius: '10px' }}>
                <Typography variant="h6" gutterBottom>
                    Nội dung khóa học ({courseContent.length} mục)
                </Typography>

                {courseContent.length === 0 ? (
                    <Alert severity="info">
                        Chưa có nội dung nào. Hãy thêm bài học hoặc bài tập để bắt đầu xây dựng khóa học.
                    </Alert>
                ) : (
                    <List>
                        {courseContent
                            .sort((a, b) => a.order - b.order)
                            .map((item, index) => (
                                <React.Fragment key={item.id}>
                                    <ListItem>
                                        <ListItemIcon>
                                            <DragIcon />
                                        </ListItemIcon>
                                        <ListItemIcon>
                                            {getContentIcon(item.type)}
                                        </ListItemIcon>
                                        <ListItemText
                                            primary={
                                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                                    <Typography variant="body1">
                                                        {item.order}. {item.title}
                                                    </Typography>
                                                    {item.type === 'lesson' && getContentTypeIcon(item.contentType)}
                                                    {!item.isPublished && (
                                                        <Chip label="Bản nháp" size="small" color="warning" />
                                                    )}
                                                </Box>
                                            }
                                            secondary={
                                                <Box>
                                                    <Typography variant="body2" color="textSecondary">
                                                        {item.description}
                                                    </Typography>
                                                    {item.duration && (
                                                        <Typography variant="caption" color="textSecondary">
                                                            Thời lượng: {item.duration} phút
                                                        </Typography>
                                                    )}
                                                </Box>
                                            }
                                        />
                                        <ListItemSecondaryAction>
                                            <Tooltip title="Di chuyển lên">
                                                <IconButton
                                                    size="small"
                                                    onClick={() => handleMoveContent(item.id, 'up')}
                                                    disabled={index === 0}
                                                >
                                                    <UpIcon />
                                                </IconButton>
                                            </Tooltip>
                                            <Tooltip title="Di chuyển xuống">
                                                <IconButton
                                                    size="small"
                                                    onClick={() => handleMoveContent(item.id, 'down')}
                                                    disabled={index === courseContent.length - 1}
                                                >
                                                    <DownIcon />
                                                </IconButton>
                                            </Tooltip>
                                            <Tooltip title="Chỉnh sửa">
                                                <IconButton
                                                    size="small"
                                                    onClick={() => handleEditContent(item)}
                                                >
                                                    <EditIcon />
                                                </IconButton>
                                            </Tooltip>
                                            <Tooltip title="Xóa">
                                                <IconButton
                                                    size="small"
                                                    color="error"
                                                    onClick={() => handleDeleteContent(item.id)}
                                                >
                                                    <DeleteIcon />
                                                </IconButton>
                                            </Tooltip>
                                        </ListItemSecondaryAction>
                                    </ListItem>
                                    {index < courseContent.length - 1 && <Divider />}
                                </React.Fragment>
                            ))}
                    </List>
                )}
            </Paper>

            {/* Add/Edit Content Dialog */}
            <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
                <DialogTitle>
                    {editingItem
                        ? `Chỉnh sửa ${dialogType === 'lesson' ? 'bài học' : dialogType === 'quiz' ? 'bài kiểm tra' : 'bài tập TurboWarp'}`
                        : `Thêm ${dialogType === 'lesson' ? 'bài học' : dialogType === 'quiz' ? 'bài kiểm tra' : 'bài tập TurboWarp'} mới`
                    }
                </DialogTitle>
                <DialogContent>
                    <Box sx={{ mt: 2 }}>
                        {renderContentForm()}
                    </Box>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseDialog} sx={{ borderRadius: '10px' }}>Hủy</Button>
                    <Button onClick={handleSaveContent} variant="contained" sx={{ borderRadius: '10px' }}>
                        {editingItem ? 'Cập nhật' : 'Thêm'}
                    </Button>
                </DialogActions>
            </Dialog>

            {/* Snackbar */}
            <Snackbar
                open={snackbar.open}
                autoHideDuration={6000}
                onClose={() => setSnackbar({ ...snackbar, open: false })}
            >
                <Alert
                    onClose={() => setSnackbar({ ...snackbar, open: false })}
                    severity={snackbar.severity}
                    sx={{ width: '100%' }}
                >
                    {snackbar.message}
                </Alert>
            </Snackbar>
        </Box>
    );
}

export default CourseBuilder;
