import React, { useState, useEffect } from 'react';
import {
    Box,
    Typography,
    Button,
    Paper,
    Drawer,
    AppBar,
    Toolbar,
    List,
    ListItem,
    ListItemText,
    ListItemIcon,
    Divider,
    Chip,
    Card,
    CardContent,
    IconButton,
    Alert,
    LinearProgress
} from '@mui/material';
import {
    ArrowBack as BackIcon,
    MenuBook as LessonIcon,
    Quiz as QuizIcon,
    Games as TurboWarpIcon,
    VideoLibrary as VideoIcon,
    AttachFile as FileIcon,
    Create as TextIcon,
    Schedule as ScheduleIcon,
    PlayArrow as PlayIcon,
    CheckCircle as CheckIcon,
    Download as DownloadIcon
} from '@mui/icons-material';

const drawerWidth = 320;

function CoursePreviewPage({ courseId, onClose }) {
    const [course, setCourse] = useState(null);
    const [courseContent, setCourseContent] = useState([]);
    const [selectedContent, setSelectedContent] = useState(null);
    const [completedItems, setCompletedItems] = useState(new Set());

    useEffect(() => {
        // <PERSON><PERSON><PERSON> lập load dữ liệu khóa học
        const mockCourse = {
            id: courseId,
            title: '<PERSON><PERSON> học nâng cao lớp 8',
            description: 'Khóa học toán nâng cao dành cho học sinh khá giỏi lớp 8',
            teacher: 'Cô Nguyễn Thị A',
            totalLessons: 12,
            totalDuration: 480 // phút
        };

        const mockContent = [
            {
                id: 1,
                type: 'lesson',
                title: 'Giới thiệu phương trình bậc hai',
                description: 'Tìm hiểu khái niệm và dạng tổng quát của phương trình bậc hai',
                contentType: 'text',
                content: `
                    <h2 style="color: #1976d2;">Phương trình bậc hai</h2>
                    <p>Phương trình bậc hai là phương trình có dạng tổng quát:</p>
                    <p style="text-align: center; font-size: 20px; font-weight: bold; color: #1976d2; background: #f0f8ff; padding: 15px; border-radius: 10px; margin: 20px 0;">
                        ax² + bx + c = 0 (với a ≠ 0)
                    </p>
                    <h3 style="color: #2e7d32;">Các thành phần:</h3>
                    <ul style="line-height: 1.8;">
                        <li><strong style="color: #d32f2f;">a, b, c</strong>: là các hệ số (a ≠ 0)</li>
                        <li><strong style="color: #d32f2f;">x</strong>: là ẩn số cần tìm</li>
                    </ul>
                    <h3 style="color: #2e7d32;">Công thức nghiệm:</h3>
                    <p style="background: #fff3e0; padding: 15px; border-radius: 10px; border-left: 4px solid #ff9800;">
                        <strong>Δ = b² - 4ac</strong>
                    </p>
                    <ul style="line-height: 1.8;">
                        <li>Nếu <strong style="color: #388e3c;">Δ > 0</strong>: phương trình có 2 nghiệm phân biệt</li>
                        <li>Nếu <strong style="color: #f57c00;">Δ = 0</strong>: phương trình có nghiệm kép</li>
                        <li>Nếu <strong style="color: #d32f2f;">Δ < 0</strong>: phương trình vô nghiệm</li>
                    </ul>
                    <h3 style="color: #2e7d32;">Ví dụ minh họa:</h3>
                    <div style="background: #e8f5e8; padding: 20px; border-radius: 10px; margin: 15px 0;">
                        <p><strong>Giải phương trình:</strong> x² - 5x + 6 = 0</p>
                        <p>Ta có: a = 1, b = -5, c = 6</p>
                        <p>Δ = (-5)² - 4.1.6 = 25 - 24 = 1 > 0</p>
                        <p style="color: #2e7d32;"><strong>Vậy phương trình có 2 nghiệm phân biệt:</strong></p>
                        <p>x₁ = (5 + √1)/2 = 3</p>
                        <p>x₂ = (5 - √1)/2 = 2</p>
                    </div>
                `,
                duration: 45,
                order: 1
            },
            {
                id: 2,
                type: 'lesson',
                title: 'Video hướng dẫn giải phương trình',
                description: 'Video minh họa cách giải phương trình bậc hai',
                contentType: 'video',
                videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
                duration: 30,
                order: 2
            },
            {
                id: 3,
                type: 'lesson',
                title: 'Tài liệu bài tập phương trình',
                description: 'File PDF chứa các bài tập thực hành',
                contentType: 'file',
                fileName: 'Bai_tap_phuong_trinh_bac_hai.pdf',
                fileUrl: '/files/math_exercises.pdf',
                fileSize: '2.5 MB',
                duration: 60,
                order: 3
            },
            {
                id: 4,
                type: 'quiz',
                title: 'Kiểm tra phương trình bậc hai',
                description: 'Bài kiểm tra kiến thức về phương trình bậc hai',
                questions: [
                    {
                        question: 'Phương trình x² - 5x + 6 = 0 có nghiệm là?',
                        options: ['x = 2, x = 3', 'x = 1, x = 6', 'x = -2, x = -3', 'Vô nghiệm'],
                        correctAnswer: 0
                    },
                    {
                        question: 'Với phương trình ax² + bx + c = 0, điều kiện để có nghiệm là?',
                        options: ['Δ ≥ 0', 'Δ > 0', 'Δ = 0', 'a ≠ 0'],
                        correctAnswer: 0
                    }
                ],
                duration: 20,
                order: 4
            },
            {
                id: 5,
                type: 'turbowarp',
                title: 'Tạo máy tính giải phương trình',
                description: 'Lập trình máy tính giải phương trình bậc hai bằng Scratch',
                requiredFeatures: [
                    'Nhập hệ số a, b, c từ người dùng',
                    'Tính toán delta (Δ = b² - 4ac)',
                    'Hiển thị kết quả nghiệm',
                    'Xử lý các trường hợp đặc biệt'
                ],
                gradingCriteria: [
                    'Chức năng hoạt động chính xác (40%)',
                    'Giao diện người dùng thân thiện (30%)',
                    'Xử lý lỗi và trường hợp đặc biệt (20%)',
                    'Tổ chức code và comments (10%)'
                ],
                starterProject: 'https://turbowarp.org/editor?project_url=math-calculator-starter.sb3',
                duration: 120,
                order: 5
            }
        ];

        setCourse(mockCourse);
        setCourseContent(mockContent);

        // Chọn bài đầu tiên mặc định
        if (mockContent.length > 0) {
            setSelectedContent(mockContent[0]);
        }
    }, [courseId]);

    const handleSelectContent = (content) => {
        setSelectedContent(content);
    };

    const handleMarkComplete = (contentId) => {
        setCompletedItems(prev => new Set([...prev, contentId]));
    };

    const getContentIcon = (type, contentType) => {
        if (type === 'lesson') {
            switch (contentType) {
                case 'video': return <VideoIcon sx={{ color: '#f44336' }} />;
                case 'file': return <FileIcon sx={{ color: '#ff9800' }} />;
                default: return <TextIcon sx={{ color: '#2196f3' }} />;
            }
        } else if (type === 'quiz') {
            return <QuizIcon sx={{ color: '#9c27b0' }} />;
        } else if (type === 'turbowarp') {
            return <TurboWarpIcon sx={{ color: '#4caf50' }} />;
        }
        return <LessonIcon />;
    };

    const renderContent = () => {
        if (!selectedContent) return null;

        switch (selectedContent.type) {
            case 'lesson':
                if (selectedContent.contentType === 'text') {
                    return (
                        <Box sx={{ p: 3 }}>
                            <div
                                dangerouslySetInnerHTML={{ __html: selectedContent.content }}
                                style={{ lineHeight: 1.6 }}
                            />
                        </Box>
                    );
                } else if (selectedContent.contentType === 'video') {
                    return (
                        <Box sx={{ p: 3 }}>
                            <Typography variant="body1" sx={{ mb: 3 }}>
                                {selectedContent.description}
                            </Typography>
                            <Box sx={{
                                position: 'relative',
                                paddingBottom: '56.25%',
                                height: 0,
                                borderRadius: '10px',
                                overflow: 'hidden'
                            }}>
                                <iframe
                                    src={selectedContent.videoUrl}
                                    style={{
                                        position: 'absolute',
                                        top: 0,
                                        left: 0,
                                        width: '100%',
                                        height: '100%',
                                        border: 'none'
                                    }}
                                    allowFullScreen
                                    title={selectedContent.title}
                                />
                            </Box>
                        </Box>
                    );
                } else if (selectedContent.contentType === 'file') {
                    return (
                        <Box sx={{ p: 3 }}>
                            <Typography variant="body1" sx={{ mb: 3 }}>
                                {selectedContent.description}
                            </Typography>
                            <Card sx={{ borderRadius: '10px', border: '2px dashed #ddd' }}>
                                <CardContent sx={{ textAlign: 'center', py: 4 }}>
                                    <FileIcon sx={{ fontSize: 64, color: '#ff9800', mb: 2 }} />
                                    <Typography variant="h6" gutterBottom>
                                        {selectedContent.fileName}
                                    </Typography>
                                    <Typography variant="body2" color="textSecondary" sx={{ mb: 3 }}>
                                        Kích thước: {selectedContent.fileSize}
                                    </Typography>
                                    <Button
                                        variant="contained"
                                        startIcon={<DownloadIcon />}
                                        component="a"
                                        href={selectedContent.fileUrl}
                                        download
                                        sx={{ borderRadius: '10px' }}
                                    >
                                        Tải xuống
                                    </Button>
                                </CardContent>
                            </Card>
                        </Box>
                    );
                }
                break;

            case 'quiz':
                return (
                    <Box sx={{ p: 3 }}>
                        <Typography variant="body1" sx={{ mb: 3 }}>
                            {selectedContent.description}
                        </Typography>
                        <Alert severity="info" sx={{ mb: 3, borderRadius: '10px' }}>
                            Bài kiểm tra gồm {selectedContent.questions.length} câu hỏi - Thời gian: {selectedContent.duration} phút
                        </Alert>

                        {selectedContent.questions.map((question, index) => (
                            <Card key={index} sx={{ mb: 2, borderRadius: '10px' }}>
                                <CardContent>
                                    <Typography variant="h6" gutterBottom>
                                        Câu {index + 1}: {question.question}
                                    </Typography>
                                    {question.options.map((option, optIndex) => (
                                        <Typography
                                            key={optIndex}
                                            variant="body2"
                                            sx={{
                                                ml: 2,
                                                mb: 1,
                                                color: optIndex === question.correctAnswer ? '#2e7d32' : 'inherit',
                                                fontWeight: optIndex === question.correctAnswer ? 'bold' : 'normal'
                                            }}
                                        >
                                            {String.fromCharCode(65 + optIndex)}. {option}
                                            {optIndex === question.correctAnswer && ' ✓'}
                                        </Typography>
                                    ))}
                                </CardContent>
                            </Card>
                        ))}

                        <Button
                            variant="contained"
                            startIcon={<PlayIcon />}
                            fullWidth
                            sx={{ mt: 2, borderRadius: '10px' }}
                        >
                            Bắt đầu làm bài
                        </Button>
                    </Box>
                );

            case 'turbowarp':
                return (
                    <Box sx={{ p: 3 }}>
                        <Typography variant="body1" sx={{ mb: 3 }}>
                            {selectedContent.description}
                        </Typography>

                        <Card sx={{ mb: 3, borderRadius: '10px' }}>
                            <CardContent>
                                <Typography variant="h6" gutterBottom>
                                    Yêu cầu tính năng:
                                </Typography>
                                {selectedContent.requiredFeatures.map((feature, index) => (
                                    <Typography key={index} variant="body2" sx={{ mb: 1, ml: 2 }}>
                                        • {feature}
                                    </Typography>
                                ))}
                            </CardContent>
                        </Card>

                        <Card sx={{ mb: 3, borderRadius: '10px' }}>
                            <CardContent>
                                <Typography variant="h6" gutterBottom>
                                    Tiêu chí chấm điểm:
                                </Typography>
                                {selectedContent.gradingCriteria.map((criteria, index) => (
                                    <Typography key={index} variant="body2" sx={{ mb: 1, ml: 2 }}>
                                        • {criteria}
                                    </Typography>
                                ))}
                            </CardContent>
                        </Card>

                        <Button
                            variant="outlined"
                            component="a"
                            href={selectedContent.starterProject}
                            target="_blank"
                            fullWidth
                            sx={{ mb: 2, borderRadius: '10px' }}
                        >
                            Mở dự án mẫu
                        </Button>

                        <Button
                            variant="contained"
                            startIcon={<PlayIcon />}
                            fullWidth
                            sx={{ borderRadius: '10px' }}
                        >
                            Bắt đầu làm bài tập
                        </Button>
                    </Box>
                );

            default:
                return null;
        }
    };

    const getTotalDuration = () => {
        return courseContent.reduce((total, item) => total + (item.duration || 0), 0);
    };

    const getCompletedDuration = () => {
        return courseContent
            .filter(item => completedItems.has(item.id))
            .reduce((total, item) => total + (item.duration || 0), 0);
    };

    const formatDuration = (minutes) => {
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        if (hours > 0) {
            return `${hours}h ${mins}m`;
        }
        return `${mins}m`;
    };

    if (!course) {
        return <Typography>Đang tải...</Typography>;
    }

    return (
        <Box sx={{
            display: 'flex',
            height: '100vh',
            '& @keyframes pulse': {
                '0%': { opacity: 1 },
                '50%': { opacity: 0.5 },
                '100%': { opacity: 1 }
            }
        }}>
            {/* AppBar */}
            <AppBar
                position="fixed"
                sx={{
                    zIndex: (theme) => theme.zIndex.drawer + 1,
                    bgcolor: '#1976d2',
                    borderRadius: '0 0 10px 10px'
                }}
            >
                <Toolbar>
                    <IconButton
                        color="inherit"
                        onClick={onClose}
                        sx={{ mr: 2 }}
                    >
                        <BackIcon />
                    </IconButton>
                    <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
                        {selectedContent?.title || course.title}
                    </Typography>
                    <Chip
                        label={`${formatDuration(getCompletedDuration())} / ${formatDuration(getTotalDuration())}`}
                        color="secondary"
                        sx={{ borderRadius: '10px' }}
                    />
                </Toolbar>
            </AppBar>

            {/* Sidebar */}
            <Drawer
                variant="permanent"
                sx={{
                    width: drawerWidth,
                    flexShrink: 0,
                    '& .MuiDrawer-paper': {
                        width: drawerWidth,
                        boxSizing: 'border-box',
                        borderRadius: '0 10px 10px 0'
                    },
                }}
            >
                <Toolbar />
                <Box sx={{ p: 2 }}>
                    <Typography variant="h6" gutterBottom>
                        {course.title}
                    </Typography>
                    <Typography variant="body2" color="textSecondary" gutterBottom>
                        {course.teacher}
                    </Typography>

                    {/* Progress */}
                    <Box sx={{ mt: 2, mb: 3 }}>
                        <Typography variant="body2" gutterBottom>
                            Tiến độ: {completedItems.size}/{courseContent.length} bài học
                        </Typography>
                        <LinearProgress
                            variant="determinate"
                            value={(completedItems.size / courseContent.length) * 100}
                            sx={{ height: 8, borderRadius: 4 }}
                        />
                    </Box>
                </Box>

                <Divider />

                <List sx={{ flexGrow: 1, overflow: 'auto' }}>
                    {courseContent
                        .sort((a, b) => a.order - b.order)
                        .map((content) => (
                            <ListItem
                                key={content.id}
                                button
                                selected={selectedContent?.id === content.id}
                                onClick={() => handleSelectContent(content)}
                                sx={{
                                    borderRadius: '10px',
                                    mx: 1,
                                    mb: 1,
                                    position: 'relative',
                                    border: selectedContent?.id === content.id ? '2px solid #1976d2' : '2px solid transparent',
                                    boxShadow: selectedContent?.id === content.id ? '0 4px 12px rgba(25, 118, 210, 0.2)' : 'none',
                                    transform: selectedContent?.id === content.id ? 'scale(1.02)' : 'scale(1)',
                                    transition: 'all 0.2s ease-in-out',
                                    '&.Mui-selected': {
                                        bgcolor: '#e3f2fd',
                                        '&:hover': {
                                            bgcolor: '#bbdefb',
                                        },
                                    },
                                    '&:hover': {
                                        transform: 'scale(1.01)',
                                        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                                    },
                                    '&::before': selectedContent?.id === content.id ? {
                                        content: '""',
                                        position: 'absolute',
                                        left: '-8px',
                                        top: '50%',
                                        transform: 'translateY(-50%)',
                                        width: '4px',
                                        height: '60%',
                                        bgcolor: '#1976d2',
                                        borderRadius: '2px',
                                        transition: 'all 0.2s ease-in-out'
                                    } : {}
                                }}
                            >
                                <ListItemIcon>
                                    <Box sx={{
                                        p: 0.5,
                                        borderRadius: '8px',
                                        bgcolor: selectedContent?.id === content.id ? 'rgba(25, 118, 210, 0.15)' : 'transparent',
                                        border: selectedContent?.id === content.id ? '1px solid rgba(25, 118, 210, 0.3)' : '1px solid transparent',
                                        transition: 'all 0.2s ease-in-out',
                                        transform: selectedContent?.id === content.id ? 'scale(1.1)' : 'scale(1)',
                                        '& svg': {
                                            filter: selectedContent?.id === content.id ? 'brightness(1.2)' : 'none'
                                        }
                                    }}>
                                        {getContentIcon(content.type, content.contentType)}
                                    </Box>
                                </ListItemIcon>
                                <ListItemText
                                    primary={
                                        <Typography
                                            variant="body2"
                                            sx={{
                                                fontWeight: selectedContent?.id === content.id ? 600 : 400,
                                                color: selectedContent?.id === content.id ? '#1976d2' : 'inherit',
                                                transition: 'all 0.2s ease-in-out'
                                            }}
                                        >
                                            {content.title}
                                        </Typography>
                                    }
                                    secondary={
                                        <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                                            <ScheduleIcon sx={{
                                                fontSize: 14,
                                                mr: 0.5,
                                                color: selectedContent?.id === content.id ? '#1976d2' : 'inherit'
                                            }} />
                                            <Typography
                                                variant="caption"
                                                sx={{
                                                    color: selectedContent?.id === content.id ? '#1976d2' : 'inherit',
                                                    fontWeight: selectedContent?.id === content.id ? 500 : 400
                                                }}
                                            >
                                                {formatDuration(content.duration)}
                                            </Typography>
                                            {completedItems.has(content.id) && (
                                                <CheckIcon sx={{
                                                    fontSize: 16,
                                                    ml: 1,
                                                    color: '#4caf50',
                                                    animation: selectedContent?.id === content.id ? 'pulse 1.5s infinite' : 'none'
                                                }} />
                                            )}
                                        </Box>
                                    }
                                />
                            </ListItem>
                        ))}
                </List>

                {selectedContent && !completedItems.has(selectedContent.id) && (
                    <Box sx={{ p: 2 }}>
                        <Button
                            variant="contained"
                            startIcon={<CheckIcon />}
                            fullWidth
                            onClick={() => handleMarkComplete(selectedContent.id)}
                            sx={{ borderRadius: '10px' }}
                        >
                            Đánh dấu hoàn thành
                        </Button>
                    </Box>
                )}
            </Drawer>

            {/* Main content */}
            <Box
                component="main"
                sx={{
                    flexGrow: 1,
                    bgcolor: 'background.default',
                    overflow: 'auto'
                }}
            >
                <Toolbar />
                <Paper sx={{ m: 2, borderRadius: '10px', minHeight: 'calc(100vh - 120px)' }}>
                    {renderContent()}
                </Paper>
            </Box>
        </Box>
    );
}

export default CoursePreviewPage;
