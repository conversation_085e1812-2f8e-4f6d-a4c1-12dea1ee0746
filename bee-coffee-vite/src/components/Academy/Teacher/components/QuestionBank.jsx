import React, { useState, useEffect } from 'react';
import {
    Box,
    Typography,
    Button,
    Paper,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    TablePagination,
    IconButton,
    Chip,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    TextField,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Card,
    CardContent,
    Fab,
    Tooltip,
    Alert,
    Snackbar
} from '@mui/material';
import Grid from "@mui/material/Grid2";
import {
    Add as AddIcon,
    Edit as EditIcon,
    Delete as DeleteIcon,
    Search as SearchIcon,
    FilterList as FilterIcon,
    QuestionAnswer as QuestionIcon
} from '@mui/icons-material';

function QuestionBank({ user }) {
    const [questions, setQuestions] = useState([]);
    const [filteredQuestions, setFilteredQuestions] = useState([]);
    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(10);
    const [openDialog, setOpenDialog] = useState(false);
    const [editingQuestion, setEditingQuestion] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [filterSubject, setFilterSubject] = useState('');
    const [filterDifficulty, setFilterDifficulty] = useState('');
    const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

    // Form state
    const [formData, setFormData] = useState({
        question: '',
        options: ['', '', '', ''],
        correctAnswer: 0,
        subject: '',
        difficulty: 'medium',
        explanation: '',
        tags: ''
    });

    const subjects = ['Toán học', 'Vật lý', 'Hóa học', 'Sinh học', 'Tin học', 'STEM'];
    const difficulties = [
        { value: 'easy', label: 'Dễ', color: 'success' },
        { value: 'medium', label: 'Trung bình', color: 'warning' },
        { value: 'hard', label: 'Khó', color: 'error' }
    ];

    useEffect(() => {
        // Giả lập dữ liệu - sau này sẽ thay bằng API calls
        const mockQuestions = [
            {
                id: 1,
                question: 'Kết quả của phép tính 2 + 2 là gì?',
                options: ['3', '4', '5', '6'],
                correctAnswer: 1,
                subject: 'Toán học',
                difficulty: 'easy',
                explanation: 'Phép cộng cơ bản: 2 + 2 = 4',
                tags: 'cộng, cơ bản',
                createdAt: '2024-01-10'
            },
            {
                id: 2,
                question: 'Công thức tính diện tích hình tròn là gì?',
                options: ['πr²', '2πr', 'πd', 'r²'],
                correctAnswer: 0,
                subject: 'Toán học',
                difficulty: 'medium',
                explanation: 'Diện tích hình tròn = π × bán kính²',
                tags: 'hình học, diện tích',
                createdAt: '2024-01-09'
            },
            {
                id: 3,
                question: 'Đơn vị đo lực trong hệ SI là gì?',
                options: ['Joule', 'Newton', 'Watt', 'Pascal'],
                correctAnswer: 1,
                subject: 'Vật lý',
                difficulty: 'medium',
                explanation: 'Newton (N) là đơn vị đo lực trong hệ SI',
                tags: 'đơn vị, lực',
                createdAt: '2024-01-08'
            }
        ];
        setQuestions(mockQuestions);
        setFilteredQuestions(mockQuestions);
    }, []);

    useEffect(() => {
        let filtered = questions;

        if (searchTerm) {
            filtered = filtered.filter(q =>
                q.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
                q.tags.toLowerCase().includes(searchTerm.toLowerCase())
            );
        }

        if (filterSubject) {
            filtered = filtered.filter(q => q.subject === filterSubject);
        }

        if (filterDifficulty) {
            filtered = filtered.filter(q => q.difficulty === filterDifficulty);
        }

        setFilteredQuestions(filtered);
        setPage(0);
    }, [questions, searchTerm, filterSubject, filterDifficulty]);

    const handleChangePage = (event, newPage) => {
        setPage(newPage);
    };

    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(parseInt(event.target.value, 10));
        setPage(0);
    };

    const handleOpenDialog = (question = null) => {
        if (question) {
            setEditingQuestion(question);
            setFormData({
                question: question.question,
                options: [...question.options],
                correctAnswer: question.correctAnswer,
                subject: question.subject,
                difficulty: question.difficulty,
                explanation: question.explanation,
                tags: question.tags
            });
        } else {
            setEditingQuestion(null);
            setFormData({
                question: '',
                options: ['', '', '', ''],
                correctAnswer: 0,
                subject: '',
                difficulty: 'medium',
                explanation: '',
                tags: ''
            });
        }
        setOpenDialog(true);
    };

    const handleCloseDialog = () => {
        setOpenDialog(false);
        setEditingQuestion(null);
    };

    const handleFormChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const handleOptionChange = (index, value) => {
        const newOptions = [...formData.options];
        newOptions[index] = value;
        setFormData(prev => ({
            ...prev,
            options: newOptions
        }));
    };

    const handleSaveQuestion = () => {
        if (!formData.question || !formData.subject || formData.options.some(opt => !opt)) {
            setSnackbar({
                open: true,
                message: 'Vui lòng điền đầy đủ thông tin!',
                severity: 'error'
            });
            return;
        }

        const questionData = {
            ...formData,
            id: editingQuestion ? editingQuestion.id : Date.now(),
            createdAt: editingQuestion ? editingQuestion.createdAt : new Date().toISOString().split('T')[0]
        };

        if (editingQuestion) {
            setQuestions(prev => prev.map(q => q.id === editingQuestion.id ? questionData : q));
            setSnackbar({
                open: true,
                message: 'Cập nhật câu hỏi thành công!',
                severity: 'success'
            });
        } else {
            setQuestions(prev => [...prev, questionData]);
            setSnackbar({
                open: true,
                message: 'Thêm câu hỏi thành công!',
                severity: 'success'
            });
        }

        handleCloseDialog();
    };

    const handleDeleteQuestion = (id) => {
        if (window.confirm('Bạn có chắc chắn muốn xóa câu hỏi này?')) {
            setQuestions(prev => prev.filter(q => q.id !== id));
            setSnackbar({
                open: true,
                message: 'Xóa câu hỏi thành công!',
                severity: 'success'
            });
        }
    };

    const getDifficultyChip = (difficulty) => {
        const difficultyInfo = difficulties.find(d => d.value === difficulty);
        return (
            <Chip
                label={difficultyInfo?.label || difficulty}
                color={difficultyInfo?.color || 'default'}
                size="small"
            />
        );
    };

    return (
        <Box>
            {/* Header */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h4">
                    Ngân hàng câu hỏi
                </Typography>
                <Button
                    sx={{ borderRadius: "10px", display: { sm: "none", md: "flex" } }}
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={() => handleOpenDialog()}
                >
                    Thêm câu hỏi
                </Button>
            </Box>

            {/* Statistics */}
            <Grid container spacing={3} sx={{ mb: 3 }}>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Card sx={{ borderRadius: "10px" }}>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Tổng số câu hỏi
                            </Typography>
                            <Typography variant="h4">
                                {questions.length}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Card sx={{ borderRadius: "10px" }}>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Môn học
                            </Typography>
                            <Typography variant="h4">
                                {new Set(questions.map(q => q.subject)).size}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Card sx={{ borderRadius: "10px" }}>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Câu hỏi dễ
                            </Typography>
                            <Typography variant="h4" color="success.main">
                                {questions.filter(q => q.difficulty === 'easy').length}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Card sx={{ borderRadius: "10px" }}>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Câu hỏi khó
                            </Typography>
                            <Typography variant="h4" color="error.main">
                                {questions.filter(q => q.difficulty === 'hard').length}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
            </Grid>

            {/* Filters */}
            <Paper sx={{ p: 2, mb: 3, borderRadius: "10px" }}>
                <Grid container spacing={2} alignItems="center">
                    <Grid size={{ xs: 12, md: 4 }}>
                        <TextField
                            size="small"
                            fullWidth
                            placeholder="Tìm kiếm câu hỏi..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            InputProps={{
                                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
                            }}
                        />
                    </Grid>
                    <Grid size={{ xs: 12, md: 3 }}>
                        <FormControl fullWidth size="small">
                            <InputLabel>Môn học</InputLabel>
                            <Select
                                value={filterSubject}
                                onChange={(e) => setFilterSubject(e.target.value)}
                                label="Môn học"
                            >
                                <MenuItem value="">Tất cả</MenuItem>
                                {subjects.map(subject => (
                                    <MenuItem key={subject} value={subject}>{subject}</MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                    </Grid>
                    <Grid size={{ xs: 12, md: 3 }}>
                        <FormControl fullWidth size="small">
                            <InputLabel>Độ khó</InputLabel>
                            <Select
                                value={filterDifficulty}
                                onChange={(e) => setFilterDifficulty(e.target.value)}
                                label="Độ khó"
                            >
                                <MenuItem value="">Tất cả</MenuItem>
                                {difficulties.map(difficulty => (
                                    <MenuItem key={difficulty.value} value={difficulty.value}>
                                        {difficulty.label}
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                    </Grid>
                    <Grid size={{ xs: 12, md: 2 }}>
                        <Button
                            fullWidth
                            variant="outlined"
                            startIcon={<FilterIcon />}
                            onClick={() => {
                                setSearchTerm('');
                                setFilterSubject('');
                                setFilterDifficulty('');
                            }}
                        >
                            Xóa bộ lọc
                        </Button>
                    </Grid>
                </Grid>
            </Paper>

            {/* Questions Table */}
            <TableContainer component={Paper} sx={{ borderRadius: "10px" }}>
                <Table>
                    <TableHead>
                        <TableRow>
                            <TableCell>Câu hỏi</TableCell>
                            <TableCell>Môn học</TableCell>
                            <TableCell>Độ khó</TableCell>
                            <TableCell>Tags</TableCell>
                            <TableCell>Ngày tạo</TableCell>
                            <TableCell align="center">Thao tác</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {filteredQuestions
                            .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                            .map((question) => (
                                <TableRow key={question.id}>
                                    <TableCell>
                                        <Typography variant="body2" sx={{ maxWidth: 300 }}>
                                            {question.question.length > 100
                                                ? `${question.question.substring(0, 100)}...`
                                                : question.question
                                            }
                                        </Typography>
                                    </TableCell>
                                    <TableCell>
                                        <Chip label={question.subject} variant="outlined" size="small" />
                                    </TableCell>
                                    <TableCell>
                                        {getDifficultyChip(question.difficulty)}
                                    </TableCell>
                                    <TableCell>
                                        <Typography variant="body2" color="textSecondary">
                                            {question.tags}
                                        </Typography>
                                    </TableCell>
                                    <TableCell>{question.createdAt}</TableCell>
                                    <TableCell align="center">
                                        <Tooltip title="Chỉnh sửa">
                                            <IconButton
                                                size="small"
                                                onClick={() => handleOpenDialog(question)}
                                            >
                                                <EditIcon />
                                            </IconButton>
                                        </Tooltip>
                                        <Tooltip title="Xóa">
                                            <IconButton
                                                size="small"
                                                color="error"
                                                onClick={() => handleDeleteQuestion(question.id)}
                                            >
                                                <DeleteIcon />
                                            </IconButton>
                                        </Tooltip>
                                    </TableCell>
                                </TableRow>
                            ))}
                    </TableBody>
                </Table>
                <TablePagination
                    rowsPerPageOptions={[5, 10, 25]}
                    component="div"
                    count={filteredQuestions.length}
                    rowsPerPage={rowsPerPage}
                    page={page}
                    onPageChange={handleChangePage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                    labelRowsPerPage="Số hàng mỗi trang:"
                />
            </TableContainer>

            {/* Add/Edit Question Dialog */}
            <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
                <DialogTitle>
                    {editingQuestion ? 'Chỉnh sửa câu hỏi' : 'Thêm câu hỏi mới'}
                </DialogTitle>
                <DialogContent>
                    <Grid container spacing={2} sx={{ mt: 1 }}>
                        <Grid size={{ xs: 12 }}>
                            <TextField
                                fullWidth
                                label="Câu hỏi"
                                multiline
                                rows={3}
                                value={formData.question}
                                onChange={(e) => handleFormChange('question', e.target.value)}
                            />
                        </Grid>

                        {formData.options.map((option, index) => (
                            <Grid size={{ xs: 12, sm: 6 }} key={index}>
                                <TextField
                                    fullWidth
                                    label={`Đáp án ${String.fromCharCode(65 + index)}`}
                                    value={option}
                                    onChange={(e) => handleOptionChange(index, e.target.value)}
                                    color={formData.correctAnswer === index ? 'success' : 'primary'}
                                />
                            </Grid>
                        ))}

                        <Grid size={{ xs: 12 }}>
                            <FormControl fullWidth>
                                <InputLabel>Đáp án đúng</InputLabel>
                                <Select
                                    value={formData.correctAnswer}
                                    onChange={(e) => handleFormChange('correctAnswer', e.target.value)}
                                    label="Đáp án đúng"
                                >
                                    {formData.options.map((option, index) => (
                                        <MenuItem key={index} value={index}>
                                            {String.fromCharCode(65 + index)} - {option || `Đáp án ${index + 1}`}
                                        </MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        </Grid>

                        <Grid size={{ xs: 12, sm: 6 }}>
                            <FormControl fullWidth>
                                <InputLabel>Môn học</InputLabel>
                                <Select
                                    value={formData.subject}
                                    onChange={(e) => handleFormChange('subject', e.target.value)}
                                    label="Môn học"
                                >
                                    {subjects.map(subject => (
                                        <MenuItem key={subject} value={subject}>{subject}</MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        </Grid>

                        <Grid size={{ xs: 12, sm: 6 }}>
                            <FormControl fullWidth>
                                <InputLabel>Độ khó</InputLabel>
                                <Select
                                    value={formData.difficulty}
                                    onChange={(e) => handleFormChange('difficulty', e.target.value)}
                                    label="Độ khó"
                                >
                                    {difficulties.map(difficulty => (
                                        <MenuItem key={difficulty.value} value={difficulty.value}>
                                            {difficulty.label}
                                        </MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        </Grid>

                        <Grid size={{ xs: 12 }}>
                            <TextField
                                fullWidth
                                label="Giải thích"
                                multiline
                                rows={2}
                                value={formData.explanation}
                                onChange={(e) => handleFormChange('explanation', e.target.value)}
                            />
                        </Grid>

                        <Grid size={{ xs: 12 }}>
                            <TextField
                                fullWidth
                                label="Tags (phân cách bằng dấu phẩy)"
                                value={formData.tags}
                                onChange={(e) => handleFormChange('tags', e.target.value)}
                                placeholder="ví dụ: toán học, cơ bản, phép cộng"
                            />
                        </Grid>
                    </Grid>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseDialog} sx={{ borderRadius: "10px" }}>Hủy</Button>
                    <Button onClick={handleSaveQuestion} variant="contained" sx={{ borderRadius: "10px" }}>
                        {editingQuestion ? 'Cập nhật' : 'Thêm'}
                    </Button>
                </DialogActions>
            </Dialog>

            {/* Floating Action Button */}
            <Fab
                color="primary"
                aria-label="add"
                sx={{ position: 'fixed', bottom: 16, right: 16, display: { sm: 'block', md: 'none' } }}
                onClick={() => handleOpenDialog()}
            >
                <AddIcon />
            </Fab>

            {/* Snackbar */}
            <Snackbar
                open={snackbar.open}
                autoHideDuration={6000}
                onClose={() => setSnackbar({ ...snackbar, open: false })}
            >
                <Alert
                    onClose={() => setSnackbar({ ...snackbar, open: false })}
                    severity={snackbar.severity}
                    sx={{ width: '100%' }}
                >
                    {snackbar.message}
                </Alert>
            </Snackbar>
        </Box>
    );
}

export default QuestionBank;
