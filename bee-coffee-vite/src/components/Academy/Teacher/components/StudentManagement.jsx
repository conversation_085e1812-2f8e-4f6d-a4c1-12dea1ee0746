import React, { useState, useEffect } from 'react';
import {
    Box,
    Typography,
    Button,
    Paper,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    TablePagination,
    IconButton,
    Chip,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    TextField,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Avatar,
    Tooltip,
    Alert,
    Snackbar,
    Card,
    CardContent,
    List,
    ListItem,
    ListItemAvatar,
    ListItemText,
    ListItemSecondaryAction,
    Divider,
    Badge
} from '@mui/material';
import Grid from "@mui/material/Grid2";
import {
    Add as AddIcon,
    Edit as EditIcon,
    Delete as DeleteIcon,
    Search as SearchIcon,
    PersonAdd as PersonAddIcon,
    Email as EmailIcon,
    Phone as PhoneIcon,
    School as SchoolIcon,
    Assignment as AssignmentIcon,
    Quiz as QuizIcon,
    TrendingUp as TrendingUpIcon,
    Block as BlockIcon,
    CheckCircle as CheckCircleIcon
} from '@mui/icons-material';

function StudentManagement({ user }) {
    const [students, setStudents] = useState([]);
    const [filteredStudents, setFilteredStudents] = useState([]);
    const [classes, setClasses] = useState([]);
    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(10);
    const [openDialog, setOpenDialog] = useState(false);
    const [editingStudent, setEditingStudent] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [filterClass, setFilterClass] = useState('');
    const [filterStatus, setFilterStatus] = useState('');
    const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

    // Form state
    const [formData, setFormData] = useState({
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        classId: '',
        studentId: '',
        parentName: '',
        parentPhone: '',
        address: '',
        status: 'active'
    });

    useEffect(() => {
        // Giả lập dữ liệu lớp học
        const mockClasses = [
            { id: 1, name: 'Toán học nâng cao 8A', subject: 'Toán học', grade: 'Lớp 8' },
            { id: 2, name: 'Vật lý thí nghiệm 9B', subject: 'Vật lý', grade: 'Lớp 9' },
            { id: 3, name: 'Lập trình Scratch 7C', subject: 'Tin học', grade: 'Lớp 7' }
        ];
        setClasses(mockClasses);

        // Giả lập dữ liệu học sinh
        const mockStudents = [
            {
                id: 1,
                firstName: 'Nguyễn',
                lastName: 'Văn A',
                email: '<EMAIL>',
                phone: '0123456789',
                classId: 1,
                className: 'Toán học nâng cao 8A',
                studentId: 'HS001',
                parentName: 'Nguyễn Văn B',
                parentPhone: '0987654321',
                address: '123 Đường ABC, Quận 1, TP.HCM',
                status: 'active',
                joinDate: '2024-01-05',
                assignments: 8,
                quizzes: 5,
                avgScore: 8.5
            },
            {
                id: 2,
                firstName: 'Trần',
                lastName: 'Thị B',
                email: '<EMAIL>',
                phone: '0123456790',
                classId: 1,
                className: 'Toán học nâng cao 8A',
                studentId: 'HS002',
                parentName: 'Trần Văn C',
                parentPhone: '0987654322',
                address: '456 Đường DEF, Quận 2, TP.HCM',
                status: 'active',
                joinDate: '2024-01-05',
                assignments: 7,
                quizzes: 4,
                avgScore: 9.0
            },
            {
                id: 3,
                firstName: 'Lê',
                lastName: 'Văn C',
                email: '<EMAIL>',
                phone: '0123456791',
                classId: 2,
                className: 'Vật lý thí nghiệm 9B',
                studentId: 'HS003',
                parentName: 'Lê Thị D',
                parentPhone: '0987654323',
                address: '789 Đường GHI, Quận 3, TP.HCM',
                status: 'inactive',
                joinDate: '2024-01-06',
                assignments: 3,
                quizzes: 2,
                avgScore: 7.5
            }
        ];
        setStudents(mockStudents);
        setFilteredStudents(mockStudents);
    }, []);

    useEffect(() => {
        let filtered = students;

        if (searchTerm) {
            filtered = filtered.filter(s =>
                `${s.firstName} ${s.lastName}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
                s.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                s.studentId.toLowerCase().includes(searchTerm.toLowerCase())
            );
        }

        if (filterClass) {
            filtered = filtered.filter(s => s.classId === parseInt(filterClass));
        }

        if (filterStatus) {
            filtered = filtered.filter(s => s.status === filterStatus);
        }

        setFilteredStudents(filtered);
        setPage(0);
    }, [students, searchTerm, filterClass, filterStatus]);

    const handleChangePage = (event, newPage) => {
        setPage(newPage);
    };

    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(parseInt(event.target.value, 10));
        setPage(0);
    };

    const handleOpenDialog = (student = null) => {
        if (student) {
            setEditingStudent(student);
            setFormData({
                firstName: student.firstName,
                lastName: student.lastName,
                email: student.email,
                phone: student.phone,
                classId: student.classId,
                studentId: student.studentId,
                parentName: student.parentName,
                parentPhone: student.parentPhone,
                address: student.address,
                status: student.status
            });
        } else {
            setEditingStudent(null);
            setFormData({
                firstName: '',
                lastName: '',
                email: '',
                phone: '',
                classId: '',
                studentId: '',
                parentName: '',
                parentPhone: '',
                address: '',
                status: 'active'
            });
        }
        setOpenDialog(true);
    };

    const handleCloseDialog = () => {
        setOpenDialog(false);
        setEditingStudent(null);
    };

    const handleFormChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const handleSaveStudent = () => {
        if (!formData.firstName || !formData.lastName || !formData.email || !formData.classId) {
            setSnackbar({
                open: true,
                message: 'Vui lòng điền đầy đủ thông tin bắt buộc!',
                severity: 'error'
            });
            return;
        }

        const selectedClass = classes.find(c => c.id === formData.classId);
        const studentData = {
            ...formData,
            id: editingStudent ? editingStudent.id : Date.now(),
            className: selectedClass?.name || '',
            joinDate: editingStudent ? editingStudent.joinDate : new Date().toISOString().split('T')[0],
            assignments: editingStudent ? editingStudent.assignments : 0,
            quizzes: editingStudent ? editingStudent.quizzes : 0,
            avgScore: editingStudent ? editingStudent.avgScore : 0
        };

        if (editingStudent) {
            setStudents(prev => prev.map(s => s.id === editingStudent.id ? studentData : s));
            setSnackbar({
                open: true,
                message: 'Cập nhật học sinh thành công!',
                severity: 'success'
            });
        } else {
            setStudents(prev => [...prev, studentData]);
            setSnackbar({
                open: true,
                message: 'Thêm học sinh thành công!',
                severity: 'success'
            });
        }

        handleCloseDialog();
    };

    const handleDeleteStudent = (id) => {
        if (window.confirm('Bạn có chắc chắn muốn xóa học sinh này?')) {
            setStudents(prev => prev.filter(s => s.id !== id));
            setSnackbar({
                open: true,
                message: 'Xóa học sinh thành công!',
                severity: 'success'
            });
        }
    };

    const handleToggleStatus = (id) => {
        setStudents(prev => prev.map(s =>
            s.id === id
                ? { ...s, status: s.status === 'active' ? 'inactive' : 'active' }
                : s
        ));
        setSnackbar({
            open: true,
            message: 'Cập nhật trạng thái thành công!',
            severity: 'success'
        });
    };

    const getStatusChip = (status) => {
        return status === 'active'
            ? <Chip label="Hoạt động" color="success" size="small" />
            : <Chip label="Tạm dừng" color="error" size="small" />;
    };

    const getScoreColor = (score) => {
        if (score >= 8) return 'success.main';
        if (score >= 6.5) return 'warning.main';
        return 'error.main';
    };

    return (
        <Box>
            {/* Header */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h4">
                    Quản lý học sinh
                </Typography>
                <Button
                    sx={{ borderRadius: "10px" }}
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={() => handleOpenDialog()}
                >
                    Thêm học sinh
                </Button>
            </Box>

            {/* Statistics */}
            <Grid container spacing={3} sx={{ mb: 3 }}>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Card sx={{ borderRadius: "10px" }}>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Tổng số học sinh
                            </Typography>
                            <Typography variant="h4">
                                {students.length}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Card sx={{ borderRadius: "10px" }}>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Đang hoạt động
                            </Typography>
                            <Typography variant="h4" color="success.main">
                                {students.filter(s => s.status === 'active').length}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Card sx={{ borderRadius: "10px" }}>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Điểm trung bình
                            </Typography>
                            <Typography variant="h4">
                                {students.length > 0
                                    ? (students.reduce((sum, s) => sum + s.avgScore, 0) / students.length).toFixed(1)
                                    : '0.0'
                                }
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Card sx={{ borderRadius: "10px" }}>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Lớp học
                            </Typography>
                            <Typography variant="h4">
                                {new Set(students.map(s => s.classId)).size}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
            </Grid>

            {/* Filters */}
            <Paper sx={{ p: 2, mb: 3, borderRadius: "10px" }}>
                <Grid container spacing={2} alignItems="center">
                    <Grid size={{ xs: 12, md: 4 }}>
                        <TextField
                            size="small"
                            fullWidth
                            placeholder="Tìm kiếm học sinh..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            InputProps={{
                                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
                            }}
                        />
                    </Grid>
                    <Grid size={{ xs: 12, md: 3 }}>
                        <FormControl fullWidth size="small">
                            <InputLabel>Lớp học</InputLabel>
                            <Select
                                value={filterClass}
                                onChange={(e) => setFilterClass(e.target.value)}
                                label="Lớp học"
                            >
                                <MenuItem value="">Tất cả</MenuItem>
                                {classes.map(cls => (
                                    <MenuItem key={cls.id} value={cls.id}>{cls.name}</MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                    </Grid>
                    <Grid size={{ xs: 12, md: 3 }}>
                        <FormControl fullWidth size="small">
                            <InputLabel>Trạng thái</InputLabel>
                            <Select
                                value={filterStatus}
                                onChange={(e) => setFilterStatus(e.target.value)}
                                label="Trạng thái"
                            >
                                <MenuItem value="">Tất cả</MenuItem>
                                <MenuItem value="active">Hoạt động</MenuItem>
                                <MenuItem value="inactive">Tạm dừng</MenuItem>
                            </Select>
                        </FormControl>
                    </Grid>
                    <Grid size={{ xs: 12, md: 2 }}>
                        <Button
                            fullWidth
                            variant="outlined"
                            onClick={() => {
                                setSearchTerm('');
                                setFilterClass('');
                                setFilterStatus('');
                            }}
                        >
                            Xóa bộ lọc
                        </Button>
                    </Grid>
                </Grid>
            </Paper>

            {/* Students Table */}
            <TableContainer component={Paper} sx={{ borderRadius: "10px" }}>
                <Table>
                    <TableHead>
                        <TableRow>
                            <TableCell>Học sinh</TableCell>
                            <TableCell>Mã HS</TableCell>
                            <TableCell>Lớp học</TableCell>
                            <TableCell>Liên hệ</TableCell>
                            <TableCell>Điểm TB</TableCell>
                            <TableCell>Trạng thái</TableCell>
                            <TableCell align="center">Thao tác</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {filteredStudents
                            .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                            .map((student) => (
                                <TableRow key={student.id}>
                                    <TableCell>
                                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                            <Avatar sx={{ mr: 2 }}>
                                                {student.firstName.charAt(0)}
                                            </Avatar>
                                            <Box>
                                                <Typography variant="body2" fontWeight="bold">
                                                    {student.firstName} {student.lastName}
                                                </Typography>
                                                <Typography variant="caption" color="textSecondary">
                                                    Tham gia: {student.joinDate}
                                                </Typography>
                                            </Box>
                                        </Box>
                                    </TableCell>
                                    <TableCell>
                                        <Typography variant="body2" fontWeight="bold">
                                            {student.studentId}
                                        </Typography>
                                    </TableCell>
                                    <TableCell>
                                        <Chip label={student.className} variant="outlined" size="small" />
                                    </TableCell>
                                    <TableCell>
                                        <Box>
                                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                                                <EmailIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                                <Typography variant="caption">
                                                    {student.email}
                                                </Typography>
                                            </Box>
                                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                                <PhoneIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                                <Typography variant="caption">
                                                    {student.phone}
                                                </Typography>
                                            </Box>
                                        </Box>
                                    </TableCell>
                                    <TableCell>
                                        <Typography
                                            variant="body2"
                                            fontWeight="bold"
                                            sx={{ color: getScoreColor(student.avgScore) }}
                                        >
                                            {student.avgScore.toFixed(1)}
                                        </Typography>
                                        <Typography variant="caption" color="textSecondary">
                                            {student.assignments} BT, {student.quizzes} KT
                                        </Typography>
                                    </TableCell>
                                    <TableCell>
                                        {getStatusChip(student.status)}
                                    </TableCell>
                                    <TableCell align="center">
                                        <Tooltip title="Chỉnh sửa">
                                            <IconButton
                                                size="small"
                                                onClick={() => handleOpenDialog(student)}
                                            >
                                                <EditIcon />
                                            </IconButton>
                                        </Tooltip>
                                        <Tooltip title={student.status === 'active' ? 'Tạm dừng' : 'Kích hoạt'}>
                                            <IconButton
                                                size="small"
                                                color={student.status === 'active' ? 'warning' : 'success'}
                                                onClick={() => handleToggleStatus(student.id)}
                                            >
                                                {student.status === 'active' ? <BlockIcon /> : <CheckCircleIcon />}
                                            </IconButton>
                                        </Tooltip>
                                        <Tooltip title="Xóa">
                                            <IconButton
                                                size="small"
                                                color="error"
                                                onClick={() => handleDeleteStudent(student.id)}
                                            >
                                                <DeleteIcon />
                                            </IconButton>
                                        </Tooltip>
                                    </TableCell>
                                </TableRow>
                            ))}
                    </TableBody>
                </Table>
                <TablePagination
                    rowsPerPageOptions={[5, 10, 25]}
                    component="div"
                    count={filteredStudents.length}
                    rowsPerPage={rowsPerPage}
                    page={page}
                    onPageChange={handleChangePage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                    labelRowsPerPage="Số hàng mỗi trang:"
                />
            </TableContainer>

            {/* Add/Edit Student Dialog */}
            <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
                <DialogTitle>
                    {editingStudent ? 'Chỉnh sửa học sinh' : 'Thêm học sinh mới'}
                </DialogTitle>
                <DialogContent>
                    <Grid container spacing={2} sx={{ mt: 1 }}>
                        <Grid size={{ xs: 12, sm: 6 }}>
                            <TextField
                                fullWidth
                                label="Họ *"
                                value={formData.firstName}
                                onChange={(e) => handleFormChange('firstName', e.target.value)}
                            />
                        </Grid>
                        <Grid size={{ xs: 12, sm: 6 }}>
                            <TextField
                                fullWidth
                                label="Tên *"
                                value={formData.lastName}
                                onChange={(e) => handleFormChange('lastName', e.target.value)}
                            />
                        </Grid>

                        <Grid size={{ xs: 12, sm: 6 }}>
                            <TextField
                                fullWidth
                                label="Email *"
                                type="email"
                                value={formData.email}
                                onChange={(e) => handleFormChange('email', e.target.value)}
                            />
                        </Grid>
                        <Grid size={{ xs: 12, sm: 6 }}>
                            <TextField
                                fullWidth
                                label="Số điện thoại"
                                value={formData.phone}
                                onChange={(e) => handleFormChange('phone', e.target.value)}
                            />
                        </Grid>

                        <Grid size={{ xs: 12, sm: 6 }}>
                            <FormControl fullWidth>
                                <InputLabel>Lớp học *</InputLabel>
                                <Select
                                    value={formData.classId}
                                    onChange={(e) => handleFormChange('classId', e.target.value)}
                                    label="Lớp học *"
                                >
                                    {classes.map(cls => (
                                        <MenuItem key={cls.id} value={cls.id}>{cls.name}</MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        </Grid>
                        <Grid size={{ xs: 12, sm: 6 }}>
                            <TextField
                                fullWidth
                                label="Mã học sinh"
                                value={formData.studentId}
                                onChange={(e) => handleFormChange('studentId', e.target.value)}
                            />
                        </Grid>

                        <Grid size={{ xs: 12, sm: 6 }}>
                            <TextField
                                fullWidth
                                label="Tên phụ huynh"
                                value={formData.parentName}
                                onChange={(e) => handleFormChange('parentName', e.target.value)}
                            />
                        </Grid>
                        <Grid size={{ xs: 12, sm: 6 }}>
                            <TextField
                                fullWidth
                                label="SĐT phụ huynh"
                                value={formData.parentPhone}
                                onChange={(e) => handleFormChange('parentPhone', e.target.value)}
                            />
                        </Grid>

                        <Grid size={{ xs: 12 }}>
                            <TextField
                                fullWidth
                                label="Địa chỉ"
                                multiline
                                rows={2}
                                value={formData.address}
                                onChange={(e) => handleFormChange('address', e.target.value)}
                            />
                        </Grid>

                        <Grid size={{ xs: 12 }}>
                            <FormControl fullWidth>
                                <InputLabel>Trạng thái</InputLabel>
                                <Select
                                    value={formData.status}
                                    onChange={(e) => handleFormChange('status', e.target.value)}
                                    label="Trạng thái"
                                >
                                    <MenuItem value="active">Hoạt động</MenuItem>
                                    <MenuItem value="inactive">Tạm dừng</MenuItem>
                                </Select>
                            </FormControl>
                        </Grid>
                    </Grid>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseDialog}>Hủy</Button>
                    <Button onClick={handleSaveStudent} variant="contained">
                        {editingStudent ? 'Cập nhật' : 'Thêm'}
                    </Button>
                </DialogActions>
            </Dialog>

            {/* Snackbar */}
            <Snackbar
                open={snackbar.open}
                autoHideDuration={6000}
                onClose={() => setSnackbar({ ...snackbar, open: false })}
            >
                <Alert
                    onClose={() => setSnackbar({ ...snackbar, open: false })}
                    severity={snackbar.severity}
                    sx={{ width: '100%' }}
                >
                    {snackbar.message}
                </Alert>
            </Snackbar>
        </Box>
    );
}

export default StudentManagement;
