import React from 'react';
import { Box, Typography } from '@mui/material';
import { CKEditor } from "ckeditor4-react";

function RichTextEditor({ value, onChange, label, height = 400 }) {
    const handleEditorChange = (event) => {
        const data = event.editor.getData();
        if (onChange) {
            onChange(data);
        }
    };

    return (
        <Box>
            {label && (
                <Typography variant="body2" gutterBottom sx={{ mb: 2, fontWeight: 500 }}>
                    {label}
                </Typography>
            )}
            <Box sx={{
                border: '1px solid #ddd',
                borderRadius: '10px',
                overflow: 'hidden',
                '& .cke': {
                    border: 'none !important',
                    borderRadius: '10px !important'
                },
                '& .cke_top': {
                    borderRadius: '10px 10px 0 0 !important',
                    background: '#f5f5f5 !important'
                },
                '& .cke_bottom': {
                    borderRadius: '0 0 10px 10px !important'
                },
                '& .cke_contents': {
                    borderRadius: '0 !important'
                }
            }}>
                <CKEditor
                    data={value || ''}
                    onChange={handleEditorChange}
                    config={{
                        versionCheck: false,
                        toolbar: [
                            { name: 'document', items: ['Source'] },
                            { name: 'clipboard', items: ['Cut', 'Copy', 'Paste', 'PasteText', 'PasteFromWord', '-', 'Undo', 'Redo'] },
                            '/',
                            { name: 'basicstyles', items: ['Bold', 'Italic', 'Underline', 'Strike', 'Subscript', 'Superscript', '-', 'RemoveFormat'] },
                            { name: 'paragraph', items: ['NumberedList', 'BulletedList', '-', 'Outdent', 'Indent', '-', 'Blockquote', '-', 'JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock'] },
                            { name: 'links', items: ['Link', 'Unlink'] },
                            { name: 'insert', items: ['Image', 'Table', 'HorizontalRule', 'SpecialChar'] },
                            '/',
                            { name: 'styles', items: ['Styles', 'Format', 'Font', 'FontSize'] },
                            { name: 'colors', items: ['TextColor', 'BGColor'] },
                            { name: 'tools', items: ['Maximize'] }
                        ],
                        height: height,
                        removePlugins: 'elementspath',
                        resize_enabled: false,
                        allowedContent: true,
                        format_tags: 'p;h1;h2;h3;h4;h5;h6;pre;div',
                        fontSize_sizes: '8/8px;9/9px;10/10px;11/11px;12/12px;14/14px;16/16px;18/18px;20/20px;22/22px;24/24px;26/26px;28/28px;36/36px;48/48px;72/72px',
                        colorButton_colors: '000,800000,8B4513,2F4F4F,008080,000080,4B0082,696969,' +
                            'B22222,A52A2A,DAA520,006400,40E0D0,0000CD,800080,808080,' +
                            'F00,FF8C00,FFD700,008000,0FF,00F,EE82EE,A9A9A9,' +
                            'FFA07A,FFA500,FFFF00,00FF00,AFEEEE,ADD8E6,DDA0DD,D3D3D3,' +
                            'FFF0F5,FAEBD7,FFFFE0,F0FFF0,F0FFFF,F0F8FF,E6E6FA,FFF',
                        extraPlugins: 'font,colorbutton,justify',
                        language: 'vi'
                    }}
                />
            </Box>
        </Box>
    );
}

export default RichTextEditor;
