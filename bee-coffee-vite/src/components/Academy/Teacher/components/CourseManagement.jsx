import React, { useState, useEffect } from 'react';
import CourseBuilder from './CourseBuilder';
import {
    Box,
    Typography,
    Button,
    Paper,
    Grid,
    Card,
    CardContent,
    CardActions,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    TextField,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Chip,
    IconButton,
    Tooltip,
    Alert,
    Snackbar,
    List,
    ListItem,
    ListItemText,
    ListItemSecondaryAction,
    Divider,
    Avatar,
    Badge,
    LinearProgress,
    Tab,
    Tabs
} from '@mui/material';
import {
    Add as AddIcon,
    Edit as EditIcon,
    Delete as DeleteIcon,
    Visibility as ViewIcon,
    School as SchoolIcon,
    MenuBook as LessonIcon,
    Assignment as AssignmentIcon,
    Quiz as QuizIcon,
    Games as TurboWarpIcon,
    People as PeopleIcon,
    PlayArrow as PlayIcon,
    Publish as PublishIcon,
    Drafts as DraftIcon,
    MoreVert as MoreVertIcon,
    ContentCopy as CopyIcon
} from '@mui/icons-material';

function TabPanel({ children, value, index, ...other }) {
    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`course-tabpanel-${index}`}
            aria-labelledby={`course-tab-${index}`}
            {...other}
        >
            {value === index && (
                <Box sx={{ p: 3 }}>
                    {children}
                </Box>
            )}
        </div>
    );
}

function CourseManagement({ user }) {
    const [courses, setCourses] = useState([]);
    const [openDialog, setOpenDialog] = useState(false);
    const [editingCourse, setEditingCourse] = useState(null);
    const [tabValue, setTabValue] = useState(0);
    const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
    const [showCourseBuilder, setShowCourseBuilder] = useState(false);
    const [selectedCourseId, setSelectedCourseId] = useState(null);

    // Form state
    const [formData, setFormData] = useState({
        title: '',
        description: '',
        subject: '',
        grade: '',
        duration: '',
        difficulty: 'beginner',
        thumbnail: '',
        objectives: [],
        prerequisites: [],
        isPublished: false,
        price: 0,
        originalPrice: 0
    });

    const subjects = ['Toán học', 'Vật lý', 'Hóa học', 'Sinh học', 'Tin học', 'STEM'];
    const grades = ['Lớp 6', 'Lớp 7', 'Lớp 8', 'Lớp 9', 'Lớp 10', 'Lớp 11', 'Lớp 12'];
    const difficulties = [
        { value: 'beginner', label: 'Cơ bản', color: 'success' },
        { value: 'intermediate', label: 'Trung bình', color: 'warning' },
        { value: 'advanced', label: 'Nâng cao', color: 'error' }
    ];

    useEffect(() => {
        // Giả lập dữ liệu khóa học
        const mockCourses = [
            {
                id: 1,
                title: 'Toán học nâng cao lớp 8',
                description: 'Khóa học toán nâng cao dành cho học sinh khá giỏi lớp 8',
                subject: 'Toán học',
                grade: 'Lớp 8',
                duration: '3 tháng',
                difficulty: 'intermediate',
                thumbnail: '/images/math-course.jpg',
                objectives: [
                    'Nắm vững các phương trình bậc hai',
                    'Hiểu về hệ phương trình tuyến tính',
                    'Áp dụng toán học vào thực tế'
                ],
                prerequisites: ['Hoàn thành toán lớp 7', 'Điểm trung bình >= 7.0'],
                isPublished: true,
                enrolledStudents: 25,
                totalLessons: 12,
                totalAssignments: 8,
                completionRate: 78,
                avgRating: 4.5,
                createdAt: '2024-01-10',
                price: 899000,
                originalPrice: 1200000,
                lessons: [
                    { id: 1, title: 'Phương trình bậc hai', type: 'lesson', order: 1, isCompleted: true },
                    { id: 2, title: 'Bài tập phương trình', type: 'quiz', order: 2, isCompleted: true },
                    { id: 3, title: 'Hệ phương trình', type: 'lesson', order: 3, isCompleted: false }
                ],
                status: 'published'
            },
            {
                id: 2,
                title: 'Lập trình Scratch cơ bản',
                description: 'Học lập trình từ cơ bản với Scratch',
                subject: 'Tin học',
                grade: 'Lớp 7',
                duration: '2 tháng',
                difficulty: 'beginner',
                thumbnail: '/images/scratch-course.jpg',
                objectives: [
                    'Hiểu cơ bản về lập trình',
                    'Tạo được game đơn giản',
                    'Phát triển tư duy logic'
                ],
                prerequisites: ['Biết sử dụng máy tính cơ bản'],
                isPublished: false,
                enrolledStudents: 0,
                totalLessons: 8,
                totalAssignments: 5,
                completionRate: 0,
                avgRating: 0,
                createdAt: '2024-01-15',
                price: 0,
                originalPrice: 0,
                lessons: [
                    { id: 4, title: 'Giới thiệu Scratch', type: 'lesson', order: 1, isCompleted: false },
                    { id: 5, title: 'Tạo sprite đầu tiên', type: 'lesson', order: 2, isCompleted: false },
                    { id: 6, title: 'Bài tập tạo animation', type: 'turbowarp', order: 3, isCompleted: false }
                ],
                status: 'draft'
            },
            {
                id: 3,
                title: 'Vật lý thí nghiệm 9',
                description: 'Khóa học vật lý với nhiều thí nghiệm thực hành',
                subject: 'Vật lý',
                grade: 'Lớp 9',
                duration: '4 tháng',
                difficulty: 'intermediate',
                thumbnail: '/images/physics-course.jpg',
                objectives: [
                    'Hiểu các định luật vật lý cơ bản',
                    'Thực hiện thí nghiệm an toàn',
                    'Áp dụng vật lý vào đời sống'
                ],
                prerequisites: ['Hoàn thành vật lý lớp 8', 'Có kiến thức toán cơ bản'],
                isPublished: true,
                enrolledStudents: 18,
                totalLessons: 15,
                totalAssignments: 10,
                completionRate: 45,
                avgRating: 4.2,
                createdAt: '2024-01-08',
                price: 1299000,
                originalPrice: 1800000,
                lessons: [
                    { id: 7, title: 'Định luật Newton', type: 'lesson', order: 1, isCompleted: true },
                    { id: 8, title: 'Thí nghiệm lực ma sát', type: 'lesson', order: 2, isCompleted: true },
                    { id: 9, title: 'Kiểm tra định luật Newton', type: 'quiz', order: 3, isCompleted: false }
                ],
                status: 'published'
            }
        ];
        setCourses(mockCourses);
    }, []);

    const handleOpenDialog = (course = null) => {
        if (course) {
            setEditingCourse(course);
            setFormData({
                title: course.title,
                description: course.description,
                subject: course.subject,
                grade: course.grade,
                duration: course.duration,
                difficulty: course.difficulty,
                thumbnail: course.thumbnail,
                objectives: course.objectives,
                prerequisites: course.prerequisites,
                isPublished: course.isPublished,
                price: course.price || 0,
                originalPrice: course.originalPrice || 0
            });
        } else {
            setEditingCourse(null);
            setFormData({
                title: '',
                description: '',
                subject: '',
                grade: '',
                duration: '',
                difficulty: 'beginner',
                thumbnail: '',
                objectives: [],
                prerequisites: [],
                isPublished: false,
                price: 0,
                originalPrice: 0
            });
        }
        setTabValue(0);
        setOpenDialog(true);
    };

    const handleCloseDialog = () => {
        setOpenDialog(false);
        setEditingCourse(null);
    };

    const handleFormChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const handleSaveCourse = () => {
        if (!formData.title || !formData.subject || !formData.grade) {
            setSnackbar({
                open: true,
                message: 'Vui lòng điền đầy đủ thông tin bắt buộc!',
                severity: 'error'
            });
            return;
        }

        const courseData = {
            ...formData,
            id: editingCourse ? editingCourse.id : Date.now(),
            enrolledStudents: editingCourse ? editingCourse.enrolledStudents : 0,
            totalLessons: editingCourse ? editingCourse.totalLessons : 0,
            totalAssignments: editingCourse ? editingCourse.totalAssignments : 0,
            completionRate: editingCourse ? editingCourse.completionRate : 0,
            avgRating: editingCourse ? editingCourse.avgRating : 0,
            createdAt: editingCourse ? editingCourse.createdAt : new Date().toISOString().split('T')[0],
            lessons: editingCourse ? editingCourse.lessons : [],
            status: formData.isPublished ? 'published' : 'draft'
        };

        if (editingCourse) {
            setCourses(prev => prev.map(c => c.id === editingCourse.id ? courseData : c));
            setSnackbar({
                open: true,
                message: 'Cập nhật khóa học thành công!',
                severity: 'success'
            });
        } else {
            setCourses(prev => [...prev, courseData]);
            setSnackbar({
                open: true,
                message: 'Tạo khóa học thành công!',
                severity: 'success'
            });
        }

        handleCloseDialog();
    };

    const handleDeleteCourse = (id) => {
        if (window.confirm('Bạn có chắc chắn muốn xóa khóa học này?')) {
            setCourses(prev => prev.filter(c => c.id !== id));
            setSnackbar({
                open: true,
                message: 'Xóa khóa học thành công!',
                severity: 'success'
            });
        }
    };

    const handleTogglePublish = (id) => {
        setCourses(prev => prev.map(course =>
            course.id === id
                ? {
                    ...course,
                    isPublished: !course.isPublished,
                    status: !course.isPublished ? 'published' : 'draft'
                }
                : course
        ));
        setSnackbar({
            open: true,
            message: 'Cập nhật trạng thái thành công!',
            severity: 'success'
        });
    };

    const handleOpenCourseBuilder = (courseId) => {
        setSelectedCourseId(courseId);
        setShowCourseBuilder(true);
    };

    const handleCloseCourseBuilder = () => {
        setShowCourseBuilder(false);
        setSelectedCourseId(null);
    };

    const getStatusChip = (status) => {
        return status === 'published'
            ? <Chip label="Đã xuất bản" color="success" size="small" icon={<PublishIcon />} />
            : <Chip label="Bản nháp" color="warning" size="small" icon={<DraftIcon />} />;
    };

    const getDifficultyChip = (difficulty) => {
        const difficultyInfo = difficulties.find(d => d.value === difficulty);
        return (
            <Chip
                label={difficultyInfo?.label || difficulty}
                color={difficultyInfo?.color || 'default'}
                size="small"
            />
        );
    };

    const getSubjectColor = (subject) => {
        const colors = {
            'Toán học': '#1976d2',
            'Vật lý': '#388e3c',
            'Hóa học': '#f57c00',
            'Sinh học': '#7b1fa2',
            'Tin học': '#d32f2f',
            'STEM': '#795548'
        };
        return colors[subject] || '#757575';
    };

    const getContentTypeIcon = (type) => {
        switch (type) {
            case 'lesson': return <LessonIcon />;
            case 'quiz': return <QuizIcon />;
            case 'turbowarp': return <TurboWarpIcon />;
            default: return <AssignmentIcon />;
        }
    };

    // Hiển thị CourseBuilder nếu được chọn
    if (showCourseBuilder && selectedCourseId) {
        return (
            <CourseBuilder
                courseId={selectedCourseId}
                onClose={handleCloseCourseBuilder}
            />
        );
    }

    return (
        <Box>
            {/* Header */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h4">
                    Quản lý khóa học
                </Typography>
                <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={() => handleOpenDialog()}
                    sx={{ borderRadius: '10px' }}
                >
                    Tạo khóa học mới
                </Button>
            </Box>

            {/* Statistics */}
            <Grid container spacing={3} sx={{ mb: 3 }}>
                <Grid item xs={12} sm={6} md={3}>
                    <Card sx={{ borderRadius: '10px' }}>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Tổng khóa học
                            </Typography>
                            <Typography variant="h4">
                                {courses.length}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                    <Card sx={{ borderRadius: '10px' }}>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Đã xuất bản
                            </Typography>
                            <Typography variant="h4" color="success.main">
                                {courses.filter(c => c.status === 'published').length}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                    <Card sx={{ borderRadius: '10px' }}>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Tổng học sinh
                            </Typography>
                            <Typography variant="h4">
                                {courses.reduce((sum, c) => sum + c.enrolledStudents, 0)}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                    <Card sx={{ borderRadius: '10px' }}>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Đánh giá TB
                            </Typography>
                            <Typography variant="h4">
                                {courses.length > 0
                                    ? (courses.reduce((sum, c) => sum + c.avgRating, 0) / courses.length).toFixed(1)
                                    : '0.0'
                                }
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
            </Grid>

            {/* Courses Grid */}
            <Grid container spacing={3}>
                {courses.map((course) => (
                    <Grid item xs={12} md={6} lg={4} key={course.id}>
                        <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column', borderRadius: '10px' }}>
                            <CardContent sx={{ flexGrow: 1 }}>
                                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                                    <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
                                        {course.title}
                                    </Typography>
                                    <Avatar sx={{ bgcolor: getSubjectColor(course.subject), width: 40, height: 40 }}>
                                        {course.subject.charAt(0)}
                                    </Avatar>
                                </Box>

                                <Box sx={{ mb: 2 }}>
                                    {getStatusChip(course.status)}
                                    <Chip
                                        label={course.grade}
                                        variant="outlined"
                                        size="small"
                                        sx={{ ml: 1 }}
                                    />
                                </Box>

                                <Box sx={{ mb: 2 }}>
                                    {getDifficultyChip(course.difficulty)}
                                </Box>

                                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                                    {course.description}
                                </Typography>

                                <Box sx={{ mb: 2 }}>
                                    <Typography variant="body2" sx={{ mb: 1 }}>
                                        <strong>Thời lượng:</strong> {course.duration}
                                    </Typography>
                                    <Typography variant="body2" sx={{ mb: 1 }}>
                                        <PeopleIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                        {course.enrolledStudents} học sinh
                                    </Typography>
                                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                                        <Typography variant="h6" color="primary" sx={{ fontWeight: 'bold' }}>
                                            {course.price === 0 ? 'Miễn phí' : new Intl.NumberFormat('vi-VN', {
                                                style: 'currency',
                                                currency: 'VND'
                                            }).format(course.price)}
                                        </Typography>
                                        {course.originalPrice > course.price && course.price > 0 && (
                                            <Typography
                                                variant="body2"
                                                sx={{
                                                    textDecoration: 'line-through',
                                                    color: 'text.secondary',
                                                    ml: 1
                                                }}
                                            >
                                                {new Intl.NumberFormat('vi-VN', {
                                                    style: 'currency',
                                                    currency: 'VND'
                                                }).format(course.originalPrice)}
                                            </Typography>
                                        )}
                                    </Box>
                                </Box>

                                <Box sx={{ mb: 2 }}>
                                    <Typography variant="body2" color="textSecondary" gutterBottom>
                                        Nội dung:
                                    </Typography>
                                    <Grid container spacing={1}>
                                        <Grid item xs={6}>
                                            <Typography variant="caption" display="block" align="center">
                                                Bài học
                                            </Typography>
                                            <Typography variant="body2" align="center" fontWeight="bold">
                                                {course.totalLessons}
                                            </Typography>
                                        </Grid>
                                        <Grid item xs={6}>
                                            <Typography variant="caption" display="block" align="center">
                                                Bài tập
                                            </Typography>
                                            <Typography variant="body2" align="center" fontWeight="bold">
                                                {course.totalAssignments}
                                            </Typography>
                                        </Grid>
                                    </Grid>
                                </Box>

                                {course.status === 'published' && course.completionRate > 0 && (
                                    <Box sx={{ mb: 2 }}>
                                        <Typography variant="body2" color="textSecondary" gutterBottom>
                                            Tỷ lệ hoàn thành: {course.completionRate}%
                                        </Typography>
                                        <LinearProgress
                                            variant="determinate"
                                            value={course.completionRate}
                                            sx={{ height: 6, borderRadius: 3 }}
                                        />
                                    </Box>
                                )}

                                {course.lessons.length > 0 && (
                                    <Box>
                                        <Typography variant="body2" color="textSecondary" gutterBottom>
                                            Nội dung gần đây:
                                        </Typography>
                                        <List dense>
                                            {course.lessons.slice(0, 3).map((lesson) => (
                                                <ListItem key={lesson.id} sx={{ px: 0 }}>
                                                    <Box sx={{ display: 'flex', alignItems: 'center', mr: 1 }}>
                                                        {getContentTypeIcon(lesson.type)}
                                                    </Box>
                                                    <ListItemText
                                                        primary={lesson.title}
                                                        primaryTypographyProps={{ variant: 'caption' }}
                                                    />
                                                    {lesson.isCompleted && (
                                                        <Chip size="small" label="✓" color="success" />
                                                    )}
                                                </ListItem>
                                            ))}
                                        </List>
                                    </Box>
                                )}
                            </CardContent>

                            <CardActions>
                                <Tooltip title="Xây dựng khóa học">
                                    <IconButton
                                        size="small"
                                        onClick={() => handleOpenCourseBuilder(course.id)}
                                    >
                                        <EditIcon />
                                    </IconButton>
                                </Tooltip>
                                <Tooltip title="Xem trước">
                                    <IconButton size="small">
                                        <ViewIcon />
                                    </IconButton>
                                </Tooltip>
                                <Tooltip title={course.status === 'published' ? 'Hủy xuất bản' : 'Xuất bản'}>
                                    <IconButton
                                        size="small"
                                        color={course.status === 'published' ? 'warning' : 'success'}
                                        onClick={() => handleTogglePublish(course.id)}
                                    >
                                        <PublishIcon />
                                    </IconButton>
                                </Tooltip>
                                <Tooltip title="Sao chép">
                                    <IconButton size="small">
                                        <CopyIcon />
                                    </IconButton>
                                </Tooltip>
                                <Tooltip title="Xóa">
                                    <IconButton
                                        size="small"
                                        color="error"
                                        onClick={() => handleDeleteCourse(course.id)}
                                    >
                                        <DeleteIcon />
                                    </IconButton>
                                </Tooltip>
                            </CardActions>
                        </Card>
                    </Grid>
                ))}
            </Grid>

            {/* Create/Edit Course Dialog */}
            <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
                <DialogTitle>
                    {editingCourse ? 'Chỉnh sửa khóa học' : 'Tạo khóa học mới'}
                </DialogTitle>
                <DialogContent>
                    <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
                        <Tab label="Thông tin cơ bản" />
                        <Tab label="Mục tiêu & Yêu cầu" />
                    </Tabs>

                    <TabPanel value={tabValue} index={0}>
                        <Grid container spacing={2}>
                            <Grid item xs={12}>
                                <TextField
                                    fullWidth
                                    label="Tiêu đề khóa học *"
                                    value={formData.title}
                                    onChange={(e) => handleFormChange('title', e.target.value)}
                                />
                            </Grid>

                            <Grid item xs={12}>
                                <TextField
                                    fullWidth
                                    label="Mô tả"
                                    multiline
                                    rows={3}
                                    value={formData.description}
                                    onChange={(e) => handleFormChange('description', e.target.value)}
                                />
                            </Grid>

                            <Grid item xs={12} sm={6}>
                                <FormControl fullWidth>
                                    <InputLabel>Môn học *</InputLabel>
                                    <Select
                                        value={formData.subject}
                                        onChange={(e) => handleFormChange('subject', e.target.value)}
                                        label="Môn học *"
                                    >
                                        {subjects.map(subject => (
                                            <MenuItem key={subject} value={subject}>{subject}</MenuItem>
                                        ))}
                                    </Select>
                                </FormControl>
                            </Grid>

                            <Grid item xs={12} sm={6}>
                                <FormControl fullWidth>
                                    <InputLabel>Khối lớp *</InputLabel>
                                    <Select
                                        value={formData.grade}
                                        onChange={(e) => handleFormChange('grade', e.target.value)}
                                        label="Khối lớp *"
                                    >
                                        {grades.map(grade => (
                                            <MenuItem key={grade} value={grade}>{grade}</MenuItem>
                                        ))}
                                    </Select>
                                </FormControl>
                            </Grid>

                            <Grid item xs={12} sm={6}>
                                <TextField
                                    fullWidth
                                    label="Thời lượng"
                                    value={formData.duration}
                                    onChange={(e) => handleFormChange('duration', e.target.value)}
                                    placeholder="Ví dụ: 3 tháng, 12 tuần"
                                />
                            </Grid>

                            <Grid item xs={12} sm={6}>
                                <FormControl fullWidth>
                                    <InputLabel>Độ khó</InputLabel>
                                    <Select
                                        value={formData.difficulty}
                                        onChange={(e) => handleFormChange('difficulty', e.target.value)}
                                        label="Độ khó"
                                    >
                                        {difficulties.map(difficulty => (
                                            <MenuItem key={difficulty.value} value={difficulty.value}>
                                                {difficulty.label}
                                            </MenuItem>
                                        ))}
                                    </Select>
                                </FormControl>
                            </Grid>

                            <Grid item xs={12} sm={6}>
                                <TextField
                                    fullWidth
                                    label="Giá khóa học (VND)"
                                    type="number"
                                    value={formData.price || ''}
                                    onChange={(e) => handleFormChange('price', parseInt(e.target.value) || 0)}
                                    placeholder="Nhập 0 cho khóa học miễn phí"
                                    helperText="Để trống hoặc nhập 0 cho khóa học miễn phí"
                                />
                            </Grid>

                            <Grid item xs={12} sm={6}>
                                <TextField
                                    fullWidth
                                    label="Giá gốc (VND)"
                                    type="number"
                                    value={formData.originalPrice || ''}
                                    onChange={(e) => handleFormChange('originalPrice', parseInt(e.target.value) || 0)}
                                    placeholder="Giá trước khi giảm (tùy chọn)"
                                    helperText="Để hiển thị % giảm giá"
                                />
                            </Grid>
                        </Grid>
                    </TabPanel>

                    <TabPanel value={tabValue} index={1}>
                        <Grid container spacing={2}>
                            <Grid item xs={12}>
                                <TextField
                                    fullWidth
                                    label="Mục tiêu khóa học"
                                    multiline
                                    rows={4}
                                    value={formData.objectives.join('\n')}
                                    onChange={(e) => handleFormChange('objectives', e.target.value.split('\n').filter(obj => obj.trim()))}
                                    placeholder="Mỗi mục tiêu một dòng..."
                                    helperText="Nhập mỗi mục tiêu trên một dòng riêng"
                                />
                            </Grid>

                            <Grid item xs={12}>
                                <TextField
                                    fullWidth
                                    label="Yêu cầu tiên quyết"
                                    multiline
                                    rows={3}
                                    value={formData.prerequisites.join('\n')}
                                    onChange={(e) => handleFormChange('prerequisites', e.target.value.split('\n').filter(req => req.trim()))}
                                    placeholder="Mỗi yêu cầu một dòng..."
                                    helperText="Nhập mỗi yêu cầu trên một dòng riêng"
                                />
                            </Grid>
                        </Grid>
                    </TabPanel>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseDialog} sx={{ borderRadius: '10px' }}>Hủy</Button>
                    <Button onClick={handleSaveCourse} variant="contained" sx={{ borderRadius: '10px' }}>
                        {editingCourse ? 'Cập nhật' : 'Tạo khóa học'}
                    </Button>
                </DialogActions>
            </Dialog>

            {/* Snackbar */}
            <Snackbar
                open={snackbar.open}
                autoHideDuration={6000}
                onClose={() => setSnackbar({ ...snackbar, open: false })}
            >
                <Alert
                    onClose={() => setSnackbar({ ...snackbar, open: false })}
                    severity={snackbar.severity}
                    sx={{ width: '100%' }}
                >
                    {snackbar.message}
                </Alert>
            </Snackbar>
        </Box>
    );
}

export default CourseManagement;
