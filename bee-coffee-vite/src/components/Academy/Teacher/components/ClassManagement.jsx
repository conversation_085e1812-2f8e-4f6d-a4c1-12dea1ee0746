import React, { useState, useEffect } from 'react';
import {
    Box,
    Typography,
    Button,
    Paper,
    Card,
    CardContent,
    CardActions,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    TextField,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Chip,
    Avatar,
    IconButton,
    Tooltip,
    List,
    ListItem,
    ListItemAvatar,
    ListItemText,
    ListItemSecondaryAction,
    Divider,
    Alert,
    Snackbar,
    Badge
} from '@mui/material';
import Grid from "@mui/material/Grid2";
import {
    Add as AddIcon,
    Edit as EditIcon,
    Delete as DeleteIcon,
    People as PeopleIcon,
    School as SchoolIcon,
    Assignment as AssignmentIcon,
    Quiz as QuizIcon,
    Visibility as ViewIcon,
    PersonAdd as PersonAddIcon,
    MoreVert as MoreVertIcon
} from '@mui/icons-material';

function ClassManagement({ user }) {
    const [classes, setClasses] = useState([]);
    const [openDialog, setOpenDialog] = useState(false);
    const [openStudentDialog, setOpenStudentDialog] = useState(false);
    const [editingClass, setEditingClass] = useState(null);
    const [selectedClass, setSelectedClass] = useState(null);
    const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

    // Form state
    const [formData, setFormData] = useState({
        name: '',
        subject: '',
        grade: '',
        description: '',
        maxStudents: 30,
        schedule: '',
        room: ''
    });

    const subjects = ['Toán học', 'Vật lý', 'Hóa học', 'Sinh học', 'Tin học', 'STEM'];
    const grades = ['Lớp 6', 'Lớp 7', 'Lớp 8', 'Lớp 9', 'Lớp 10', 'Lớp 11', 'Lớp 12'];

    useEffect(() => {
        // Giả lập dữ liệu - sau này sẽ thay bằng API calls
        const mockClasses = [
            {
                id: 1,
                name: 'Toán học nâng cao 8A',
                subject: 'Toán học',
                grade: 'Lớp 8',
                description: 'Lớp học toán nâng cao dành cho học sinh khá giỏi',
                maxStudents: 25,
                currentStudents: 22,
                schedule: 'Thứ 2, 4, 6 - 14:00-15:30',
                room: 'Phòng 201',
                createdAt: '2024-01-01',
                students: [
                    { id: 1, name: 'Nguyễn Văn A', email: '<EMAIL>', joinDate: '2024-01-05' },
                    { id: 2, name: 'Trần Thị B', email: '<EMAIL>', joinDate: '2024-01-05' },
                    { id: 3, name: 'Lê Văn C', email: '<EMAIL>', joinDate: '2024-01-06' }
                ],
                assignments: 5,
                quizzes: 3
            },
            {
                id: 2,
                name: 'Vật lý thí nghiệm 9B',
                subject: 'Vật lý',
                grade: 'Lớp 9',
                description: 'Lớp học vật lý với nhiều thí nghiệm thực hành',
                maxStudents: 20,
                currentStudents: 18,
                schedule: 'Thứ 3, 5, 7 - 15:00-16:30',
                room: 'Phòng thí nghiệm',
                createdAt: '2024-01-02',
                students: [
                    { id: 4, name: 'Phạm Văn D', email: '<EMAIL>', joinDate: '2024-01-07' },
                    { id: 5, name: 'Hoàng Thị E', email: '<EMAIL>', joinDate: '2024-01-07' }
                ],
                assignments: 3,
                quizzes: 2
            },
            {
                id: 3,
                name: 'Lập trình Scratch 7C',
                subject: 'Tin học',
                grade: 'Lớp 7',
                description: 'Học lập trình cơ bản với Scratch',
                maxStudents: 30,
                currentStudents: 28,
                schedule: 'Thứ 2, 4 - 16:00-17:30',
                room: 'Phòng máy tính',
                createdAt: '2024-01-03',
                students: [
                    { id: 6, name: 'Vũ Văn F', email: '<EMAIL>', joinDate: '2024-01-08' },
                    { id: 7, name: 'Đặng Thị G', email: '<EMAIL>', joinDate: '2024-01-08' }
                ],
                assignments: 7,
                quizzes: 1
            }
        ];
        setClasses(mockClasses);
    }, []);

    const handleOpenDialog = (classData = null) => {
        if (classData) {
            setEditingClass(classData);
            setFormData({
                name: classData.name,
                subject: classData.subject,
                grade: classData.grade,
                description: classData.description,
                maxStudents: classData.maxStudents,
                schedule: classData.schedule,
                room: classData.room
            });
        } else {
            setEditingClass(null);
            setFormData({
                name: '',
                subject: '',
                grade: '',
                description: '',
                maxStudents: 30,
                schedule: '',
                room: ''
            });
        }
        setOpenDialog(true);
    };

    const handleCloseDialog = () => {
        setOpenDialog(false);
        setEditingClass(null);
    };

    const handleFormChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const handleSaveClass = () => {
        if (!formData.name || !formData.subject || !formData.grade) {
            setSnackbar({
                open: true,
                message: 'Vui lòng điền đầy đủ thông tin bắt buộc!',
                severity: 'error'
            });
            return;
        }

        const classData = {
            ...formData,
            id: editingClass ? editingClass.id : Date.now(),
            currentStudents: editingClass ? editingClass.currentStudents : 0,
            createdAt: editingClass ? editingClass.createdAt : new Date().toISOString().split('T')[0],
            students: editingClass ? editingClass.students : [],
            assignments: editingClass ? editingClass.assignments : 0,
            quizzes: editingClass ? editingClass.quizzes : 0
        };

        if (editingClass) {
            setClasses(prev => prev.map(c => c.id === editingClass.id ? classData : c));
            setSnackbar({
                open: true,
                message: 'Cập nhật lớp học thành công!',
                severity: 'success'
            });
        } else {
            setClasses(prev => [...prev, classData]);
            setSnackbar({
                open: true,
                message: 'Tạo lớp học thành công!',
                severity: 'success'
            });
        }

        handleCloseDialog();
    };

    const handleDeleteClass = (id) => {
        if (window.confirm('Bạn có chắc chắn muốn xóa lớp học này?')) {
            setClasses(prev => prev.filter(c => c.id !== id));
            setSnackbar({
                open: true,
                message: 'Xóa lớp học thành công!',
                severity: 'success'
            });
        }
    };

    const handleViewStudents = (classData) => {
        setSelectedClass(classData);
        setOpenStudentDialog(true);
    };

    const getSubjectColor = (subject) => {
        const colors = {
            'Toán học': '#1976d2',
            'Vật lý': '#388e3c',
            'Hóa học': '#f57c00',
            'Sinh học': '#7b1fa2',
            'Tin học': '#d32f2f',
            'STEM': '#795548'
        };
        return colors[subject] || '#757575';
    };

    const getOccupancyColor = (current, max) => {
        const percentage = (current / max) * 100;
        if (percentage >= 90) return 'error';
        if (percentage >= 70) return 'warning';
        return 'success';
    };

    return (
        <Box>
            {/* Header */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h4">
                    Quản lý lớp học
                </Typography>
                <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={() => handleOpenDialog()}
                    sx={{ borderRadius: "10px" }}
                >
                    Tạo lớp học mới
                </Button>
            </Box>

            {/* Statistics */}
            <Grid container spacing={3} sx={{ mb: 3 }}>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Card sx={{ borderRadius: "10px" }}>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Tổng số lớp
                            </Typography>
                            <Typography variant="h4">
                                {classes.length}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Card sx={{ borderRadius: "10px" }}>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Tổng học sinh
                            </Typography>
                            <Typography variant="h4">
                                {classes.reduce((sum, c) => sum + c.currentStudents, 0)}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Card sx={{ borderRadius: "10px" }}>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Bài tập
                            </Typography>
                            <Typography variant="h4">
                                {classes.reduce((sum, c) => sum + c.assignments, 0)}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Card>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Bài kiểm tra
                            </Typography>
                            <Typography variant="h4">
                                {classes.reduce((sum, c) => sum + c.quizzes, 0)}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
            </Grid>

            {/* Classes Grid */}
            <Grid container spacing={3}>
                {classes.map((classData) => (
                    <Grid size={{ xs: 12, sm: 6, md: 4 }} key={classData.id}>
                        <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column', borderRadius: "10px" }}>
                            <CardContent sx={{ flexGrow: 1 }}>
                                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                                    <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
                                        {classData.name}
                                    </Typography>
                                    <IconButton size="small">
                                        <MoreVertIcon />
                                    </IconButton>
                                </Box>

                                <Box sx={{ mb: 2 }}>
                                    <Chip
                                        label={classData.subject}
                                        sx={{
                                            bgcolor: getSubjectColor(classData.subject),
                                            color: 'white',
                                            mr: 1,
                                            mb: 1
                                        }}
                                        size="small"
                                    />
                                    <Chip
                                        label={classData.grade}
                                        variant="outlined"
                                        size="small"
                                    />
                                </Box>

                                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                                    {classData.description}
                                </Typography>

                                <Box sx={{ mb: 2 }}>
                                    <Typography variant="body2" sx={{ mb: 1 }}>
                                        <strong>Lịch học:</strong> {classData.schedule}
                                    </Typography>
                                    <Typography variant="body2" sx={{ mb: 1 }}>
                                        <strong>Phòng:</strong> {classData.room}
                                    </Typography>
                                </Box>

                                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                        <PeopleIcon sx={{ mr: 1, fontSize: 20 }} />
                                        <Typography variant="body2">
                                            {classData.currentStudents}/{classData.maxStudents}
                                        </Typography>
                                    </Box>
                                    <Chip
                                        label={`${Math.round((classData.currentStudents / classData.maxStudents) * 100)}%`}
                                        color={getOccupancyColor(classData.currentStudents, classData.maxStudents)}
                                        size="small"
                                    />
                                </Box>

                                <Box sx={{ display: 'flex', justifyContent: 'space-around', mt: 2 }}>
                                    <Box sx={{ textAlign: 'center' }}>
                                        <Badge badgeContent={classData.assignments} color="primary">
                                            <AssignmentIcon />
                                        </Badge>
                                        <Typography variant="caption" display="block">
                                            Bài tập
                                        </Typography>
                                    </Box>
                                    <Box sx={{ textAlign: 'center' }}>
                                        <Badge badgeContent={classData.quizzes} color="secondary">
                                            <QuizIcon />
                                        </Badge>
                                        <Typography variant="caption" display="block">
                                            Kiểm tra
                                        </Typography>
                                    </Box>
                                </Box>
                            </CardContent>

                            <CardActions>
                                <Button
                                    size="small"
                                    startIcon={<ViewIcon />}
                                    onClick={() => handleViewStudents(classData)}
                                >
                                    Xem học sinh
                                </Button>
                                <Button
                                    size="small"
                                    startIcon={<EditIcon />}
                                    onClick={() => handleOpenDialog(classData)}
                                >
                                    Chỉnh sửa
                                </Button>
                                <Button
                                    size="small"
                                    color="error"
                                    startIcon={<DeleteIcon />}
                                    onClick={() => handleDeleteClass(classData.id)}
                                >
                                    Xóa
                                </Button>
                            </CardActions>
                        </Card>
                    </Grid>
                ))}
            </Grid>

            {/* Add/Edit Class Dialog */}
            <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
                <DialogTitle>
                    {editingClass ? 'Chỉnh sửa lớp học' : 'Tạo lớp học mới'}
                </DialogTitle>
                <DialogContent>
                    <Grid container spacing={2} sx={{ mt: 1 }}>
                        <Grid size={{ xs: 12 }}>
                            <TextField
                                fullWidth
                                label="Tên lớp học *"
                                value={formData.name}
                                onChange={(e) => handleFormChange('name', e.target.value)}
                            />
                        </Grid>

                        <Grid size={{ xs: 12, sm: 6 }}>
                            <FormControl fullWidth>
                                <InputLabel>Môn học *</InputLabel>
                                <Select
                                    value={formData.subject}
                                    onChange={(e) => handleFormChange('subject', e.target.value)}
                                    label="Môn học *"
                                >
                                    {subjects.map(subject => (
                                        <MenuItem key={subject} value={subject}>{subject}</MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        </Grid>

                        <Grid size={{ xs: 12, sm: 6 }}>
                            <FormControl fullWidth>
                                <InputLabel>Khối lớp *</InputLabel>
                                <Select
                                    value={formData.grade}
                                    onChange={(e) => handleFormChange('grade', e.target.value)}
                                    label="Khối lớp *"
                                >
                                    {grades.map(grade => (
                                        <MenuItem key={grade} value={grade}>{grade}</MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        </Grid>

                        <Grid size={{ xs: 12 }}>
                            <TextField
                                fullWidth
                                label="Mô tả"
                                multiline
                                rows={3}
                                value={formData.description}
                                onChange={(e) => handleFormChange('description', e.target.value)}
                            />
                        </Grid>

                        <Grid size={{ xs: 12, sm: 6 }}>
                            <TextField
                                fullWidth
                                label="Số học sinh tối đa"
                                type="number"
                                value={formData.maxStudents}
                                onChange={(e) => handleFormChange('maxStudents', parseInt(e.target.value))}
                                inputProps={{ min: 1, max: 50 }}
                            />
                        </Grid>

                        <Grid size={{ xs: 12, sm: 6 }}>
                            <TextField
                                fullWidth
                                label="Phòng học"
                                value={formData.room}
                                onChange={(e) => handleFormChange('room', e.target.value)}
                            />
                        </Grid>

                        <Grid size={{ xs: 12 }}>
                            <TextField
                                fullWidth
                                label="Lịch học"
                                value={formData.schedule}
                                onChange={(e) => handleFormChange('schedule', e.target.value)}
                                placeholder="Ví dụ: Thứ 2, 4, 6 - 14:00-15:30"
                            />
                        </Grid>
                    </Grid>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseDialog}>Hủy</Button>
                    <Button onClick={handleSaveClass} variant="contained">
                        {editingClass ? 'Cập nhật' : 'Tạo lớp'}
                    </Button>
                </DialogActions>
            </Dialog>

            {/* View Students Dialog */}
            <Dialog open={openStudentDialog} onClose={() => setOpenStudentDialog(false)} maxWidth="md" fullWidth>
                <DialogTitle>
                    Danh sách học sinh - {selectedClass?.name}
                </DialogTitle>
                <DialogContent>
                    {selectedClass && (
                        <Box>
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                                <Typography variant="body1">
                                    Tổng số: {selectedClass.currentStudents}/{selectedClass.maxStudents} học sinh
                                </Typography>
                                <Button
                                    sx={{ borderRadius: "10px" }}
                                    variant="outlined"
                                    startIcon={<PersonAddIcon />}
                                    size="small"
                                >
                                    Thêm học sinh
                                </Button>
                            </Box>

                            <List>
                                {selectedClass.students?.map((student, index) => (
                                    <React.Fragment key={student.id}>
                                        <ListItem>
                                            <ListItemAvatar>
                                                <Avatar>{student.name.charAt(0)}</Avatar>
                                            </ListItemAvatar>
                                            <ListItemText
                                                primary={student.name}
                                                secondary={`${student.email} • Tham gia: ${student.joinDate}`}
                                            />
                                            <ListItemSecondaryAction>
                                                <Tooltip title="Xem chi tiết">
                                                    <IconButton edge="end">
                                                        <ViewIcon />
                                                    </IconButton>
                                                </Tooltip>
                                            </ListItemSecondaryAction>
                                        </ListItem>
                                        {index < selectedClass.students.length - 1 && <Divider />}
                                    </React.Fragment>
                                ))}
                            </List>

                            {(!selectedClass.students || selectedClass.students.length === 0) && (
                                <Alert severity="info">
                                    Chưa có học sinh nào trong lớp này.
                                </Alert>
                            )}
                        </Box>
                    )}
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setOpenStudentDialog(false)}>Đóng</Button>
                </DialogActions>
            </Dialog>

            {/* Snackbar */}
            <Snackbar
                open={snackbar.open}
                autoHideDuration={6000}
                onClose={() => setSnackbar({ ...snackbar, open: false })}
            >
                <Alert
                    onClose={() => setSnackbar({ ...snackbar, open: false })}
                    severity={snackbar.severity}
                    sx={{ width: '100%' }}
                >
                    {snackbar.message}
                </Alert>
            </Snackbar>
        </Box>
    );
}

export default ClassManagement;
