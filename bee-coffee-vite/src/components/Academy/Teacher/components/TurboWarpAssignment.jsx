import React, { useState, useEffect } from 'react';
import {
    Box,
    Typography,
    Button,
    Paper,
    Grid,
    Card,
    CardContent,
    CardActions,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    TextField,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Chip,
    IconButton,
    Tooltip,
    Alert,
    Snackbar,
    List,
    ListItem,
    ListItemText,
    ListItemSecondaryAction,
    Divider,
    Avatar,
    Badge,
    FormControlLabel,
    Switch,
    Tab,
    Tabs,
    LinearProgress,
    Link,
    Checkbox
} from '@mui/material';
import {
    Add as AddIcon,
    Edit as EditIcon,
    Delete as DeleteIcon,
    Games as GamesIcon,
    Code as CodeIcon,
    PlayArrow as PlayIcon,
    Download as DownloadIcon,
    Upload as UploadIcon,
    Visibility as ViewIcon,
    Assignment as AssignmentIcon,
    Timer as TimerIcon,
    People as PeopleIcon,
    CheckCircle as CheckIcon,
    Schedule as ScheduleIcon,
    Share as ShareIcon,
    Launch as LaunchIcon
} from '@mui/icons-material';

function TabPanel({ children, value, index, ...other }) {
    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`turbowarp-tabpanel-${index}`}
            aria-labelledby={`turbowarp-tab-${index}`}
            {...other}
        >
            {value === index && (
                <Box sx={{ p: 3 }}>
                    {children}
                </Box>
            )}
        </div>
    );
}

function TurboWarpAssignment({ user }) {
    const [assignments, setAssignments] = useState([]);
    const [classes, setClasses] = useState([]);
    const [openDialog, setOpenDialog] = useState(false);
    const [editingAssignment, setEditingAssignment] = useState(null);
    const [tabValue, setTabValue] = useState(0);
    const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

    // Form state
    const [formData, setFormData] = useState({
        title: '',
        description: '',
        classId: '',
        assignmentType: 'project', // project, challenge, tutorial
        difficulty: 'beginner',
        estimatedTime: 120,
        dueDate: '',
        instructions: '',
        starterProject: '',
        requiredFeatures: [],
        gradingCriteria: [],
        isPublished: false,
        allowCollaboration: false,
        maxSubmissions: 3
    });

    const assignmentTypes = [
        { value: 'project', label: 'Dự án tự do', icon: <GamesIcon /> },
        { value: 'challenge', label: 'Thử thách', icon: <AssignmentIcon /> },
        { value: 'tutorial', label: 'Bài hướng dẫn', icon: <CodeIcon /> }
    ];

    const difficulties = [
        { value: 'beginner', label: 'Người mới bắt đầu', color: 'success' },
        { value: 'intermediate', label: 'Trung cấp', color: 'warning' },
        { value: 'advanced', label: 'Nâng cao', color: 'error' }
    ];

    const defaultFeatures = [
        'Sprite movement',
        'User input handling',
        'Sound effects',
        'Background music',
        'Score system',
        'Multiple levels',
        'Collision detection',
        'Animation',
        'Variables usage',
        'Custom blocks'
    ];

    const defaultCriteria = [
        'Creativity and originality',
        'Code organization',
        'User interface design',
        'Functionality',
        'Bug-free execution',
        'Documentation/comments'
    ];

    useEffect(() => {
        // Giả lập dữ liệu lớp học
        const mockClasses = [
            { id: 1, name: 'Toán học nâng cao 8A', subject: 'Toán học' },
            { id: 2, name: 'Vật lý thí nghiệm 9B', subject: 'Vật lý' },
            { id: 3, name: 'Lập trình Scratch 7C', subject: 'Tin học' }
        ];
        setClasses(mockClasses);

        // Giả lập dữ liệu bài tập TurboWarp
        const mockAssignments = [
            {
                id: 1,
                title: 'Tạo game Pong đơn giản',
                description: 'Lập trình game Pong cổ điển với 2 người chơi',
                classId: 3,
                className: 'Lập trình Scratch 7C',
                assignmentType: 'project',
                difficulty: 'intermediate',
                estimatedTime: 180,
                dueDate: '2024-01-25',
                instructions: 'Tạo một game Pong với 2 paddle và 1 quả bóng...',
                starterProject: 'https://turbowarp.org/editor?project_url=starter-pong.sb3',
                requiredFeatures: ['Sprite movement', 'Collision detection', 'Score system'],
                gradingCriteria: ['Functionality', 'Code organization', 'Creativity'],
                isPublished: true,
                submissions: 15,
                completed: 12,
                avgScore: 85.5,
                status: 'active',
                createdAt: '2024-01-15'
            },
            {
                id: 2,
                title: 'Mô phỏng chuyển động vật lý',
                description: 'Tạo mô phỏng các định luật vật lý cơ bản',
                classId: 2,
                className: 'Vật lý thí nghiệm 9B',
                assignmentType: 'challenge',
                difficulty: 'advanced',
                estimatedTime: 240,
                dueDate: '2024-01-30',
                instructions: 'Sử dụng Scratch để mô phỏng chuyển động rơi tự do...',
                starterProject: '',
                requiredFeatures: ['Variables usage', 'Animation', 'User input handling'],
                gradingCriteria: ['Scientific accuracy', 'Visual presentation', 'Interactivity'],
                isPublished: false,
                submissions: 0,
                completed: 0,
                avgScore: 0,
                status: 'draft',
                createdAt: '2024-01-18'
            }
        ];
        setAssignments(mockAssignments);
    }, []);

    const handleOpenDialog = (assignment = null) => {
        if (assignment) {
            setEditingAssignment(assignment);
            setFormData({
                title: assignment.title,
                description: assignment.description,
                classId: assignment.classId,
                assignmentType: assignment.assignmentType,
                difficulty: assignment.difficulty,
                estimatedTime: assignment.estimatedTime,
                dueDate: assignment.dueDate,
                instructions: assignment.instructions,
                starterProject: assignment.starterProject,
                requiredFeatures: assignment.requiredFeatures,
                gradingCriteria: assignment.gradingCriteria,
                isPublished: assignment.isPublished,
                allowCollaboration: assignment.allowCollaboration || false,
                maxSubmissions: assignment.maxSubmissions || 3
            });
        } else {
            setEditingAssignment(null);
            setFormData({
                title: '',
                description: '',
                classId: '',
                assignmentType: 'project',
                difficulty: 'beginner',
                estimatedTime: 120,
                dueDate: '',
                instructions: '',
                starterProject: '',
                requiredFeatures: [],
                gradingCriteria: [],
                isPublished: false,
                allowCollaboration: false,
                maxSubmissions: 3
            });
        }
        setTabValue(0);
        setOpenDialog(true);
    };

    const handleCloseDialog = () => {
        setOpenDialog(false);
        setEditingAssignment(null);
    };

    const handleFormChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const handleFeatureToggle = (feature) => {
        setFormData(prev => ({
            ...prev,
            requiredFeatures: prev.requiredFeatures.includes(feature)
                ? prev.requiredFeatures.filter(f => f !== feature)
                : [...prev.requiredFeatures, feature]
        }));
    };

    const handleCriteriaToggle = (criteria) => {
        setFormData(prev => ({
            ...prev,
            gradingCriteria: prev.gradingCriteria.includes(criteria)
                ? prev.gradingCriteria.filter(c => c !== criteria)
                : [...prev.gradingCriteria, criteria]
        }));
    };

    const handleSaveAssignment = () => {
        if (!formData.title || !formData.classId || !formData.instructions) {
            setSnackbar({
                open: true,
                message: 'Vui lòng điền đầy đủ thông tin bắt buộc!',
                severity: 'error'
            });
            return;
        }

        const selectedClass = classes.find(c => c.id === formData.classId);
        const assignmentData = {
            ...formData,
            id: editingAssignment ? editingAssignment.id : Date.now(),
            className: selectedClass?.name || '',
            submissions: editingAssignment ? editingAssignment.submissions : 0,
            completed: editingAssignment ? editingAssignment.completed : 0,
            avgScore: editingAssignment ? editingAssignment.avgScore : 0,
            status: formData.isPublished ? 'active' : 'draft',
            createdAt: editingAssignment ? editingAssignment.createdAt : new Date().toISOString().split('T')[0]
        };

        if (editingAssignment) {
            setAssignments(prev => prev.map(a => a.id === editingAssignment.id ? assignmentData : a));
            setSnackbar({
                open: true,
                message: 'Cập nhật bài tập thành công!',
                severity: 'success'
            });
        } else {
            setAssignments(prev => [...prev, assignmentData]);
            setSnackbar({
                open: true,
                message: 'Tạo bài tập thành công!',
                severity: 'success'
            });
        }

        handleCloseDialog();
    };

    const handleDeleteAssignment = (id) => {
        if (window.confirm('Bạn có chắc chắn muốn xóa bài tập này?')) {
            setAssignments(prev => prev.filter(a => a.id !== id));
            setSnackbar({
                open: true,
                message: 'Xóa bài tập thành công!',
                severity: 'success'
            });
        }
    };

    const handleTogglePublish = (id) => {
        setAssignments(prev => prev.map(a =>
            a.id === id
                ? {
                    ...a,
                    isPublished: !a.isPublished,
                    status: !a.isPublished ? 'active' : 'draft'
                }
                : a
        ));
        setSnackbar({
            open: true,
            message: 'Cập nhật trạng thái thành công!',
            severity: 'success'
        });
    };

    const getStatusChip = (status) => {
        const statusMap = {
            'active': { label: 'Đang diễn ra', color: 'success' },
            'draft': { label: 'Bản nháp', color: 'warning' },
            'completed': { label: 'Đã kết thúc', color: 'info' }
        };
        const statusInfo = statusMap[status] || statusMap.draft;
        return <Chip label={statusInfo.label} color={statusInfo.color} size="small" />;
    };

    const getDifficultyChip = (difficulty) => {
        const difficultyInfo = difficulties.find(d => d.value === difficulty);
        return (
            <Chip
                label={difficultyInfo?.label || difficulty}
                color={difficultyInfo?.color || 'default'}
                size="small"
            />
        );
    };

    const getTypeIcon = (type) => {
        const typeInfo = assignmentTypes.find(t => t.value === type);
        return typeInfo?.icon || <GamesIcon />;
    };

    const getTypeLabel = (type) => {
        const typeInfo = assignmentTypes.find(t => t.value === type);
        return typeInfo?.label || 'Không xác định';
    };

    return (
        <Box>
            {/* Header */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h4">
                    Bài kiểm tra TurboWarp
                </Typography>
                <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={() => handleOpenDialog()}
                >
                    Tạo bài tập mới
                </Button>
            </Box>

            {/* Statistics */}
            <Grid container spacing={3} sx={{ mb: 3 }}>
                <Grid item xs={12} sm={6} md={3}>
                    <Card>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Tổng bài tập
                            </Typography>
                            <Typography variant="h4">
                                {assignments.length}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                    <Card>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Đang diễn ra
                            </Typography>
                            <Typography variant="h4" color="success.main">
                                {assignments.filter(a => a.status === 'active').length}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                    <Card>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Tổng nộp bài
                            </Typography>
                            <Typography variant="h4">
                                {assignments.reduce((sum, a) => sum + a.submissions, 0)}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                    <Card>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Điểm TB
                            </Typography>
                            <Typography variant="h4">
                                {assignments.length > 0
                                    ? (assignments.reduce((sum, a) => sum + a.avgScore, 0) / assignments.length).toFixed(1)
                                    : '0.0'
                                }
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
            </Grid>

            {/* Assignments Grid */}
            <Grid container spacing={3}>
                {assignments.map((assignment) => (
                    <Grid item xs={12} md={6} lg={4} key={assignment.id}>
                        <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                            <CardContent sx={{ flexGrow: 1 }}>
                                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                                    <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
                                        {assignment.title}
                                    </Typography>
                                    {getTypeIcon(assignment.assignmentType)}
                                </Box>

                                <Box sx={{ mb: 2 }}>
                                    <Chip
                                        label={assignment.className}
                                        variant="outlined"
                                        size="small"
                                        sx={{ mr: 1, mb: 1 }}
                                    />
                                    {getStatusChip(assignment.status)}
                                </Box>

                                <Box sx={{ mb: 2 }}>
                                    <Chip
                                        label={getTypeLabel(assignment.assignmentType)}
                                        size="small"
                                        sx={{ mr: 1, mb: 1 }}
                                    />
                                    {getDifficultyChip(assignment.difficulty)}
                                </Box>

                                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                                    {assignment.description}
                                </Typography>

                                <Box sx={{ mb: 2 }}>
                                    <Typography variant="body2" sx={{ mb: 1 }}>
                                        <TimerIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                        Thời gian ước tính: {assignment.estimatedTime} phút
                                    </Typography>
                                    <Typography variant="body2" sx={{ mb: 1 }}>
                                        <ScheduleIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                        Hạn nộp: {assignment.dueDate}
                                    </Typography>
                                    <Typography variant="body2">
                                        <PeopleIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                        {assignment.completed}/{assignment.submissions} đã hoàn thành
                                    </Typography>
                                </Box>

                                {assignment.requiredFeatures.length > 0 && (
                                    <Box sx={{ mb: 2 }}>
                                        <Typography variant="caption" color="textSecondary">
                                            Yêu cầu: {assignment.requiredFeatures.slice(0, 2).join(', ')}
                                            {assignment.requiredFeatures.length > 2 && '...'}
                                        </Typography>
                                    </Box>
                                )}

                                {assignment.avgScore > 0 && (
                                    <Box sx={{ mb: 1 }}>
                                        <Typography variant="body2" color="textSecondary">
                                            Điểm trung bình: {assignment.avgScore.toFixed(1)}
                                        </Typography>
                                        <LinearProgress
                                            variant="determinate"
                                            value={assignment.avgScore}
                                            sx={{ mt: 0.5 }}
                                        />
                                    </Box>
                                )}
                            </CardContent>

                            <CardActions>
                                <Tooltip title="Xem bài nộp">
                                    <IconButton size="small">
                                        <ViewIcon />
                                    </IconButton>
                                </Tooltip>
                                <Tooltip title="Chỉnh sửa">
                                    <IconButton
                                        size="small"
                                        onClick={() => handleOpenDialog(assignment)}
                                    >
                                        <EditIcon />
                                    </IconButton>
                                </Tooltip>
                                {assignment.starterProject && (
                                    <Tooltip title="Mở TurboWarp">
                                        <IconButton
                                            size="small"
                                            color="primary"
                                            component={Link}
                                            href={assignment.starterProject}
                                            target="_blank"
                                        >
                                            <LaunchIcon />
                                        </IconButton>
                                    </Tooltip>
                                )}
                                <Tooltip title={assignment.status === 'active' ? 'Dừng' : 'Bắt đầu'}>
                                    <IconButton
                                        size="small"
                                        color={assignment.status === 'active' ? 'warning' : 'success'}
                                        onClick={() => handleTogglePublish(assignment.id)}
                                    >
                                        <PlayIcon />
                                    </IconButton>
                                </Tooltip>
                                <Tooltip title="Xóa">
                                    <IconButton
                                        size="small"
                                        color="error"
                                        onClick={() => handleDeleteAssignment(assignment.id)}
                                    >
                                        <DeleteIcon />
                                    </IconButton>
                                </Tooltip>
                            </CardActions>
                        </Card>
                    </Grid>
                ))}
            </Grid>

            {/* Create/Edit Assignment Dialog */}
            <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="lg" fullWidth>
                <DialogTitle>
                    {editingAssignment ? 'Chỉnh sửa bài tập TurboWarp' : 'Tạo bài tập TurboWarp mới'}
                </DialogTitle>
                <DialogContent>
                    <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
                        <Tab label="Thông tin cơ bản" />
                        <Tab label="Hướng dẫn" />
                        <Tab label="Yêu cầu" />
                        <Tab label="Chấm điểm" />
                    </Tabs>

                    <TabPanel value={tabValue} index={0}>
                        <Grid container spacing={2}>
                            <Grid item xs={12}>
                                <TextField
                                    fullWidth
                                    label="Tiêu đề bài tập *"
                                    value={formData.title}
                                    onChange={(e) => handleFormChange('title', e.target.value)}
                                />
                            </Grid>
                            <Grid item xs={12}>
                                <TextField
                                    fullWidth
                                    label="Mô tả"
                                    multiline
                                    rows={3}
                                    value={formData.description}
                                    onChange={(e) => handleFormChange('description', e.target.value)}
                                />
                            </Grid>
                            <Grid item xs={12} sm={6}>
                                <FormControl fullWidth>
                                    <InputLabel>Lớp học *</InputLabel>
                                    <Select
                                        value={formData.classId}
                                        onChange={(e) => handleFormChange('classId', e.target.value)}
                                        label="Lớp học *"
                                    >
                                        {classes.map(cls => (
                                            <MenuItem key={cls.id} value={cls.id}>{cls.name}</MenuItem>
                                        ))}
                                    </Select>
                                </FormControl>
                            </Grid>
                            <Grid item xs={12} sm={6}>
                                <FormControl fullWidth>
                                    <InputLabel>Loại bài tập</InputLabel>
                                    <Select
                                        value={formData.assignmentType}
                                        onChange={(e) => handleFormChange('assignmentType', e.target.value)}
                                        label="Loại bài tập"
                                    >
                                        {assignmentTypes.map(type => (
                                            <MenuItem key={type.value} value={type.value}>
                                                {type.label}
                                            </MenuItem>
                                        ))}
                                    </Select>
                                </FormControl>
                            </Grid>
                            <Grid item xs={12} sm={6}>
                                <FormControl fullWidth>
                                    <InputLabel>Độ khó</InputLabel>
                                    <Select
                                        value={formData.difficulty}
                                        onChange={(e) => handleFormChange('difficulty', e.target.value)}
                                        label="Độ khó"
                                    >
                                        {difficulties.map(difficulty => (
                                            <MenuItem key={difficulty.value} value={difficulty.value}>
                                                {difficulty.label}
                                            </MenuItem>
                                        ))}
                                    </Select>
                                </FormControl>
                            </Grid>
                            <Grid item xs={12} sm={6}>
                                <TextField
                                    fullWidth
                                    label="Thời gian ước tính (phút)"
                                    type="number"
                                    value={formData.estimatedTime}
                                    onChange={(e) => handleFormChange('estimatedTime', parseInt(e.target.value))}
                                    inputProps={{ min: 30, max: 600 }}
                                />
                            </Grid>
                            <Grid item xs={12} sm={6}>
                                <TextField
                                    fullWidth
                                    label="Hạn nộp"
                                    type="datetime-local"
                                    value={formData.dueDate}
                                    onChange={(e) => handleFormChange('dueDate', e.target.value)}
                                    InputLabelProps={{ shrink: true }}
                                />
                            </Grid>
                            <Grid item xs={12} sm={6}>
                                <TextField
                                    fullWidth
                                    label="Số lần nộp tối đa"
                                    type="number"
                                    value={formData.maxSubmissions}
                                    onChange={(e) => handleFormChange('maxSubmissions', parseInt(e.target.value))}
                                    inputProps={{ min: 1, max: 10 }}
                                />
                            </Grid>
                        </Grid>
                    </TabPanel>

                    <TabPanel value={tabValue} index={1}>
                        <Grid container spacing={2}>
                            <Grid item xs={12}>
                                <TextField
                                    fullWidth
                                    label="Hướng dẫn chi tiết *"
                                    multiline
                                    rows={8}
                                    value={formData.instructions}
                                    onChange={(e) => handleFormChange('instructions', e.target.value)}
                                    placeholder="Mô tả chi tiết về bài tập, các bước thực hiện..."
                                />
                            </Grid>
                            <Grid item xs={12}>
                                <TextField
                                    fullWidth
                                    label="Link dự án mẫu (TurboWarp)"
                                    value={formData.starterProject}
                                    onChange={(e) => handleFormChange('starterProject', e.target.value)}
                                    placeholder="https://turbowarp.org/editor?project_url=..."
                                    helperText="Link đến dự án Scratch/TurboWarp mẫu cho học sinh"
                                />
                            </Grid>
                        </Grid>
                    </TabPanel>

                    <TabPanel value={tabValue} index={2}>
                        <Typography variant="h6" gutterBottom>
                            Tính năng yêu cầu
                        </Typography>
                        <Grid container spacing={1}>
                            {defaultFeatures.map((feature) => (
                                <Grid item xs={12} sm={6} md={4} key={feature}>
                                    <FormControlLabel
                                        control={
                                            <Checkbox
                                                checked={formData.requiredFeatures.includes(feature)}
                                                onChange={() => handleFeatureToggle(feature)}
                                            />
                                        }
                                        label={feature}
                                    />
                                </Grid>
                            ))}
                        </Grid>

                        <Box sx={{ mt: 3 }}>
                            <FormControlLabel
                                control={
                                    <Switch
                                        checked={formData.allowCollaboration}
                                        onChange={(e) => handleFormChange('allowCollaboration', e.target.checked)}
                                    />
                                }
                                label="Cho phép làm nhóm"
                            />
                        </Box>

                        <Box sx={{ mt: 2 }}>
                            <FormControlLabel
                                control={
                                    <Switch
                                        checked={formData.isPublished}
                                        onChange={(e) => handleFormChange('isPublished', e.target.checked)}
                                    />
                                }
                                label="Xuất bản ngay"
                            />
                        </Box>
                    </TabPanel>

                    <TabPanel value={tabValue} index={3}>
                        <Typography variant="h6" gutterBottom>
                            Tiêu chí chấm điểm
                        </Typography>
                        <Grid container spacing={1}>
                            {defaultCriteria.map((criteria) => (
                                <Grid item xs={12} sm={6} key={criteria}>
                                    <FormControlLabel
                                        control={
                                            <Checkbox
                                                checked={formData.gradingCriteria.includes(criteria)}
                                                onChange={() => handleCriteriaToggle(criteria)}
                                            />
                                        }
                                        label={criteria}
                                    />
                                </Grid>
                            ))}
                        </Grid>
                    </TabPanel>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseDialog}>Hủy</Button>
                    <Button onClick={handleSaveAssignment} variant="contained">
                        {editingAssignment ? 'Cập nhật' : 'Tạo bài tập'}
                    </Button>
                </DialogActions>
            </Dialog>

            {/* Snackbar */}
            <Snackbar
                open={snackbar.open}
                autoHideDuration={6000}
                onClose={() => setSnackbar({ ...snackbar, open: false })}
            >
                <Alert
                    onClose={() => setSnackbar({ ...snackbar, open: false })}
                    severity={snackbar.severity}
                    sx={{ width: '100%' }}
                >
                    {snackbar.message}
                </Alert>
            </Snackbar>
        </Box>
    );
}

export default TurboWarpAssignment;
