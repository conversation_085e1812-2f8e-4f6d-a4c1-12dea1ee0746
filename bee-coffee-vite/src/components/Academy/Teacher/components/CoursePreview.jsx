import React, { useState, useEffect } from 'react';
import {
    Box,
    Typography,
    Button,
    Paper,
    Grid,
    Card,
    CardContent,
    CardActions,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Chip,
    Avatar,
    List,
    ListItem,
    ListItemText,
    ListItemIcon,
    Divider,
    LinearProgress,
    Accordion,
    AccordionSummary,
    AccordionDetails,
    Alert
} from '@mui/material';
import {
    PlayArrow as PlayIcon,
    MenuBook as LessonIcon,
    Quiz as QuizIcon,
    Games as TurboWarpIcon,
    VideoLibrary as VideoIcon,
    AttachFile as FileIcon,
    Create as TextIcon,
    ExpandMore as ExpandMoreIcon,
    Schedule as ScheduleIcon,
    People as PeopleIcon,
    Star as StarIcon,
    CheckCircle as CheckCircleIcon,
    Lock as LockIcon
} from '@mui/icons-material';

function CoursePreview({ courseId, onClose, viewMode = 'teacher' }) {
    const [course, setCourse] = useState(null);
    const [courseContent, setCourseContent] = useState([]);
    const [expandedContent, setExpandedContent] = useState({});

    useEffect(() => {
        // G<PERSON><PERSON> lập load dữ liệu khóa học
        const mockCourse = {
            id: courseId,
            title: 'Toán học nâng cao lớp 8',
            description: 'Khóa học toán nâng cao dành cho học sinh khá giỏi lớp 8',
            subject: 'Toán học',
            grade: 'Lớp 8',
            duration: '3 tháng',
            difficulty: 'intermediate',
            thumbnail: '/images/math-course.jpg',
            objectives: [
                'Nắm vững các phương trình bậc hai',
                'Hiểu về hệ phương trình tuyến tính',
                'Áp dụng toán học vào thực tế'
            ],
            prerequisites: ['Hoàn thành toán lớp 7', 'Điểm trung bình >= 7.0'],
            teacher: 'Cô Nguyễn Thị A',
            enrolledStudents: 25,
            totalLessons: 12,
            totalAssignments: 8,
            completionRate: 78,
            avgRating: 4.5,
            isPublished: true
        };

        const mockContent = [
            {
                id: 1,
                type: 'lesson',
                title: 'Giới thiệu phương trình bậc hai',
                description: 'Tìm hiểu khái niệm và dạng tổng quát của phương trình bậc hai',
                contentType: 'text',
                content: `
                    <h3>Phương trình bậc hai</h3>
                    <p>Phương trình bậc hai có dạng tổng quát: ax² + bx + c = 0 (a ≠ 0)</p>
                    <h4>Công thức nghiệm:</h4>
                    <p>Δ = b² - 4ac</p>
                    <ul>
                        <li>Nếu Δ > 0: phương trình có 2 nghiệm phân biệt</li>
                        <li>Nếu Δ = 0: phương trình có nghiệm kép</li>
                        <li>Nếu Δ < 0: phương trình vô nghiệm</li>
                    </ul>
                `,
                duration: 45,
                order: 1,
                isPublished: true,
                isCompleted: viewMode === 'student' ? true : undefined
            },
            {
                id: 2,
                type: 'lesson',
                title: 'Video hướng dẫn giải phương trình',
                description: 'Video minh họa cách giải phương trình bậc hai',
                contentType: 'video',
                videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
                duration: 30,
                order: 2,
                isPublished: true,
                isCompleted: viewMode === 'student' ? true : undefined
            },
            {
                id: 3,
                type: 'quiz',
                title: 'Kiểm tra phương trình bậc hai',
                description: 'Bài kiểm tra kiến thức về phương trình bậc hai',
                questions: [
                    {
                        question: 'Phương trình x² - 5x + 6 = 0 có nghiệm là?',
                        options: ['x = 2, x = 3', 'x = 1, x = 6', 'x = -2, x = -3', 'Vô nghiệm'],
                        correctAnswer: 0
                    }
                ],
                duration: 20,
                order: 3,
                isPublished: true,
                isCompleted: viewMode === 'student' ? false : undefined,
                isLocked: viewMode === 'student' ? false : undefined
            },
            {
                id: 4,
                type: 'turbowarp',
                title: 'Tạo máy tính giải phương trình',
                description: 'Lập trình máy tính giải phương trình bậc hai bằng Scratch',
                requiredFeatures: ['User input', 'Mathematical calculations', 'Result display'],
                gradingCriteria: ['Functionality', 'User interface', 'Code organization'],
                order: 4,
                isPublished: true,
                isCompleted: viewMode === 'student' ? false : undefined,
                isLocked: viewMode === 'student' ? true : undefined
            }
        ];

        setCourse(mockCourse);
        setCourseContent(mockContent);
    }, [courseId, viewMode]);

    const handleExpandContent = (contentId) => {
        setExpandedContent(prev => ({
            ...prev,
            [contentId]: !prev[contentId]
        }));
    };

    const getContentIcon = (type, contentType) => {
        if (type === 'lesson') {
            switch (contentType) {
                case 'video': return <VideoIcon />;
                case 'file': return <FileIcon />;
                default: return <TextIcon />;
            }
        } else if (type === 'quiz') {
            return <QuizIcon />;
        } else if (type === 'turbowarp') {
            return <TurboWarpIcon />;
        }
        return <LessonIcon />;
    };

    const getSubjectColor = (subject) => {
        const colors = {
            'Toán học': '#1976d2',
            'Vật lý': '#388e3c',
            'Hóa học': '#f57c00',
            'Sinh học': '#7b1fa2',
            'Tin học': '#d32f2f',
            'STEM': '#795548'
        };
        return colors[subject] || '#757575';
    };

    const getDifficultyChip = (difficulty) => {
        const difficultyMap = {
            'beginner': { label: 'Cơ bản', color: 'success' },
            'intermediate': { label: 'Trung bình', color: 'warning' },
            'advanced': { label: 'Nâng cao', color: 'error' }
        };
        const difficultyInfo = difficultyMap[difficulty] || difficultyMap.beginner;
        return <Chip label={difficultyInfo.label} color={difficultyInfo.color} size="small" />;
    };

    const renderContentPreview = (item) => {
        if (item.type === 'lesson') {
            if (item.contentType === 'text') {
                return (
                    <Box sx={{ p: 2 }}>
                        <div dangerouslySetInnerHTML={{ __html: item.content }} />
                    </Box>
                );
            } else if (item.contentType === 'video') {
                return (
                    <Box sx={{ p: 2 }}>
                        <Box sx={{ position: 'relative', paddingBottom: '56.25%', height: 0 }}>
                            <iframe
                                src={item.videoUrl}
                                style={{
                                    position: 'absolute',
                                    top: 0,
                                    left: 0,
                                    width: '100%',
                                    height: '100%',
                                    border: 'none'
                                }}
                                allowFullScreen
                                title={item.title}
                            />
                        </Box>
                    </Box>
                );
            }
        } else if (item.type === 'quiz') {
            return (
                <Box sx={{ p: 2 }}>
                    <Typography variant="body2" gutterBottom>
                        Số câu hỏi: {item.questions?.length || 0}
                    </Typography>
                    <Typography variant="body2" gutterBottom>
                        Thời gian: {item.duration} phút
                    </Typography>
                    {viewMode === 'teacher' && item.questions?.length > 0 && (
                        <Box sx={{ mt: 2 }}>
                            <Typography variant="subtitle2" gutterBottom>
                                Câu hỏi mẫu:
                            </Typography>
                            <Typography variant="body2">
                                {item.questions[0].question}
                            </Typography>
                        </Box>
                    )}
                </Box>
            );
        } else if (item.type === 'turbowarp') {
            return (
                <Box sx={{ p: 2 }}>
                    <Typography variant="subtitle2" gutterBottom>
                        Tính năng yêu cầu:
                    </Typography>
                    <List dense>
                        {item.requiredFeatures?.map((feature, index) => (
                            <ListItem key={index} sx={{ py: 0 }}>
                                <ListItemIcon sx={{ minWidth: 20 }}>
                                    <CheckCircleIcon sx={{ fontSize: 16 }} color="success" />
                                </ListItemIcon>
                                <ListItemText primary={feature} />
                            </ListItem>
                        ))}
                    </List>
                </Box>
            );
        }
        return null;
    };

    if (!course) {
        return <Typography>Đang tải...</Typography>;
    }

    return (
        <Dialog open={true} onClose={onClose} maxWidth="lg" fullWidth>
            <DialogTitle>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="h6">
                        {viewMode === 'teacher' ? 'Xem trước khóa học' : 'Khóa học'}
                    </Typography>
                    {viewMode === 'student' && (
                        <Button variant="contained" startIcon={<PlayIcon />}>
                            Tiếp tục học
                        </Button>
                    )}
                </Box>
            </DialogTitle>
            <DialogContent>
                <Grid container spacing={3}>
                    {/* Course Info */}
                    <Grid item xs={12} md={4}>
                        <Card>
                            <CardContent>
                                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                    <Avatar sx={{ bgcolor: getSubjectColor(course.subject), mr: 2 }}>
                                        {course.subject.charAt(0)}
                                    </Avatar>
                                    <Box>
                                        <Typography variant="h6">{course.title}</Typography>
                                        <Typography variant="body2" color="textSecondary">
                                            {course.teacher}
                                        </Typography>
                                    </Box>
                                </Box>

                                <Box sx={{ mb: 2 }}>
                                    <Chip label={course.grade} variant="outlined" size="small" sx={{ mr: 1 }} />
                                    {getDifficultyChip(course.difficulty)}
                                </Box>

                                <Typography variant="body2" sx={{ mb: 2 }}>
                                    {course.description}
                                </Typography>

                                <Box sx={{ mb: 2 }}>
                                    <Typography variant="body2" sx={{ mb: 1 }}>
                                        <ScheduleIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                        Thời lượng: {course.duration}
                                    </Typography>
                                    <Typography variant="body2" sx={{ mb: 1 }}>
                                        <PeopleIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                        {course.enrolledStudents} học sinh
                                    </Typography>
                                    <Typography variant="body2">
                                        <StarIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                        {course.avgRating}/5.0 ({course.enrolledStudents} đánh giá)
                                    </Typography>
                                </Box>

                                {viewMode === 'student' && (
                                    <Box sx={{ mb: 2 }}>
                                        <Typography variant="body2" gutterBottom>
                                            Tiến độ: {course.completionRate}%
                                        </Typography>
                                        <LinearProgress 
                                            variant="determinate" 
                                            value={course.completionRate}
                                            sx={{ height: 8, borderRadius: 4 }}
                                        />
                                    </Box>
                                )}

                                <Typography variant="subtitle2" gutterBottom>
                                    Mục tiêu khóa học:
                                </Typography>
                                <List dense>
                                    {course.objectives.map((objective, index) => (
                                        <ListItem key={index} sx={{ py: 0 }}>
                                            <ListItemIcon sx={{ minWidth: 20 }}>
                                                <CheckCircleIcon sx={{ fontSize: 16 }} color="success" />
                                            </ListItemIcon>
                                            <ListItemText 
                                                primary={objective}
                                                primaryTypographyProps={{ variant: 'body2' }}
                                            />
                                        </ListItem>
                                    ))}
                                </List>

                                <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
                                    Yêu cầu tiên quyết:
                                </Typography>
                                <List dense>
                                    {course.prerequisites.map((prerequisite, index) => (
                                        <ListItem key={index} sx={{ py: 0 }}>
                                            <ListItemIcon sx={{ minWidth: 20 }}>
                                                <CheckCircleIcon sx={{ fontSize: 16 }} color="warning" />
                                            </ListItemIcon>
                                            <ListItemText 
                                                primary={prerequisite}
                                                primaryTypographyProps={{ variant: 'body2' }}
                                            />
                                        </ListItem>
                                    ))}
                                </List>
                            </CardContent>
                        </Card>
                    </Grid>

                    {/* Course Content */}
                    <Grid item xs={12} md={8}>
                        <Typography variant="h6" gutterBottom>
                            Nội dung khóa học ({courseContent.length} mục)
                        </Typography>

                        {courseContent
                            .sort((a, b) => a.order - b.order)
                            .map((item, index) => (
                                <Accordion 
                                    key={item.id}
                                    expanded={expandedContent[item.id] || false}
                                    onChange={() => handleExpandContent(item.id)}
                                    disabled={viewMode === 'student' && item.isLocked}
                                >
                                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                                        <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                                            <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>
                                                {getContentIcon(item.type, item.contentType)}
                                            </Box>
                                            <Box sx={{ flexGrow: 1 }}>
                                                <Typography variant="body1">
                                                    {item.order}. {item.title}
                                                </Typography>
                                                <Typography variant="body2" color="textSecondary">
                                                    {item.description}
                                                </Typography>
                                                {item.duration && (
                                                    <Typography variant="caption" color="textSecondary">
                                                        {item.duration} phút
                                                    </Typography>
                                                )}
                                            </Box>
                                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                                {viewMode === 'student' && item.isCompleted && (
                                                    <CheckCircleIcon color="success" />
                                                )}
                                                {viewMode === 'student' && item.isLocked && (
                                                    <LockIcon color="disabled" />
                                                )}
                                                {viewMode === 'teacher' && !item.isPublished && (
                                                    <Chip label="Bản nháp" size="small" color="warning" />
                                                )}
                                            </Box>
                                        </Box>
                                    </AccordionSummary>
                                    <AccordionDetails>
                                        {renderContentPreview(item)}
                                        {viewMode === 'student' && !item.isLocked && (
                                            <Box sx={{ mt: 2, textAlign: 'right' }}>
                                                <Button
                                                    variant="contained"
                                                    startIcon={<PlayIcon />}
                                                    size="small"
                                                >
                                                    {item.type === 'lesson' ? 'Học bài' : 
                                                     item.type === 'quiz' ? 'Làm bài kiểm tra' : 
                                                     'Làm bài tập'}
                                                </Button>
                                            </Box>
                                        )}
                                    </AccordionDetails>
                                </Accordion>
                            ))}
                    </Grid>
                </Grid>
            </DialogContent>
            <DialogActions>
                <Button onClick={onClose}>Đóng</Button>
                {viewMode === 'teacher' && (
                    <Button variant="contained">
                        Chỉnh sửa khóa học
                    </Button>
                )}
                {viewMode === 'student' && (
                    <Button variant="contained" startIcon={<PlayIcon />}>
                        Bắt đầu học
                    </Button>
                )}
            </DialogActions>
        </Dialog>
    );
}

export default CoursePreview;
