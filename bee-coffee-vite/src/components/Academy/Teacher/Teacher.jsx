import { useState } from 'react';
import {
    <PERSON>,
    Drawer,
    AppBar,
    <PERSON><PERSON><PERSON>,
    List,
    Typography,
    Divider,
    ListItem,
    ListItemButton,
    ListItemIcon,
    ListItemText,
    IconButton,
    Badge,
    Avatar,
    Menu,
    MenuItem
} from '@mui/material';
import {
    Dashboard as DashboardIcon,
    QuestionAnswer as QuestionIcon,
    School as ClassIcon,
    People as StudentsIcon,
    Notifications as NotificationIcon,
    AccountCircle as AccountIcon,
    Settings as SettingsIcon,
    Logout as LogoutIcon
} from '@mui/icons-material';

// Import các component con (sẽ tạo sau)
import TeacherDashboard from './components/TeacherDashboard';
import CourseManagement from './components/CourseManagement';

import QuestionBank from './components/QuestionBank';
import StudentManagement from './components/StudentManagement';

const drawerWidth = 280;

const menuItems = [
    { id: 'dashboard', label: 'Tổng quan', icon: <DashboardIcon /> },
    { id: 'questions', label: '<PERSON><PERSON> hàng câu hỏi', icon: <QuestionIcon /> },
    { id: 'students', label: 'Quản lý học sinh', icon: <StudentsIcon /> },
    { id: 'courses', label: 'Quản lý khóa học', icon: <ClassIcon /> },
];

function Teacher({ user }) {
    const [selectedMenu, setSelectedMenu] = useState('dashboard');
    const [anchorEl, setAnchorEl] = useState(null);

    const handleMenuClick = (menuId) => {
        setSelectedMenu(menuId);
    };

    const handleProfileMenuOpen = (event) => {
        setAnchorEl(event.currentTarget);
    };

    const handleProfileMenuClose = () => {
        setAnchorEl(null);
    };

    const renderContent = () => {
        switch (selectedMenu) {
            case 'dashboard':
                return <TeacherDashboard user={user} />;
            case 'questions':
                return <QuestionBank user={user} />;
            case 'students':
                return <StudentManagement user={user} />;
            case 'courses':
                return <CourseManagement user={user} />;
            default:
                return <TeacherDashboard user={user} />;
        }
    };

    return (
        <Box sx={{ display: 'flex' }}>
            {/* App Bar */}
            <AppBar
                position="fixed"
                sx={{
                    width: `calc(100% - ${drawerWidth}px)`,
                    ml: `${drawerWidth}px`,
                    bgcolor: '#1976d2',
                    // borderRadius: '0 0 10px 10px'
                }}
            >
                <Toolbar>
                    <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
                        Dashboard Giáo viên STEM
                    </Typography>

                    <IconButton color="inherit" sx={{ mr: 2 }}>
                        <Badge badgeContent={4} color="error">
                            <NotificationIcon />
                        </Badge>
                    </IconButton>

                    <IconButton
                        color="inherit"
                        onClick={handleProfileMenuOpen}
                    >
                        <Avatar sx={{ width: 32, height: 32 }}>
                            {user?.first_name?.charAt(0) || 'T'}
                        </Avatar>
                    </IconButton>

                    <Menu
                        anchorEl={anchorEl}
                        open={Boolean(anchorEl)}
                        onClose={handleProfileMenuClose}
                    >
                        <MenuItem onClick={handleProfileMenuClose}>
                            <ListItemIcon>
                                <AccountIcon fontSize="small" />
                            </ListItemIcon>
                            Hồ sơ cá nhân
                        </MenuItem>
                        <MenuItem onClick={handleProfileMenuClose}>
                            <ListItemIcon>
                                <SettingsIcon fontSize="small" />
                            </ListItemIcon>
                            Cài đặt
                        </MenuItem>
                        <Divider />
                        <MenuItem onClick={handleProfileMenuClose}>
                            <ListItemIcon>
                                <LogoutIcon fontSize="small" />
                            </ListItemIcon>
                            Đăng xuất
                        </MenuItem>
                    </Menu>
                </Toolbar>
            </AppBar>

            {/* Sidebar */}
            <Drawer
                sx={{
                    width: drawerWidth,
                    flexShrink: 0,
                    '& .MuiDrawer-paper': {
                        width: drawerWidth,
                        boxSizing: 'border-box',
                        bgcolor: '#f5f5f5',
                        borderRadius: '0 10px 10px 0'
                    },
                }}
                variant="permanent"
                anchor="left"
            >
                <Toolbar>
                    <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#1976d2' }}>
                        BeE STEM
                    </Typography>
                </Toolbar>
                <Divider />
                <List>
                    {menuItems.map((item) => (
                        <ListItem key={item.id} disablePadding>
                            <ListItemButton
                                selected={selectedMenu === item.id}
                                onClick={() => handleMenuClick(item.id)}
                                sx={{
                                    borderRadius: '10px',
                                    mx: 1,
                                    '&.Mui-selected': {
                                        bgcolor: '#e3f2fd',
                                        '&:hover': {
                                            bgcolor: '#bbdefb',
                                        },
                                    },
                                }}
                            >
                                <ListItemIcon sx={{ color: selectedMenu === item.id ? '#1976d2' : 'inherit' }}>
                                    {item.icon}
                                </ListItemIcon>
                                <ListItemText
                                    primary={item.label}
                                    sx={{
                                        '& .MuiListItemText-primary': {
                                            fontWeight: selectedMenu === item.id ? 'bold' : 'normal',
                                            color: selectedMenu === item.id ? '#1976d2' : 'inherit'
                                        }
                                    }}
                                />
                            </ListItemButton>
                        </ListItem>
                    ))}
                </List>
            </Drawer>

            {/* Main Content */}
            <Box
                component="main"
                sx={{
                    flexGrow: 1,
                    bgcolor: '#fafafa',
                    p: 3,
                    minHeight: '100vh'
                }}
            >
                <Toolbar />
                {renderContent()}
            </Box>
        </Box>
    );
}

export default Teacher;
