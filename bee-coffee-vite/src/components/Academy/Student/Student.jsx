import { useState } from 'react';
import {
    Box,
    Drawer,
    AppBar,
    <PERSON><PERSON><PERSON>,
    <PERSON>,
    Typography,
    Divider,
    ListItem,
    ListItemButton,
    ListItemIcon,
    ListItemText,
    IconButton,
    Badge,
    Avatar,
    Menu,
    MenuItem,
} from '@mui/material';
import {
    Dashboard as DashboardIcon,
    School as CoursesIcon,
    Assignment as AssignmentIcon,
    Quiz as QuizIcon,
    Games as TurboWarpIcon,
    BookOnline as LearningIcon,
    Notifications as NotificationIcon,
    AccountCircle as AccountIcon,
    Settings as SettingsIcon,
    Logout as LogoutIcon,
} from '@mui/icons-material';
import SchoolIcon from '@mui/icons-material/School';
// Import các component con (sẽ tạo sau)
import StudentDashboard from './components/StudentDashboard';
import StudentLearning from './components/StudentLearning';
import MyCourses from './components/MyCourses';
import CourseEnrollment from './components/CourseEnrollment';
import LearningModule from './components/LearningModule';
import TakeQuiz from './components/TakeQuiz';
import TurboWarpSubmission from './components/TurboWarpSubmission';

const drawerWidth = 280;

const menuItems = [
    { id: 'dashboard', label: 'Tổng quan', icon: <DashboardIcon /> },
    { id: 'elearning', label: 'Học tập E-Learning', icon: <SchoolIcon /> },
    // { id: 'my-courses', label: 'Khóa học của tôi', icon: <CoursesIcon /> },
    { id: 'enroll', label: 'Đăng ký khóa học', icon: <AssignmentIcon /> },
    // { id: 'learning', label: 'Học bài', icon: <LearningIcon /> },
    // { id: 'quizzes', label: 'Bài kiểm tra', icon: <QuizIcon /> },
    // { id: 'turbowarp', label: 'Bài tập TurboWarp', icon: <TurboWarpIcon /> },
];

function Student({ user }) {
    const [selectedMenu, setSelectedMenu] = useState('dashboard');
    const [anchorEl, setAnchorEl] = useState(null);

    const handleMenuClick = (menuId) => {
        setSelectedMenu(menuId);
    };

    const handleProfileMenuOpen = (event) => {
        setAnchorEl(event.currentTarget);
    };

    const handleProfileMenuClose = () => {
        setAnchorEl(null);
    };

    const renderContent = () => {
        switch (selectedMenu) {
            case 'dashboard':
                return <StudentDashboard user={user} />;
            case 'elearning':
                return <StudentLearning user={user} />;
            case 'my-courses':
                return <MyCourses user={user} />;
            case 'enroll':
                return <CourseEnrollment user={user} />;
            case 'learning':
                return <LearningModule user={user} />;
            case 'quizzes':
                return <TakeQuiz user={user} />;
            case 'turbowarp':
                return <TurboWarpSubmission user={user} />;
            default:
                return <StudentDashboard user={user} />;
        }
    };

    return (
        <Box sx={{ display: 'flex' }}>
            {/* App Bar */}
            <AppBar
                position="fixed"
                sx={{
                    width: `calc(100% - ${drawerWidth}px)`,
                    ml: `${drawerWidth}px`,
                    bgcolor: '#2e7d32',
                    // borderRadius: '0 0 10px 10px'
                }}
            >
                <Toolbar>
                    <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
                        Dashboard Học sinh
                    </Typography>

                    <IconButton color="inherit" sx={{ mr: 2 }}>
                        <Badge badgeContent={3} color="error">
                            <NotificationIcon />
                        </Badge>
                    </IconButton>

                    <IconButton
                        color="inherit"
                        onClick={handleProfileMenuOpen}
                    >
                        <Avatar sx={{ width: 32, height: 32 }}>
                            {user?.first_name?.charAt(0) || 'S'}
                        </Avatar>
                    </IconButton>

                    <Menu
                        anchorEl={anchorEl}
                        open={Boolean(anchorEl)}
                        onClose={handleProfileMenuClose}
                    >
                        <MenuItem onClick={handleProfileMenuClose}>
                            <ListItemIcon>
                                <AccountIcon fontSize="small" />
                            </ListItemIcon>
                            Hồ sơ cá nhân
                        </MenuItem>
                        <MenuItem onClick={handleProfileMenuClose}>
                            <ListItemIcon>
                                <SettingsIcon fontSize="small" />
                            </ListItemIcon>
                            Cài đặt
                        </MenuItem>
                        <Divider />
                        <MenuItem onClick={handleProfileMenuClose}>
                            <ListItemIcon>
                                <LogoutIcon fontSize="small" />
                            </ListItemIcon>
                            Đăng xuất
                        </MenuItem>
                    </Menu>
                </Toolbar>
            </AppBar>

            {/* Sidebar */}
            <Drawer
                sx={{
                    width: drawerWidth,
                    flexShrink: 0,
                    '& .MuiDrawer-paper': {
                        width: drawerWidth,
                        boxSizing: 'border-box',
                        bgcolor: '#f5f5f5',
                        borderRadius: '0 10px 10px 0'
                    },
                }}
                variant="permanent"
                anchor="left"
            >
                <Toolbar>
                    <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#2e7d32' }}>
                        BeE Learning
                    </Typography>
                </Toolbar>
                <Divider />
                <List>
                    {menuItems.map((item) => (
                        <ListItem key={item.id} disablePadding>
                            <ListItemButton
                                selected={selectedMenu === item.id}
                                onClick={() => handleMenuClick(item.id)}
                                sx={{
                                    borderRadius: '10px',
                                    mx: 1,
                                    '&.Mui-selected': {
                                        bgcolor: '#e8f5e8',
                                        '&:hover': {
                                            bgcolor: '#c8e6c9',
                                        },
                                    },
                                }}
                            >
                                <ListItemIcon sx={{ color: selectedMenu === item.id ? '#2e7d32' : 'inherit' }}>
                                    {item.icon}
                                </ListItemIcon>
                                <ListItemText
                                    primary={item.label}
                                    sx={{
                                        '& .MuiListItemText-primary': {
                                            fontWeight: selectedMenu === item.id ? 'bold' : 'normal',
                                            color: selectedMenu === item.id ? '#2e7d32' : 'inherit'
                                        }
                                    }}
                                />
                            </ListItemButton>
                        </ListItem>
                    ))}
                </List>
            </Drawer>

            {/* Main Content */}
            <Box
                component="main"
                sx={{
                    flexGrow: 1,
                    bgcolor: '#fafafa',
                    p: 3,
                    minHeight: '100vh'
                }}
            >
                <Toolbar />
                {renderContent()}
            </Box>
        </Box>
    );
}

export default Student;
