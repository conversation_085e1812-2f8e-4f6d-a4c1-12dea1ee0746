import React, { useState, useEffect } from 'react';
import {
    Box,
    Typography,
    Grid,
    Card,
    CardContent,
    CardActions,
    Button,
    Chip,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Alert,
    Snackbar,
    Paper,
    TextField,
    List,
    ListItem,
    ListItemText,
    ListItemIcon,
    Divider,
    LinearProgress,
    Link,
    FormControl,
    InputLabel,
    Select,
    MenuItem
} from '@mui/material';
import {
    Games as TurboWarpIcon,
    Assignment as AssignmentIcon,
    Schedule as ScheduleIcon,
    Person as PersonIcon,
    CheckCircle as CheckCircleIcon,
    Upload as UploadIcon,
    Download as DownloadIcon,
    Launch as LaunchIcon,
    Code as CodeIcon,
    PlayArrow as PlayIcon,
    Send as SendIcon,
    Visibility as ViewIcon,
    Edit as EditIcon,
    AccessTime as TimeIcon,
    Flag as FlagIcon,
    Star as StarIcon,
    Comment as CommentIcon
} from '@mui/icons-material';

function TurboWarpSubmission({ user }) {
    const [assignments, setAssignments] = useState([]);
    const [filteredAssignments, setFilteredAssignments] = useState([]);
    const [selectedAssignment, setSelectedAssignment] = useState(null);
    const [openDialog, setOpenDialog] = useState(false);
    const [openSubmissionDialog, setOpenSubmissionDialog] = useState(false);
    const [filterStatus, setFilterStatus] = useState('');
    const [submissionData, setSubmissionData] = useState({
        projectUrl: '',
        description: '',
        features: '',
        challenges: '',
        improvements: ''
    });
    const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

    useEffect(() => {
        // Giả lập dữ liệu bài tập TurboWarp
        const mockAssignments = [
            {
                id: 1,
                title: 'Tạo game Pong đơn giản',
                course: 'Lập trình Scratch 7C',
                teacher: 'Cô Lê Thị C',
                description: 'Lập trình game Pong cổ điển với 2 người chơi',
                assignmentType: 'project',
                difficulty: 'intermediate',
                estimatedTime: 180,
                dueDate: '2024-01-25 23:59',
                instructions: `
                    Tạo một game Pong với các yêu cầu sau:
                    1. Có 2 paddle điều khiển bằng phím
                    2. Quả bóng di chuyển và va chạm
                    3. Hệ thống tính điểm
                    4. Màn hình bắt đầu và kết thúc
                `,
                starterProject: 'https://turbowarp.org/editor?project_url=starter-pong.sb3',
                requiredFeatures: ['Sprite movement', 'Collision detection', 'Score system', 'User input handling'],
                gradingCriteria: ['Functionality', 'Code organization', 'Creativity', 'User interface'],
                maxSubmissions: 3,
                status: 'assigned',
                submissions: [
                    {
                        id: 1,
                        submittedAt: '2024-01-20 15:30',
                        projectUrl: 'https://turbowarp.org/123456',
                        description: 'Game Pong hoàn chỉnh với âm thanh',
                        score: 85,
                        feedback: 'Tốt! Cần cải thiện giao diện người dùng.',
                        status: 'graded'
                    }
                ]
            },
            {
                id: 2,
                title: 'Mô phỏng chuyển động vật lý',
                course: 'Vật lý thí nghiệm 9B',
                teacher: 'Thầy Trần Văn B',
                description: 'Tạo mô phỏng các định luật vật lý cơ bản',
                assignmentType: 'challenge',
                difficulty: 'advanced',
                estimatedTime: 240,
                dueDate: '2024-01-30 23:59',
                instructions: `
                    Sử dụng Scratch để mô phỏng:
                    1. Chuyển động rơi tự do
                    2. Chuyển động ném xiên
                    3. Va chạm đàn hồi
                    4. Hiển thị đồ thị vận tốc/gia tốc
                `,
                starterProject: '',
                requiredFeatures: ['Variables usage', 'Animation', 'Mathematical calculations'],
                gradingCriteria: ['Scientific accuracy', 'Visual presentation', 'Interactivity'],
                maxSubmissions: 2,
                status: 'assigned',
                submissions: []
            },
            {
                id: 3,
                title: 'Ứng dụng tính toán toán học',
                course: 'Toán học nâng cao 8A',
                teacher: 'Cô Nguyễn Thị A',
                description: 'Tạo ứng dụng giải phương trình bậc hai',
                assignmentType: 'tutorial',
                difficulty: 'beginner',
                estimatedTime: 120,
                dueDate: '2024-01-22 23:59',
                instructions: `
                    Tạo ứng dụng với các chức năng:
                    1. Nhập hệ số a, b, c
                    2. Tính delta và nghiệm
                    3. Hiển thị kết quả rõ ràng
                    4. Xử lý trường hợp đặc biệt
                `,
                starterProject: 'https://turbowarp.org/editor?project_url=math-calculator.sb3',
                requiredFeatures: ['User input handling', 'Variables usage', 'Custom blocks'],
                gradingCriteria: ['Functionality', 'User interface', 'Error handling'],
                maxSubmissions: 1,
                status: 'completed',
                submissions: [
                    {
                        id: 1,
                        submittedAt: '2024-01-21 14:20',
                        projectUrl: 'https://turbowarp.org/789012',
                        description: 'Máy tính giải phương trình bậc hai hoàn chỉnh',
                        score: 95,
                        feedback: 'Xuất sắc! Giao diện đẹp và logic chính xác.',
                        status: 'graded'
                    }
                ]
            }
        ];
        setAssignments(mockAssignments);
        setFilteredAssignments(mockAssignments);
    }, []);

    useEffect(() => {
        let filtered = assignments;

        if (filterStatus) {
            filtered = filtered.filter(assignment => assignment.status === filterStatus);
        }

        setFilteredAssignments(filtered);
    }, [assignments, filterStatus]);

    const handleViewAssignment = (assignment) => {
        setSelectedAssignment(assignment);
        setOpenDialog(true);
    };

    const handleCloseDialog = () => {
        setOpenDialog(false);
        setSelectedAssignment(null);
    };

    const handleOpenSubmission = (assignment) => {
        setSelectedAssignment(assignment);
        setSubmissionData({
            projectUrl: '',
            description: '',
            features: '',
            challenges: '',
            improvements: ''
        });
        setOpenSubmissionDialog(true);
    };

    const handleCloseSubmissionDialog = () => {
        setOpenSubmissionDialog(false);
        setSelectedAssignment(null);
    };

    const handleSubmissionChange = (field, value) => {
        setSubmissionData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const handleSubmitAssignment = () => {
        if (!submissionData.projectUrl || !submissionData.description) {
            setSnackbar({
                open: true,
                message: 'Vui lòng điền đầy đủ thông tin!',
                severity: 'error'
            });
            return;
        }

        const newSubmission = {
            id: Date.now(),
            submittedAt: new Date().toISOString(),
            ...submissionData,
            status: 'submitted'
        };

        setAssignments(prev => prev.map(assignment => 
            assignment.id === selectedAssignment.id 
                ? { 
                    ...assignment, 
                    submissions: [...assignment.submissions, newSubmission],
                    status: assignment.submissions.length + 1 >= assignment.maxSubmissions ? 'completed' : 'assigned'
                }
                : assignment
        ));

        setSnackbar({
            open: true,
            message: 'Nộp bài thành công!',
            severity: 'success'
        });

        handleCloseSubmissionDialog();
    };

    const getStatusChip = (assignment) => {
        switch (assignment.status) {
            case 'assigned':
                return <Chip label="Đã giao" color="warning" size="small" icon={<AssignmentIcon />} />;
            case 'completed':
                return <Chip label="Đã hoàn thành" color="success" size="small" icon={<CheckCircleIcon />} />;
            case 'overdue':
                return <Chip label="Quá hạn" color="error" size="small" />;
            default:
                return <Chip label="Không xác định" color="default" size="small" />;
        }
    };

    const getDifficultyChip = (difficulty) => {
        const difficultyMap = {
            'beginner': { label: 'Cơ bản', color: 'success' },
            'intermediate': { label: 'Trung bình', color: 'warning' },
            'advanced': { label: 'Nâng cao', color: 'error' }
        };
        const difficultyInfo = difficultyMap[difficulty] || difficultyMap.beginner;
        return <Chip label={difficultyInfo.label} color={difficultyInfo.color} size="small" />;
    };

    const getTypeLabel = (type) => {
        const typeMap = {
            'project': 'Dự án',
            'challenge': 'Thử thách',
            'tutorial': 'Hướng dẫn'
        };
        return typeMap[type] || 'Không xác định';
    };

    const canSubmit = (assignment) => {
        return assignment.status === 'assigned' && assignment.submissions.length < assignment.maxSubmissions;
    };

    const getLatestSubmission = (assignment) => {
        return assignment.submissions.length > 0 ? assignment.submissions[assignment.submissions.length - 1] : null;
    };

    return (
        <Box>
            {/* Header */}
            <Box sx={{ mb: 4 }}>
                <Typography variant="h4" gutterBottom>
                    Bài tập TurboWarp
                </Typography>
                <Typography variant="body1" color="textSecondary">
                    Làm và nộp các bài tập lập trình Scratch/TurboWarp
                </Typography>
            </Box>

            {/* Statistics */}
            <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12} sm={6} md={3}>
                    <Card>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Tổng bài tập
                            </Typography>
                            <Typography variant="h4">
                                {assignments.length}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                    <Card>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Đã giao
                            </Typography>
                            <Typography variant="h4" color="warning.main">
                                {assignments.filter(a => a.status === 'assigned').length}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                    <Card>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Đã hoàn thành
                            </Typography>
                            <Typography variant="h4" color="success.main">
                                {assignments.filter(a => a.status === 'completed').length}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                    <Card>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Điểm trung bình
                            </Typography>
                            <Typography variant="h4">
                                {assignments.filter(a => getLatestSubmission(a)?.score).length > 0 
                                    ? Math.round(assignments.filter(a => getLatestSubmission(a)?.score).reduce((sum, a) => sum + getLatestSubmission(a).score, 0) / assignments.filter(a => getLatestSubmission(a)?.score).length)
                                    : 0
                                }
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
            </Grid>

            {/* Filter */}
            <Paper sx={{ p: 2, mb: 3 }}>
                <Grid container spacing={2} alignItems="center">
                    <Grid item xs={12} md={3}>
                        <FormControl fullWidth>
                            <InputLabel>Trạng thái</InputLabel>
                            <Select
                                value={filterStatus}
                                onChange={(e) => setFilterStatus(e.target.value)}
                                label="Trạng thái"
                            >
                                <MenuItem value="">Tất cả</MenuItem>
                                <MenuItem value="assigned">Đã giao</MenuItem>
                                <MenuItem value="completed">Đã hoàn thành</MenuItem>
                                <MenuItem value="overdue">Quá hạn</MenuItem>
                            </Select>
                        </FormControl>
                    </Grid>
                </Grid>
            </Paper>

            {/* Assignments Grid */}
            <Grid container spacing={3}>
                {filteredAssignments.map((assignment) => {
                    const latestSubmission = getLatestSubmission(assignment);
                    return (
                        <Grid item xs={12} md={6} lg={4} key={assignment.id}>
                            <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                                <CardContent sx={{ flexGrow: 1 }}>
                                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                                        <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
                                            {assignment.title}
                                        </Typography>
                                        <TurboWarpIcon />
                                    </Box>
                                    
                                    <Box sx={{ mb: 2 }}>
                                        <Typography variant="body2" color="textSecondary" sx={{ mb: 1 }}>
                                            <PersonIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                            {assignment.teacher} • {assignment.course}
                                        </Typography>
                                        <Typography variant="body2" color="textSecondary">
                                            {assignment.description}
                                        </Typography>
                                    </Box>

                                    <Box sx={{ mb: 2 }}>
                                        {getStatusChip(assignment)}
                                        <Chip 
                                            label={getTypeLabel(assignment.assignmentType)} 
                                            variant="outlined" 
                                            size="small" 
                                            sx={{ ml: 1 }}
                                        />
                                    </Box>

                                    <Box sx={{ mb: 2 }}>
                                        {getDifficultyChip(assignment.difficulty)}
                                    </Box>

                                    <Box sx={{ mb: 2 }}>
                                        <Typography variant="body2" sx={{ mb: 1 }}>
                                            <TimeIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                            Thời gian ước tính: {assignment.estimatedTime} phút
                                        </Typography>
                                        <Typography variant="body2" sx={{ mb: 1 }}>
                                            <ScheduleIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                            Hạn nộp: {assignment.dueDate}
                                        </Typography>
                                        <Typography variant="body2">
                                            <FlagIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                            Lần nộp: {assignment.submissions.length}/{assignment.maxSubmissions}
                                        </Typography>
                                    </Box>

                                    {latestSubmission && (
                                        <Box sx={{ mb: 2 }}>
                                            <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                                                <Typography variant="body2" color="textSecondary" gutterBottom>
                                                    Lần nộp gần nhất:
                                                </Typography>
                                                {latestSubmission.score && (
                                                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                                        <StarIcon sx={{ fontSize: 16, mr: 0.5, color: 'warning.main' }} />
                                                        <Typography variant="body2" fontWeight="bold">
                                                            {latestSubmission.score} điểm
                                                        </Typography>
                                                    </Box>
                                                )}
                                                <Typography variant="caption" color="textSecondary">
                                                    {new Date(latestSubmission.submittedAt).toLocaleString('vi-VN')}
                                                </Typography>
                                            </Paper>
                                        </Box>
                                    )}

                                    {assignment.requiredFeatures.length > 0 && (
                                        <Box>
                                            <Typography variant="body2" color="textSecondary" gutterBottom>
                                                Yêu cầu: {assignment.requiredFeatures.slice(0, 2).join(', ')}
                                                {assignment.requiredFeatures.length > 2 && '...'}
                                            </Typography>
                                        </Box>
                                    )}
                                </CardContent>
                                
                                <CardActions>
                                    <Button
                                        size="small"
                                        startIcon={<ViewIcon />}
                                        onClick={() => handleViewAssignment(assignment)}
                                    >
                                        Chi tiết
                                    </Button>
                                    {assignment.starterProject && (
                                        <Button
                                            size="small"
                                            startIcon={<LaunchIcon />}
                                            component={Link}
                                            href={assignment.starterProject}
                                            target="_blank"
                                        >
                                            TurboWarp
                                        </Button>
                                    )}
                                    {canSubmit(assignment) && (
                                        <Button
                                            size="small"
                                            variant="contained"
                                            startIcon={<SendIcon />}
                                            onClick={() => handleOpenSubmission(assignment)}
                                            sx={{ bgcolor: '#2e7d32' }}
                                        >
                                            Nộp bài
                                        </Button>
                                    )}
                                </CardActions>
                            </Card>
                        </Grid>
                    );
                })}
            </Grid>

            {/* Assignment Detail Dialog */}
            <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
                <DialogTitle>
                    {selectedAssignment?.title}
                </DialogTitle>
                <DialogContent>
                    {selectedAssignment && (
                        <Box>
                            <Typography variant="h6" gutterBottom>
                                Hướng dẫn
                            </Typography>
                            <Typography variant="body2" sx={{ mb: 3, whiteSpace: 'pre-line' }}>
                                {selectedAssignment.instructions}
                            </Typography>

                            <Typography variant="h6" gutterBottom>
                                Yêu cầu tính năng
                            </Typography>
                            <List dense>
                                {selectedAssignment.requiredFeatures.map((feature, index) => (
                                    <ListItem key={index}>
                                        <ListItemIcon>
                                            <CheckCircleIcon color="success" />
                                        </ListItemIcon>
                                        <ListItemText primary={feature} />
                                    </ListItem>
                                ))}
                            </List>

                            <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                                Tiêu chí chấm điểm
                            </Typography>
                            <List dense>
                                {selectedAssignment.gradingCriteria.map((criteria, index) => (
                                    <ListItem key={index}>
                                        <ListItemIcon>
                                            <StarIcon color="warning" />
                                        </ListItemIcon>
                                        <ListItemText primary={criteria} />
                                    </ListItem>
                                ))}
                            </List>

                            {selectedAssignment.submissions.length > 0 && (
                                <Box sx={{ mt: 3 }}>
                                    <Typography variant="h6" gutterBottom>
                                        Lịch sử nộp bài
                                    </Typography>
                                    {selectedAssignment.submissions.map((submission, index) => (
                                        <Paper key={submission.id} sx={{ p: 2, mb: 2 }}>
                                            <Typography variant="body2" gutterBottom>
                                                <strong>Lần {index + 1}:</strong> {new Date(submission.submittedAt).toLocaleString('vi-VN')}
                                            </Typography>
                                            <Typography variant="body2" sx={{ mb: 1 }}>
                                                {submission.description}
                                            </Typography>
                                            {submission.score && (
                                                <Typography variant="body2" color="success.main" sx={{ mb: 1 }}>
                                                    <strong>Điểm:</strong> {submission.score}
                                                </Typography>
                                            )}
                                            {submission.feedback && (
                                                <Typography variant="body2" color="textSecondary">
                                                    <strong>Nhận xét:</strong> {submission.feedback}
                                                </Typography>
                                            )}
                                        </Paper>
                                    ))}
                                </Box>
                            )}
                        </Box>
                    )}
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseDialog}>Đóng</Button>
                    {selectedAssignment?.starterProject && (
                        <Button 
                            variant="outlined"
                            startIcon={<LaunchIcon />}
                            component={Link}
                            href={selectedAssignment.starterProject}
                            target="_blank"
                        >
                            Mở TurboWarp
                        </Button>
                    )}
                    {selectedAssignment && canSubmit(selectedAssignment) && (
                        <Button 
                            variant="contained" 
                            startIcon={<SendIcon />}
                            onClick={() => {
                                handleCloseDialog();
                                handleOpenSubmission(selectedAssignment);
                            }}
                            sx={{ bgcolor: '#2e7d32' }}
                        >
                            Nộp bài
                        </Button>
                    )}
                </DialogActions>
            </Dialog>

            {/* Submission Dialog */}
            <Dialog open={openSubmissionDialog} onClose={handleCloseSubmissionDialog} maxWidth="md" fullWidth>
                <DialogTitle>
                    Nộp bài: {selectedAssignment?.title}
                </DialogTitle>
                <DialogContent>
                    <Grid container spacing={2} sx={{ mt: 1 }}>
                        <Grid item xs={12}>
                            <TextField
                                fullWidth
                                label="Link dự án TurboWarp *"
                                value={submissionData.projectUrl}
                                onChange={(e) => handleSubmissionChange('projectUrl', e.target.value)}
                                placeholder="https://turbowarp.org/123456"
                                helperText="Paste link dự án TurboWarp của bạn"
                            />
                        </Grid>
                        
                        <Grid item xs={12}>
                            <TextField
                                fullWidth
                                label="Mô tả dự án *"
                                multiline
                                rows={3}
                                value={submissionData.description}
                                onChange={(e) => handleSubmissionChange('description', e.target.value)}
                                placeholder="Mô tả ngắn gọn về dự án của bạn..."
                            />
                        </Grid>
                        
                        <Grid item xs={12}>
                            <TextField
                                fullWidth
                                label="Tính năng đã thực hiện"
                                multiline
                                rows={2}
                                value={submissionData.features}
                                onChange={(e) => handleSubmissionChange('features', e.target.value)}
                                placeholder="Liệt kê các tính năng bạn đã hoàn thành..."
                            />
                        </Grid>
                        
                        <Grid item xs={12}>
                            <TextField
                                fullWidth
                                label="Khó khăn gặp phải"
                                multiline
                                rows={2}
                                value={submissionData.challenges}
                                onChange={(e) => handleSubmissionChange('challenges', e.target.value)}
                                placeholder="Những khó khăn bạn gặp phải khi làm bài..."
                            />
                        </Grid>
                        
                        <Grid item xs={12}>
                            <TextField
                                fullWidth
                                label="Ý tưởng cải tiến"
                                multiline
                                rows={2}
                                value={submissionData.improvements}
                                onChange={(e) => handleSubmissionChange('improvements', e.target.value)}
                                placeholder="Những cải tiến bạn muốn thêm vào dự án..."
                            />
                        </Grid>
                    </Grid>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseSubmissionDialog}>Hủy</Button>
                    <Button 
                        variant="contained" 
                        startIcon={<SendIcon />}
                        onClick={handleSubmitAssignment}
                        sx={{ bgcolor: '#2e7d32' }}
                    >
                        Nộp bài
                    </Button>
                </DialogActions>
            </Dialog>

            {/* Snackbar */}
            <Snackbar
                open={snackbar.open}
                autoHideDuration={6000}
                onClose={() => setSnackbar({ ...snackbar, open: false })}
            >
                <Alert
                    onClose={() => setSnackbar({ ...snackbar, open: false })}
                    severity={snackbar.severity}
                    sx={{ width: '100%' }}
                >
                    {snackbar.message}
                </Alert>
            </Snackbar>
        </Box>
    );
}

export default TurboWarpSubmission;
