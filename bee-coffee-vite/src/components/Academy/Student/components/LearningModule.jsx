import React, { useState, useEffect } from 'react';
import {
    Box,
    Typo<PERSON>,
    Grid,
    Card,
    CardContent,
    CardActions,
    Button,
    LinearProgress,
    Chip,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Paper,
    List,
    ListItem,
    ListItemText,
    ListItemIcon,
    Divider,
    IconButton,
    Alert,
    Snackbar,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    TextField
} from '@mui/material';
import {
    PlayArrow as PlayIcon,
    Pause as PauseIcon,
    CheckCircle as CheckCircleIcon,
    Schedule as ScheduleIcon,
    VideoLibrary as VideoIcon,
    AttachFile as FileIcon,
    Create as TextIcon,
    BookOnline as LessonIcon,
    AccessTime as TimeIcon,
    Person as PersonIcon,
    Download as DownloadIcon,
    Fullscreen as FullscreenIcon,
    VolumeUp as VolumeIcon,
    Search as SearchIcon,
    FilterList as FilterIcon
} from '@mui/icons-material';

function LearningModule({ user }) {
    const [lessons, setLessons] = useState([]);
    const [filteredLessons, setFilteredLessons] = useState([]);
    const [selectedLesson, setSelectedLesson] = useState(null);
    const [openDialog, setOpenDialog] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [filterCourse, setFilterCourse] = useState('');
    const [filterStatus, setFilterStatus] = useState('');
    const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

    const courses = [
        'Toán học nâng cao 8A',
        'Vật lý thí nghiệm 9B',
        'Lập trình Scratch 7C'
    ];

    useEffect(() => {
        // Giả lập dữ liệu bài học
        const mockLessons = [
            {
                id: 1,
                title: 'Phương trình bậc hai',
                course: 'Toán học nâng cao 8A',
                teacher: 'Cô Nguyễn Thị A',
                description: 'Học cách giải phương trình bậc hai và ứng dụng trong thực tế',
                contentType: 'text',
                content: `
                    <h3>Phương trình bậc hai</h3>
                    <p>Phương trình bậc hai có dạng tổng quát: ax² + bx + c = 0 (a ≠ 0)</p>
                    <h4>Công thức nghiệm:</h4>
                    <p>Δ = b² - 4ac</p>
                    <ul>
                        <li>Nếu Δ > 0: phương trình có 2 nghiệm phân biệt</li>
                        <li>Nếu Δ = 0: phương trình có nghiệm kép</li>
                        <li>Nếu Δ < 0: phương trình vô nghiệm</li>
                    </ul>
                    <h4>Ví dụ:</h4>
                    <p>Giải phương trình: x² - 5x + 6 = 0</p>
                    <p>Ta có: a = 1, b = -5, c = 6</p>
                    <p>Δ = (-5)² - 4.1.6 = 25 - 24 = 1 > 0</p>
                    <p>Vậy phương trình có 2 nghiệm: x₁ = 2, x₂ = 3</p>
                `,
                duration: 45,
                scheduledTime: '2024-01-16 14:00',
                status: 'available',
                isCompleted: false,
                progress: 0,
                materials: [
                    { name: 'Bài tập thực hành.pdf', type: 'pdf', url: '/files/bai-tap-phuong-trinh.pdf' },
                    { name: 'Công thức tóm tắt.docx', type: 'doc', url: '/files/cong-thuc-tom-tat.docx' }
                ]
            },
            {
                id: 2,
                title: 'Định luật Newton thứ hai',
                course: 'Vật lý thí nghiệm 9B',
                teacher: 'Thầy Trần Văn B',
                description: 'Tìm hiểu về định luật Newton thứ hai và ứng dụng',
                contentType: 'video',
                videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
                duration: 50,
                scheduledTime: '2024-01-17 15:00',
                status: 'available',
                isCompleted: true,
                progress: 100,
                completedAt: '2024-01-15 16:30',
                materials: [
                    { name: 'Thí nghiệm minh họa.mp4', type: 'video', url: '/videos/thi-nghiem-newton.mp4' },
                    { name: 'Bài tập vận dụng.pdf', type: 'pdf', url: '/files/bai-tap-newton.pdf' }
                ]
            },
            {
                id: 3,
                title: 'Lập trình game Pong',
                course: 'Lập trình Scratch 7C',
                teacher: 'Cô Lê Thị C',
                description: 'Hướng dẫn tạo game Pong cổ điển với Scratch',
                contentType: 'file',
                fileName: 'Huong-dan-lap-trinh-Pong.pdf',
                fileUrl: '/files/huong-dan-pong.pdf',
                duration: 60,
                scheduledTime: '2024-01-18 16:00',
                status: 'scheduled',
                isCompleted: false,
                progress: 30,
                materials: [
                    { name: 'File Scratch mẫu.sb3', type: 'scratch', url: '/scratch/pong-template.sb3' },
                    { name: 'Hình ảnh sprites.zip', type: 'zip', url: '/files/pong-sprites.zip' }
                ]
            },
            {
                id: 4,
                title: 'Hệ phương trình tuyến tính',
                course: 'Toán học nâng cao 8A',
                teacher: 'Cô Nguyễn Thị A',
                description: 'Các phương pháp giải hệ phương trình tuyến tính',
                contentType: 'text',
                content: `
                    <h3>Hệ phương trình tuyến tính</h3>
                    <p>Hệ phương trình tuyến tính gồm hai phương trình bậc nhất hai ẩn</p>
                    <h4>Các phương pháp giải:</h4>
                    <ol>
                        <li>Phương pháp thế</li>
                        <li>Phương pháp cộng đại số</li>
                        <li>Phương pháp đồ thị</li>
                    </ol>
                `,
                duration: 40,
                scheduledTime: '2024-01-19 14:00',
                status: 'locked',
                isCompleted: false,
                progress: 0,
                prerequisite: 'Hoàn thành bài: Phương trình bậc hai',
                materials: []
            }
        ];
        setLessons(mockLessons);
        setFilteredLessons(mockLessons);
    }, []);

    useEffect(() => {
        let filtered = lessons;

        if (searchTerm) {
            filtered = filtered.filter(lesson => 
                lesson.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                lesson.description.toLowerCase().includes(searchTerm.toLowerCase())
            );
        }

        if (filterCourse) {
            filtered = filtered.filter(lesson => lesson.course === filterCourse);
        }

        if (filterStatus) {
            filtered = filtered.filter(lesson => lesson.status === filterStatus);
        }

        setFilteredLessons(filtered);
    }, [lessons, searchTerm, filterCourse, filterStatus]);

    const handleStartLesson = (lesson) => {
        if (lesson.status === 'locked') {
            setSnackbar({
                open: true,
                message: 'Bài học này chưa được mở khóa!',
                severity: 'warning'
            });
            return;
        }

        setSelectedLesson(lesson);
        setOpenDialog(true);

        // Cập nhật trạng thái bài học
        if (!lesson.isCompleted && lesson.progress === 0) {
            setLessons(prev => prev.map(l => 
                l.id === lesson.id 
                    ? { ...l, progress: 10, status: 'in-progress' }
                    : l
            ));
        }
    };

    const handleCompleteLesson = (lessonId) => {
        setLessons(prev => prev.map(lesson => 
            lesson.id === lessonId 
                ? { 
                    ...lesson, 
                    isCompleted: true, 
                    progress: 100,
                    status: 'completed',
                    completedAt: new Date().toISOString()
                }
                : lesson
        ));
        setSnackbar({
            open: true,
            message: 'Hoàn thành bài học!',
            severity: 'success'
        });
        handleCloseDialog();
    };

    const handleCloseDialog = () => {
        setOpenDialog(false);
        setSelectedLesson(null);
    };

    const getStatusChip = (lesson) => {
        if (lesson.isCompleted) {
            return <Chip label="Đã hoàn thành" color="success" size="small" icon={<CheckCircleIcon />} />;
        }
        
        switch (lesson.status) {
            case 'available':
                return <Chip label="Có thể học" color="primary" size="small" />;
            case 'scheduled':
                return <Chip label="Đã lên lịch" color="info" size="small" icon={<ScheduleIcon />} />;
            case 'in-progress':
                return <Chip label="Đang học" color="warning" size="small" />;
            case 'locked':
                return <Chip label="Chưa mở khóa" color="error" size="small" />;
            default:
                return <Chip label="Không xác định" color="default" size="small" />;
        }
    };

    const getContentIcon = (contentType) => {
        switch (contentType) {
            case 'video': return <VideoIcon />;
            case 'file': return <FileIcon />;
            case 'text': return <TextIcon />;
            default: return <LessonIcon />;
        }
    };

    const renderLessonContent = (lesson) => {
        switch (lesson.contentType) {
            case 'text':
                return (
                    <Box sx={{ p: 2 }}>
                        <div dangerouslySetInnerHTML={{ __html: lesson.content }} />
                    </Box>
                );
            case 'video':
                return (
                    <Box sx={{ p: 2 }}>
                        <Box sx={{ position: 'relative', paddingBottom: '56.25%', height: 0 }}>
                            <iframe
                                src={lesson.videoUrl}
                                style={{
                                    position: 'absolute',
                                    top: 0,
                                    left: 0,
                                    width: '100%',
                                    height: '100%',
                                    border: 'none'
                                }}
                                allowFullScreen
                                title={lesson.title}
                            />
                        </Box>
                    </Box>
                );
            case 'file':
                return (
                    <Box sx={{ p: 2, textAlign: 'center' }}>
                        <FileIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                        <Typography variant="h6" gutterBottom>
                            {lesson.fileName}
                        </Typography>
                        <Button
                            variant="contained"
                            startIcon={<DownloadIcon />}
                            href={lesson.fileUrl}
                            target="_blank"
                        >
                            Tải xuống tài liệu
                        </Button>
                    </Box>
                );
            default:
                return (
                    <Box sx={{ p: 2 }}>
                        <Typography>Nội dung bài học không khả dụng</Typography>
                    </Box>
                );
        }
    };

    return (
        <Box>
            {/* Header */}
            <Box sx={{ mb: 4 }}>
                <Typography variant="h4" gutterBottom>
                    Học bài
                </Typography>
                <Typography variant="body1" color="textSecondary">
                    Học theo thời gian chỉ định và hoàn thành các bài học được giao
                </Typography>
            </Box>

            {/* Statistics */}
            <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12} sm={6} md={3}>
                    <Card>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Tổng bài học
                            </Typography>
                            <Typography variant="h4">
                                {lessons.length}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                    <Card>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Đã hoàn thành
                            </Typography>
                            <Typography variant="h4" color="success.main">
                                {lessons.filter(l => l.isCompleted).length}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                    <Card>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Đang học
                            </Typography>
                            <Typography variant="h4" color="warning.main">
                                {lessons.filter(l => l.status === 'in-progress').length}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                    <Card>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Tiến độ trung bình
                            </Typography>
                            <Typography variant="h4">
                                {lessons.length > 0 
                                    ? Math.round(lessons.reduce((sum, l) => sum + l.progress, 0) / lessons.length)
                                    : 0
                                }%
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
            </Grid>

            {/* Filters */}
            <Paper sx={{ p: 2, mb: 3 }}>
                <Grid container spacing={2} alignItems="center">
                    <Grid item xs={12} md={4}>
                        <TextField
                            fullWidth
                            placeholder="Tìm kiếm bài học..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            InputProps={{
                                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
                            }}
                        />
                    </Grid>
                    <Grid item xs={12} md={3}>
                        <FormControl fullWidth>
                            <InputLabel>Khóa học</InputLabel>
                            <Select
                                value={filterCourse}
                                onChange={(e) => setFilterCourse(e.target.value)}
                                label="Khóa học"
                            >
                                <MenuItem value="">Tất cả</MenuItem>
                                {courses.map(course => (
                                    <MenuItem key={course} value={course}>{course}</MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                    </Grid>
                    <Grid item xs={12} md={3}>
                        <FormControl fullWidth>
                            <InputLabel>Trạng thái</InputLabel>
                            <Select
                                value={filterStatus}
                                onChange={(e) => setFilterStatus(e.target.value)}
                                label="Trạng thái"
                            >
                                <MenuItem value="">Tất cả</MenuItem>
                                <MenuItem value="available">Có thể học</MenuItem>
                                <MenuItem value="scheduled">Đã lên lịch</MenuItem>
                                <MenuItem value="in-progress">Đang học</MenuItem>
                                <MenuItem value="completed">Đã hoàn thành</MenuItem>
                                <MenuItem value="locked">Chưa mở khóa</MenuItem>
                            </Select>
                        </FormControl>
                    </Grid>
                    <Grid item xs={12} md={2}>
                        <Button
                            fullWidth
                            variant="outlined"
                            startIcon={<FilterIcon />}
                            onClick={() => {
                                setSearchTerm('');
                                setFilterCourse('');
                                setFilterStatus('');
                            }}
                        >
                            Xóa bộ lọc
                        </Button>
                    </Grid>
                </Grid>
            </Paper>

            {/* Lessons Grid */}
            <Grid container spacing={3}>
                {filteredLessons.map((lesson) => (
                    <Grid item xs={12} md={6} lg={4} key={lesson.id}>
                        <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                            <CardContent sx={{ flexGrow: 1 }}>
                                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                                    <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
                                        {lesson.title}
                                    </Typography>
                                    {getContentIcon(lesson.contentType)}
                                </Box>
                                
                                <Box sx={{ mb: 2 }}>
                                    <Typography variant="body2" color="textSecondary" sx={{ mb: 1 }}>
                                        <PersonIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                        {lesson.teacher} • {lesson.course}
                                    </Typography>
                                    <Typography variant="body2" color="textSecondary">
                                        {lesson.description}
                                    </Typography>
                                </Box>

                                <Box sx={{ mb: 2 }}>
                                    {getStatusChip(lesson)}
                                </Box>

                                <Box sx={{ mb: 2 }}>
                                    <Typography variant="body2" sx={{ mb: 1 }}>
                                        <TimeIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                        Thời lượng: {lesson.duration} phút
                                    </Typography>
                                    <Typography variant="body2" sx={{ mb: 1 }}>
                                        <ScheduleIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                        Lịch học: {lesson.scheduledTime}
                                    </Typography>
                                </Box>

                                {lesson.progress > 0 && (
                                    <Box sx={{ mb: 2 }}>
                                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                                            <Typography variant="body2">Tiến độ</Typography>
                                            <Typography variant="body2">{lesson.progress}%</Typography>
                                        </Box>
                                        <LinearProgress 
                                            variant="determinate" 
                                            value={lesson.progress}
                                            sx={{ height: 8, borderRadius: 4 }}
                                        />
                                    </Box>
                                )}

                                {lesson.prerequisite && lesson.status === 'locked' && (
                                    <Alert severity="info" sx={{ mb: 2 }}>
                                        Yêu cầu: {lesson.prerequisite}
                                    </Alert>
                                )}

                                {lesson.materials.length > 0 && (
                                    <Box>
                                        <Typography variant="body2" color="textSecondary" gutterBottom>
                                            Tài liệu đính kèm: {lesson.materials.length} file
                                        </Typography>
                                    </Box>
                                )}
                            </CardContent>
                            
                            <CardActions>
                                <Button
                                    size="small"
                                    variant="contained"
                                    startIcon={lesson.isCompleted ? <CheckCircleIcon /> : <PlayIcon />}
                                    onClick={() => handleStartLesson(lesson)}
                                    disabled={lesson.status === 'locked'}
                                    sx={{ bgcolor: lesson.isCompleted ? 'success.main' : '#2e7d32' }}
                                >
                                    {lesson.isCompleted ? 'Xem lại' : 'Bắt đầu học'}
                                </Button>
                            </CardActions>
                        </Card>
                    </Grid>
                ))}
            </Grid>

            {/* Lesson Content Dialog */}
            <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="lg" fullWidth>
                <DialogTitle>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                        <Typography variant="h6">
                            {selectedLesson?.title}
                        </Typography>
                        <IconButton onClick={handleCloseDialog}>
                            <FullscreenIcon />
                        </IconButton>
                    </Box>
                </DialogTitle>
                <DialogContent sx={{ p: 0 }}>
                    {selectedLesson && (
                        <Box>
                            {renderLessonContent(selectedLesson)}
                            
                            {selectedLesson.materials.length > 0 && (
                                <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
                                    <Typography variant="h6" gutterBottom>
                                        Tài liệu đính kèm
                                    </Typography>
                                    <List>
                                        {selectedLesson.materials.map((material, index) => (
                                            <ListItem key={index}>
                                                <ListItemIcon>
                                                    <FileIcon />
                                                </ListItemIcon>
                                                <ListItemText primary={material.name} />
                                                <Button
                                                    size="small"
                                                    startIcon={<DownloadIcon />}
                                                    href={material.url}
                                                    target="_blank"
                                                >
                                                    Tải xuống
                                                </Button>
                                            </ListItem>
                                        ))}
                                    </List>
                                </Box>
                            )}
                        </Box>
                    )}
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseDialog}>Đóng</Button>
                    {selectedLesson && !selectedLesson.isCompleted && (
                        <Button 
                            variant="contained" 
                            startIcon={<CheckCircleIcon />}
                            onClick={() => handleCompleteLesson(selectedLesson.id)}
                            sx={{ bgcolor: '#2e7d32' }}
                        >
                            Hoàn thành bài học
                        </Button>
                    )}
                </DialogActions>
            </Dialog>

            {/* Snackbar */}
            <Snackbar
                open={snackbar.open}
                autoHideDuration={6000}
                onClose={() => setSnackbar({ ...snackbar, open: false })}
            >
                <Alert
                    onClose={() => setSnackbar({ ...snackbar, open: false })}
                    severity={snackbar.severity}
                    sx={{ width: '100%' }}
                >
                    {snackbar.message}
                </Alert>
            </Snackbar>
        </Box>
    );
}

export default LearningModule;
