import React, { useState, useEffect } from 'react';
import {
    Box,
    Typography,
    Grid,
    Card,
    CardContent,
    CardActions,
    Button,
    LinearProgress,
    Chip,
    Avatar,
    IconButton,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    List,
    ListItem,
    ListItemText,
    ListItemIcon,
    Divider,
    Paper,
    Tab,
    Tabs
} from '@mui/material';
import {
    PlayArrow as PlayIcon,
    Assignment as AssignmentIcon,
    Quiz as QuizIcon,
    Games as TurboWarpIcon,
    Schedule as ScheduleIcon,
    CheckCircle as CheckCircleIcon,
    Person as PersonIcon,
    Visibility as ViewIcon,
    BookOnline as LessonIcon,
    Star as StarIcon,
    AccessTime as TimeIcon
} from '@mui/icons-material';

function TabPanel({ children, value, index, ...other }) {
    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`courses-tabpanel-${index}`}
            aria-labelledby={`courses-tab-${index}`}
            {...other}
        >
            {value === index && (
                <Box sx={{ p: 3 }}>
                    {children}
                </Box>
            )}
        </div>
    );
}

function MyCourses({ user }) {
    const [courses, setCourses] = useState([]);
    const [selectedCourse, setSelectedCourse] = useState(null);
    const [openDialog, setOpenDialog] = useState(false);
    const [tabValue, setTabValue] = useState(0);

    useEffect(() => {
        // Giả lập dữ liệu khóa học
        const mockCourses = [
            {
                id: 1,
                name: 'Toán học nâng cao 8A',
                teacher: 'Cô Nguyễn Thị A',
                subject: 'Toán học',
                grade: 'Lớp 8',
                description: 'Khóa học toán nâng cao dành cho học sinh khá giỏi',
                progress: 75,
                totalLessons: 20,
                completedLessons: 15,
                totalAssignments: 8,
                completedAssignments: 6,
                totalQuizzes: 5,
                completedQuizzes: 4,
                avgScore: 8.5,
                enrollDate: '2024-01-05',
                nextLesson: {
                    title: 'Phương trình bậc hai',
                    scheduledTime: '2024-01-16 14:00',
                    duration: 45
                },
                recentActivities: [
                    { type: 'lesson', title: 'Hoàn thành bài 14: Hệ phương trình', time: '2 ngày trước' },
                    { type: 'quiz', title: 'Bài kiểm tra chương 3', score: 9.0, time: '3 ngày trước' },
                    { type: 'assignment', title: 'Bài tập về nhà số 12', score: 8.5, time: '5 ngày trước' }
                ],
                status: 'active'
            },
            {
                id: 2,
                name: 'Vật lý thí nghiệm 9B',
                teacher: 'Thầy Trần Văn B',
                subject: 'Vật lý',
                grade: 'Lớp 9',
                description: 'Khóa học vật lý với nhiều thí nghiệm thực hành',
                progress: 60,
                totalLessons: 18,
                completedLessons: 11,
                totalAssignments: 6,
                completedAssignments: 4,
                totalQuizzes: 3,
                completedQuizzes: 2,
                avgScore: 7.8,
                enrollDate: '2024-01-07',
                nextLesson: {
                    title: 'Định luật Newton thứ hai',
                    scheduledTime: '2024-01-17 15:00',
                    duration: 50
                },
                recentActivities: [
                    { type: 'lesson', title: 'Thí nghiệm về lực ma sát', time: '1 ngày trước' },
                    { type: 'assignment', title: 'Báo cáo thí nghiệm', score: 8.0, time: '4 ngày trước' }
                ],
                status: 'active'
            },
            {
                id: 3,
                name: 'Lập trình Scratch 7C',
                teacher: 'Cô Lê Thị C',
                subject: 'Tin học',
                grade: 'Lớp 7',
                description: 'Học lập trình cơ bản với Scratch',
                progress: 90,
                totalLessons: 16,
                completedLessons: 14,
                totalAssignments: 10,
                completedAssignments: 9,
                totalQuizzes: 2,
                completedQuizzes: 2,
                avgScore: 9.2,
                enrollDate: '2024-01-08',
                nextLesson: {
                    title: 'Dự án cuối khóa - Game nâng cao',
                    scheduledTime: '2024-01-18 16:00',
                    duration: 60
                },
                recentActivities: [
                    { type: 'turbowarp', title: 'Game Pong hoàn chỉnh', score: 9.5, time: '1 ngày trước' },
                    { type: 'lesson', title: 'Lập trình AI đơn giản', time: '2 ngày trước' }
                ],
                status: 'active'
            }
        ];
        setCourses(mockCourses);
    }, []);

    const handleViewCourse = (course) => {
        setSelectedCourse(course);
        setOpenDialog(true);
    };

    const handleCloseDialog = () => {
        setOpenDialog(false);
        setSelectedCourse(null);
        setTabValue(0);
    };

    const getSubjectColor = (subject) => {
        const colors = {
            'Toán học': '#1976d2',
            'Vật lý': '#388e3c',
            'Hóa học': '#f57c00',
            'Sinh học': '#7b1fa2',
            'Tin học': '#d32f2f',
            'STEM': '#795548'
        };
        return colors[subject] || '#757575';
    };

    const getProgressColor = (progress) => {
        if (progress >= 80) return 'success';
        if (progress >= 60) return 'warning';
        return 'error';
    };

    const getActivityIcon = (type) => {
        switch (type) {
            case 'lesson': return <LessonIcon />;
            case 'quiz': return <QuizIcon />;
            case 'assignment': return <AssignmentIcon />;
            case 'turbowarp': return <TurboWarpIcon />;
            default: return <CheckCircleIcon />;
        }
    };

    return (
        <Box>
            {/* Header */}
            <Box sx={{ mb: 4 }}>
                <Typography variant="h4" gutterBottom>
                    Khóa học của tôi
                </Typography>
                <Typography variant="body1" color="textSecondary">
                    Theo dõi tiến độ học tập và hoàn thành các bài tập được giao
                </Typography>
            </Box>

            {/* Statistics */}
            <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12} sm={6} md={3}>
                    <Card>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Tổng khóa học
                            </Typography>
                            <Typography variant="h4">
                                {courses.length}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                    <Card>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Tiến độ trung bình
                            </Typography>
                            <Typography variant="h4">
                                {courses.length > 0 
                                    ? Math.round(courses.reduce((sum, c) => sum + c.progress, 0) / courses.length)
                                    : 0
                                }%
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                    <Card>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Điểm trung bình
                            </Typography>
                            <Typography variant="h4">
                                {courses.length > 0 
                                    ? (courses.reduce((sum, c) => sum + c.avgScore, 0) / courses.length).toFixed(1)
                                    : '0.0'
                                }
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                    <Card>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Bài hoàn thành
                            </Typography>
                            <Typography variant="h4">
                                {courses.reduce((sum, c) => sum + c.completedLessons + c.completedAssignments, 0)}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
            </Grid>

            {/* Courses Grid */}
            <Grid container spacing={3}>
                {courses.map((course) => (
                    <Grid item xs={12} md={6} lg={4} key={course.id}>
                        <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                            <CardContent sx={{ flexGrow: 1 }}>
                                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                                    <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
                                        {course.name}
                                    </Typography>
                                    <Avatar sx={{ bgcolor: getSubjectColor(course.subject), width: 40, height: 40 }}>
                                        {course.subject.charAt(0)}
                                    </Avatar>
                                </Box>
                                
                                <Box sx={{ mb: 2 }}>
                                    <Typography variant="body2" color="textSecondary" sx={{ mb: 1 }}>
                                        <PersonIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                        {course.teacher}
                                    </Typography>
                                    <Typography variant="body2" color="textSecondary">
                                        {course.description}
                                    </Typography>
                                </Box>

                                <Box sx={{ mb: 2 }}>
                                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                                        <Typography variant="body2">Tiến độ</Typography>
                                        <Typography variant="body2" fontWeight="bold">
                                            {course.progress}%
                                        </Typography>
                                    </Box>
                                    <LinearProgress 
                                        variant="determinate" 
                                        value={course.progress}
                                        color={getProgressColor(course.progress)}
                                        sx={{ height: 8, borderRadius: 4 }}
                                    />
                                </Box>

                                <Box sx={{ mb: 2 }}>
                                    <Typography variant="body2" color="textSecondary" gutterBottom>
                                        Thống kê:
                                    </Typography>
                                    <Grid container spacing={1}>
                                        <Grid item xs={4}>
                                            <Typography variant="caption" display="block" align="center">
                                                Bài học
                                            </Typography>
                                            <Typography variant="body2" align="center" fontWeight="bold">
                                                {course.completedLessons}/{course.totalLessons}
                                            </Typography>
                                        </Grid>
                                        <Grid item xs={4}>
                                            <Typography variant="caption" display="block" align="center">
                                                Bài tập
                                            </Typography>
                                            <Typography variant="body2" align="center" fontWeight="bold">
                                                {course.completedAssignments}/{course.totalAssignments}
                                            </Typography>
                                        </Grid>
                                        <Grid item xs={4}>
                                            <Typography variant="caption" display="block" align="center">
                                                Kiểm tra
                                            </Typography>
                                            <Typography variant="body2" align="center" fontWeight="bold">
                                                {course.completedQuizzes}/{course.totalQuizzes}
                                            </Typography>
                                        </Grid>
                                    </Grid>
                                </Box>

                                <Box sx={{ mb: 2 }}>
                                    <Typography variant="body2" color="textSecondary">
                                        Bài tiếp theo:
                                    </Typography>
                                    <Typography variant="body2" fontWeight="bold">
                                        {course.nextLesson.title}
                                    </Typography>
                                    <Typography variant="caption" color="textSecondary">
                                        <ScheduleIcon sx={{ fontSize: 12, mr: 0.5 }} />
                                        {course.nextLesson.scheduledTime}
                                    </Typography>
                                </Box>

                                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                    <Chip
                                        icon={<StarIcon />}
                                        label={`${course.avgScore.toFixed(1)} điểm`}
                                        color="success"
                                        size="small"
                                    />
                                    <Typography variant="caption" color="textSecondary">
                                        Tham gia: {course.enrollDate}
                                    </Typography>
                                </Box>
                            </CardContent>
                            
                            <CardActions>
                                <Button
                                    size="small"
                                    startIcon={<PlayIcon />}
                                    variant="contained"
                                    sx={{ bgcolor: '#2e7d32' }}
                                >
                                    Tiếp tục học
                                </Button>
                                <Button
                                    size="small"
                                    startIcon={<ViewIcon />}
                                    onClick={() => handleViewCourse(course)}
                                >
                                    Chi tiết
                                </Button>
                            </CardActions>
                        </Card>
                    </Grid>
                ))}
            </Grid>

            {/* Course Detail Dialog */}
            <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
                <DialogTitle>
                    {selectedCourse?.name}
                </DialogTitle>
                <DialogContent>
                    {selectedCourse && (
                        <Box>
                            <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
                                <Tab label="Tổng quan" />
                                <Tab label="Hoạt động gần đây" />
                                <Tab label="Thống kê" />
                            </Tabs>

                            <TabPanel value={tabValue} index={0}>
                                <Grid container spacing={2}>
                                    <Grid item xs={12} sm={6}>
                                        <Typography variant="h6" gutterBottom>
                                            Thông tin khóa học
                                        </Typography>
                                        <Typography variant="body2" sx={{ mb: 1 }}>
                                            <strong>Giáo viên:</strong> {selectedCourse.teacher}
                                        </Typography>
                                        <Typography variant="body2" sx={{ mb: 1 }}>
                                            <strong>Môn học:</strong> {selectedCourse.subject}
                                        </Typography>
                                        <Typography variant="body2" sx={{ mb: 1 }}>
                                            <strong>Lớp:</strong> {selectedCourse.grade}
                                        </Typography>
                                        <Typography variant="body2" sx={{ mb: 2 }}>
                                            <strong>Ngày tham gia:</strong> {selectedCourse.enrollDate}
                                        </Typography>
                                        <Typography variant="body2">
                                            {selectedCourse.description}
                                        </Typography>
                                    </Grid>
                                    <Grid item xs={12} sm={6}>
                                        <Typography variant="h6" gutterBottom>
                                            Bài học tiếp theo
                                        </Typography>
                                        <Paper sx={{ p: 2 }}>
                                            <Typography variant="body1" fontWeight="bold" gutterBottom>
                                                {selectedCourse.nextLesson.title}
                                            </Typography>
                                            <Typography variant="body2" sx={{ mb: 1 }}>
                                                <ScheduleIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                                {selectedCourse.nextLesson.scheduledTime}
                                            </Typography>
                                            <Typography variant="body2">
                                                <TimeIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                                {selectedCourse.nextLesson.duration} phút
                                            </Typography>
                                        </Paper>
                                    </Grid>
                                </Grid>
                            </TabPanel>

                            <TabPanel value={tabValue} index={1}>
                                <Typography variant="h6" gutterBottom>
                                    Hoạt động gần đây
                                </Typography>
                                <List>
                                    {selectedCourse.recentActivities.map((activity, index) => (
                                        <React.Fragment key={index}>
                                            <ListItem>
                                                <ListItemIcon>
                                                    {getActivityIcon(activity.type)}
                                                </ListItemIcon>
                                                <ListItemText
                                                    primary={activity.title}
                                                    secondary={activity.time}
                                                />
                                                {activity.score && (
                                                    <Chip
                                                        label={`${activity.score} điểm`}
                                                        color="success"
                                                        size="small"
                                                    />
                                                )}
                                            </ListItem>
                                            {index < selectedCourse.recentActivities.length - 1 && <Divider />}
                                        </React.Fragment>
                                    ))}
                                </List>
                            </TabPanel>

                            <TabPanel value={tabValue} index={2}>
                                <Typography variant="h6" gutterBottom>
                                    Thống kê chi tiết
                                </Typography>
                                <Grid container spacing={3}>
                                    <Grid item xs={12} sm={6}>
                                        <Paper sx={{ p: 2 }}>
                                            <Typography variant="subtitle1" gutterBottom>
                                                Tiến độ học tập
                                            </Typography>
                                            <Box sx={{ mb: 2 }}>
                                                <Typography variant="body2">
                                                    Bài học: {selectedCourse.completedLessons}/{selectedCourse.totalLessons}
                                                </Typography>
                                                <LinearProgress 
                                                    variant="determinate" 
                                                    value={(selectedCourse.completedLessons / selectedCourse.totalLessons) * 100}
                                                    sx={{ mt: 1 }}
                                                />
                                            </Box>
                                            <Box sx={{ mb: 2 }}>
                                                <Typography variant="body2">
                                                    Bài tập: {selectedCourse.completedAssignments}/{selectedCourse.totalAssignments}
                                                </Typography>
                                                <LinearProgress 
                                                    variant="determinate" 
                                                    value={(selectedCourse.completedAssignments / selectedCourse.totalAssignments) * 100}
                                                    sx={{ mt: 1 }}
                                                />
                                            </Box>
                                            <Box>
                                                <Typography variant="body2">
                                                    Kiểm tra: {selectedCourse.completedQuizzes}/{selectedCourse.totalQuizzes}
                                                </Typography>
                                                <LinearProgress 
                                                    variant="determinate" 
                                                    value={(selectedCourse.completedQuizzes / selectedCourse.totalQuizzes) * 100}
                                                    sx={{ mt: 1 }}
                                                />
                                            </Box>
                                        </Paper>
                                    </Grid>
                                    <Grid item xs={12} sm={6}>
                                        <Paper sx={{ p: 2 }}>
                                            <Typography variant="subtitle1" gutterBottom>
                                                Kết quả học tập
                                            </Typography>
                                            <Typography variant="h4" color="success.main" gutterBottom>
                                                {selectedCourse.avgScore.toFixed(1)}/10
                                            </Typography>
                                            <Typography variant="body2" color="textSecondary">
                                                Điểm trung bình
                                            </Typography>
                                        </Paper>
                                    </Grid>
                                </Grid>
                            </TabPanel>
                        </Box>
                    )}
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseDialog}>Đóng</Button>
                    <Button variant="contained" sx={{ bgcolor: '#2e7d32' }}>
                        Tiếp tục học
                    </Button>
                </DialogActions>
            </Dialog>
        </Box>
    );
}

export default MyCourses;
