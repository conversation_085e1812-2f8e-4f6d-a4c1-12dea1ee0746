import React, { useState, useEffect } from 'react';
import StudentCoursePage from './StudentCoursePage';
import {
    Box,
    Typography,
    Button,
    Paper,
    Grid,
    Card,
    CardContent,
    CardActions,
    Chip,
    Avatar,
    LinearProgress,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Alert,
    Snackbar
} from '@mui/material';
import {
    School as SchoolIcon,
    Schedule as ScheduleIcon,
    People as PeopleIcon,
    Star as StarIcon,
    CheckCircle as CheckCircleIcon,
    PlayArrow as PlayIcon,
    Lock as LockIcon,
    Assignment as AssignmentIcon,
    MenuBook as LessonIcon,
    Quiz as QuizIcon,
    Games as TurboWarpIcon
} from '@mui/icons-material';

function StudentLearning({ user }) {
    const [courses, setCourses] = useState([]);
    const [enrolledCourses, setEnrolledCourses] = useState([]);
    const [selectedCourse, setSelectedCourse] = useState(null);
    const [openEnrollDialog, setOpenEnrollDialog] = useState(false);
    const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
    const [showCoursePage, setShowCoursePage] = useState(false);
    const [activeCourse, setActiveCourse] = useState(null);

    useEffect(() => {
        // Giả lập dữ liệu khóa học có sẵn
        const mockCourses = [
            {
                id: 1,
                title: 'Toán học nâng cao lớp 8',
                description: 'Khóa học toán nâng cao dành cho học sinh khá giỏi lớp 8',
                teacher: 'Cô Nguyễn Thị A',
                subject: 'Toán học',
                grade: 'Lớp 8',
                difficulty: 'intermediate',
                duration: '3 tháng',
                totalLessons: 12,
                totalAssignments: 8,
                estimatedTime: 480, // phút
                avgRating: 4.5,
                enrolledStudents: 25,
                objectives: [
                    'Nắm vững các phương trình bậc hai',
                    'Hiểu về hệ phương trình tuyến tính',
                    'Áp dụng toán học vào thực tế'
                ],
                prerequisites: ['Hoàn thành toán lớp 7', 'Điểm trung bình >= 7.0'],
                isEnrolled: false,
                isAssigned: true // Được assign bởi giáo viên
            },
            {
                id: 2,
                title: 'Lập trình Scratch cơ bản',
                description: 'Học lập trình từ cơ bản với Scratch',
                teacher: 'Thầy Trần Văn B',
                subject: 'Tin học',
                grade: 'Lớp 7',
                difficulty: 'beginner',
                duration: '2 tháng',
                totalLessons: 8,
                totalAssignments: 5,
                estimatedTime: 320,
                avgRating: 4.8,
                enrolledStudents: 30,
                objectives: [
                    'Hiểu cơ bản về lập trình',
                    'Tạo được game đơn giản',
                    'Phát triển tư duy logic'
                ],
                prerequisites: ['Biết sử dụng máy tính cơ bản'],
                isEnrolled: true,
                progress: 65,
                completedLessons: 5,
                nextLesson: 'Tạo sprite đầu tiên'
            },
            {
                id: 3,
                title: 'Vật lý thí nghiệm 9',
                description: 'Khóa học vật lý với nhiều thí nghiệm thực hành',
                teacher: 'Thầy Lê Văn C',
                subject: 'Vật lý',
                grade: 'Lớp 9',
                difficulty: 'intermediate',
                duration: '4 tháng',
                totalLessons: 15,
                totalAssignments: 10,
                estimatedTime: 600,
                avgRating: 4.2,
                enrolledStudents: 18,
                objectives: [
                    'Hiểu các định luật vật lý cơ bản',
                    'Thực hiện thí nghiệm an toàn',
                    'Áp dụng vật lý vào đời sống'
                ],
                prerequisites: ['Hoàn thành vật lý lớp 8', 'Có kiến thức toán cơ bản'],
                isEnrolled: false,
                isAssigned: false
            }
        ];

        setCourses(mockCourses);
        setEnrolledCourses(mockCourses.filter(course => course.isEnrolled));
    }, []);

    const handleEnrollCourse = (course) => {
        setSelectedCourse(course);
        setOpenEnrollDialog(true);
    };

    const confirmEnroll = () => {
        if (selectedCourse) {
            // Cập nhật trạng thái enroll
            setCourses(prev => prev.map(course =>
                course.id === selectedCourse.id
                    ? { ...course, isEnrolled: true, progress: 0, completedLessons: 0 }
                    : course
            ));

            // Thêm vào danh sách enrolled
            setEnrolledCourses(prev => [...prev, {
                ...selectedCourse,
                isEnrolled: true,
                progress: 0,
                completedLessons: 0
            }]);

            setSnackbar({
                open: true,
                message: 'Đăng ký khóa học thành công!',
                severity: 'success'
            });
        }
        setOpenEnrollDialog(false);
        setSelectedCourse(null);
    };

    const handleStartLearning = (course) => {
        setActiveCourse(course);
        setShowCoursePage(true);
    };

    const handleCloseCourse = () => {
        setShowCoursePage(false);
        setActiveCourse(null);
    };

    const getSubjectColor = (subject) => {
        const colors = {
            'Toán học': '#1976d2',
            'Vật lý': '#388e3c',
            'Hóa học': '#f57c00',
            'Sinh học': '#7b1fa2',
            'Tin học': '#d32f2f',
            'STEM': '#795548'
        };
        return colors[subject] || '#757575';
    };

    const getDifficultyChip = (difficulty) => {
        const difficultyMap = {
            'beginner': { label: 'Cơ bản', color: 'success' },
            'intermediate': { label: 'Trung bình', color: 'warning' },
            'advanced': { label: 'Nâng cao', color: 'error' }
        };
        const difficultyInfo = difficultyMap[difficulty] || difficultyMap.beginner;
        return <Chip label={difficultyInfo.label} color={difficultyInfo.color} size="small" />;
    };

    const formatTime = (minutes) => {
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        if (hours > 0) {
            return `${hours}h ${mins}m`;
        }
        return `${mins}m`;
    };

    // Hiển thị StudentCoursePage nếu được chọn
    if (showCoursePage && activeCourse) {
        return (
            <StudentCoursePage
                course={activeCourse}
                onClose={handleCloseCourse}
                onProgress={(progress) => {
                    // Cập nhật progress
                    setEnrolledCourses(prev => prev.map(c =>
                        c.id === activeCourse.id ? { ...c, ...progress } : c
                    ));
                }}
            />
        );
    }

    return (
        <Box>
            {/* Header */}
            <Box sx={{ mb: 4 }}>
                <Typography variant="h4" gutterBottom>
                    Học tập E-Learning
                </Typography>
                <Typography variant="body1" color="textSecondary">
                    Tham gia các khóa học trực tuyến và nâng cao kiến thức
                </Typography>
            </Box>

            {/* Enrolled Courses */}
            {enrolledCourses.length > 0 && (
                <Box sx={{ mb: 4 }}>
                    <Typography variant="h5" gutterBottom>
                        Khóa học đang theo học ({enrolledCourses.length})
                    </Typography>
                    <Grid container spacing={3}>
                        {enrolledCourses.map((course) => (
                            <Grid item xs={12} md={6} lg={4} key={course.id}>
                                <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column', borderRadius: '10px' }}>
                                    <CardContent sx={{ flexGrow: 1 }}>
                                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                                            <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
                                                {course.title}
                                            </Typography>
                                            <Avatar sx={{ bgcolor: getSubjectColor(course.subject), width: 32, height: 32 }}>
                                                {course.subject.charAt(0)}
                                            </Avatar>
                                        </Box>

                                        <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
                                            {course.teacher}
                                        </Typography>

                                        <Box sx={{ mb: 2 }}>
                                            <Typography variant="body2" gutterBottom>
                                                Tiến độ: {course.progress || 0}%
                                            </Typography>
                                            <LinearProgress
                                                variant="determinate"
                                                value={course.progress || 0}
                                                sx={{ height: 8, borderRadius: 4 }}
                                            />
                                        </Box>

                                        <Box sx={{ mb: 2 }}>
                                            <Typography variant="body2" sx={{ mb: 1 }}>
                                                <LessonIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                                {course.completedLessons || 0}/{course.totalLessons} bài học
                                            </Typography>
                                            <Typography variant="body2" sx={{ mb: 1 }}>
                                                <ScheduleIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                                {formatTime(course.estimatedTime)}
                                            </Typography>
                                        </Box>

                                        {course.nextLesson && (
                                            <Alert severity="info" sx={{ borderRadius: '10px' }}>
                                                Tiếp theo: {course.nextLesson}
                                            </Alert>
                                        )}
                                    </CardContent>

                                    <CardActions>
                                        <Button
                                            variant="contained"
                                            startIcon={<PlayIcon />}
                                            fullWidth
                                            onClick={() => handleStartLearning(course)}
                                            sx={{ borderRadius: '10px', bgcolor: '#2e7d32' }}
                                        >
                                            Tiếp tục học
                                        </Button>
                                    </CardActions>
                                </Card>
                            </Grid>
                        ))}
                    </Grid>
                </Box>
            )}

            {/* Available Courses */}
            <Box>
                <Typography variant="h5" gutterBottom>
                    Khóa học có sẵn
                </Typography>
                <Grid container spacing={3}>
                    {courses.filter(course => !course.isEnrolled).map((course) => (
                        <Grid item xs={12} md={6} lg={4} key={course.id}>
                            <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column', borderRadius: '10px' }}>
                                <CardContent sx={{ flexGrow: 1 }}>
                                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                                        <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
                                            {course.title}
                                        </Typography>
                                        <Avatar sx={{ bgcolor: getSubjectColor(course.subject), width: 32, height: 32 }}>
                                            {course.subject.charAt(0)}
                                        </Avatar>
                                    </Box>

                                    <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
                                        {course.teacher}
                                    </Typography>

                                    <Typography variant="body2" sx={{ mb: 2 }}>
                                        {course.description}
                                    </Typography>

                                    <Box sx={{ mb: 2 }}>
                                        {getDifficultyChip(course.difficulty)}
                                        {course.isAssigned && (
                                            <Chip
                                                label="Được giao"
                                                color="primary"
                                                size="small"
                                                sx={{ ml: 1 }}
                                            />
                                        )}
                                    </Box>

                                    <Box sx={{ mb: 2 }}>
                                        <Typography variant="body2" sx={{ mb: 1 }}>
                                            <LessonIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                            {course.totalLessons} bài học • {course.totalAssignments} bài tập
                                        </Typography>
                                        <Typography variant="body2" sx={{ mb: 1 }}>
                                            <ScheduleIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                            {formatTime(course.estimatedTime)} • {course.duration}
                                        </Typography>
                                        <Typography variant="body2" sx={{ mb: 1 }}>
                                            <StarIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                            {course.avgRating}/5.0 ({course.enrolledStudents} học sinh)
                                        </Typography>
                                    </Box>

                                    <Typography variant="body2" color="textSecondary" gutterBottom>
                                        Mục tiêu:
                                    </Typography>
                                    <Box sx={{ mb: 2 }}>
                                        {course.objectives.slice(0, 2).map((objective, index) => (
                                            <Typography key={index} variant="caption" display="block" sx={{ mb: 0.5 }}>
                                                • {objective}
                                            </Typography>
                                        ))}
                                        {course.objectives.length > 2 && (
                                            <Typography variant="caption" color="textSecondary">
                                                +{course.objectives.length - 2} mục tiêu khác...
                                            </Typography>
                                        )}
                                    </Box>
                                </CardContent>

                                <CardActions>
                                    <Button
                                        variant={course.isAssigned ? "contained" : "outlined"}
                                        startIcon={course.isAssigned ? <SchoolIcon /> : <PlayIcon />}
                                        fullWidth
                                        onClick={() => handleEnrollCourse(course)}
                                        sx={{
                                            borderRadius: '10px',
                                            bgcolor: course.isAssigned ? '#2e7d32' : 'transparent'
                                        }}
                                    >
                                        {course.isAssigned ? 'Đăng ký ngay' : 'Tham gia khóa học'}
                                    </Button>
                                </CardActions>
                            </Card>
                        </Grid>
                    ))}
                </Grid>
            </Box>

            {/* Enroll Confirmation Dialog */}
            <Dialog open={openEnrollDialog} onClose={() => setOpenEnrollDialog(false)} maxWidth="sm" fullWidth>
                <DialogTitle>
                    Đăng ký khóa học
                </DialogTitle>
                <DialogContent>
                    {selectedCourse && (
                        <Box>
                            <Typography variant="h6" gutterBottom>
                                {selectedCourse.title}
                            </Typography>
                            <Typography variant="body2" color="textSecondary" gutterBottom>
                                Giáo viên: {selectedCourse.teacher}
                            </Typography>

                            <Box sx={{ my: 2 }}>
                                <Typography variant="body2" gutterBottom>
                                    <strong>Thời lượng:</strong> {selectedCourse.duration}
                                </Typography>
                                <Typography variant="body2" gutterBottom>
                                    <strong>Số bài học:</strong> {selectedCourse.totalLessons} bài học, {selectedCourse.totalAssignments} bài tập
                                </Typography>
                                <Typography variant="body2" gutterBottom>
                                    <strong>Thời gian ước tính:</strong> {formatTime(selectedCourse.estimatedTime)}
                                </Typography>
                            </Box>

                            <Typography variant="body2" gutterBottom sx={{ mt: 2 }}>
                                <strong>Yêu cầu tiên quyết:</strong>
                            </Typography>
                            {selectedCourse.prerequisites.map((req, index) => (
                                <Typography key={index} variant="body2" sx={{ ml: 2, mb: 0.5 }}>
                                    • {req}
                                </Typography>
                            ))}

                            <Alert severity="info" sx={{ mt: 2, borderRadius: '10px' }}>
                                Bạn có chắc chắn muốn đăng ký khóa học này không?
                            </Alert>
                        </Box>
                    )}
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setOpenEnrollDialog(false)} sx={{ borderRadius: '10px' }}>
                        Hủy
                    </Button>
                    <Button onClick={confirmEnroll} variant="contained" sx={{ borderRadius: '10px' }}>
                        Đăng ký
                    </Button>
                </DialogActions>
            </Dialog>

            {/* Snackbar */}
            <Snackbar
                open={snackbar.open}
                autoHideDuration={6000}
                onClose={() => setSnackbar({ ...snackbar, open: false })}
            >
                <Alert
                    onClose={() => setSnackbar({ ...snackbar, open: false })}
                    severity={snackbar.severity}
                    sx={{ width: '100%' }}
                >
                    {snackbar.message}
                </Alert>
            </Snackbar>
        </Box>
    );
}

export default StudentLearning;
