import React, { useState, useEffect } from 'react';
import {
    Box,
    Typography,
    Grid,
    Card,
    CardContent,
    CardActions,
    Button,
    Chip,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Alert,
    Snackbar,
    Paper,
    Radio,
    RadioGroup,
    FormControlLabel,
    FormControl,
    LinearProgress,
    IconButton,
    Divider,
    List,
    ListItem,
    ListItemText,
    TextField,
    FormLabel
} from '@mui/material';
import {
    Quiz as QuizIcon,
    Timer as TimerIcon,
    PlayArrow as StartIcon,
    CheckCircle as CheckCircleIcon,
    Cancel as CancelIcon,
    Schedule as ScheduleIcon,
    Person as PersonIcon,
    Assignment as AssignmentIcon,
    Visibility as ViewIcon,
    NavigateNext as NextIcon,
    NavigateBefore as PrevIcon,
    Flag as FlagIcon,
    AccessTime as TimeIcon,
    Assessment as ResultIcon
} from '@mui/icons-material';

function TakeQuiz({ user }) {
    const [quizzes, setQuizzes] = useState([]);
    const [selectedQuiz, setSelectedQuiz] = useState(null);
    const [openQuizDialog, setOpenQuizDialog] = useState(false);
    const [openResultDialog, setOpenResultDialog] = useState(false);
    const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
    const [answers, setAnswers] = useState({});
    const [timeLeft, setTimeLeft] = useState(0);
    const [isQuizStarted, setIsQuizStarted] = useState(false);
    const [quizResult, setQuizResult] = useState(null);
    const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

    useEffect(() => {
        // Giả lập dữ liệu bài kiểm tra
        const mockQuizzes = [
            {
                id: 1,
                title: 'Kiểm tra Toán học giữa kỳ',
                course: 'Toán học nâng cao 8A',
                teacher: 'Cô Nguyễn Thị A',
                description: 'Bài kiểm tra toán học cho học sinh lớp 8A',
                duration: 45,
                totalQuestions: 10,
                passingScore: 70,
                startTime: '2024-01-20 14:00',
                endTime: '2024-01-20 14:45',
                status: 'available',
                attempts: 0,
                maxAttempts: 2,
                questions: [
                    {
                        id: 1,
                        question: 'Kết quả của phép tính 2 + 2 là gì?',
                        options: ['3', '4', '5', '6'],
                        correctAnswer: 1
                    },
                    {
                        id: 2,
                        question: 'Công thức tính diện tích hình tròn là gì?',
                        options: ['πr²', '2πr', 'πd', 'r²'],
                        correctAnswer: 0
                    },
                    {
                        id: 3,
                        question: 'Nghiệm của phương trình x² - 5x + 6 = 0 là?',
                        options: ['x = 1, x = 6', 'x = 2, x = 3', 'x = -2, x = -3', 'Vô nghiệm'],
                        correctAnswer: 1
                    }
                ]
            },
            {
                id: 2,
                title: 'Bài kiểm tra Vật lý',
                course: 'Vật lý thí nghiệm 9B',
                teacher: 'Thầy Trần Văn B',
                description: 'Kiểm tra kiến thức vật lý cơ bản',
                duration: 30,
                totalQuestions: 8,
                passingScore: 60,
                startTime: '2024-01-25 15:00',
                endTime: '2024-01-25 15:30',
                status: 'scheduled',
                attempts: 0,
                maxAttempts: 1,
                questions: [
                    {
                        id: 1,
                        question: 'Đơn vị đo lực trong hệ SI là gì?',
                        options: ['Joule', 'Newton', 'Watt', 'Pascal'],
                        correctAnswer: 1
                    },
                    {
                        id: 2,
                        question: 'Công thức tính vận tốc là?',
                        options: ['v = s/t', 'v = s*t', 'v = t/s', 'v = s + t'],
                        correctAnswer: 0
                    }
                ]
            },
            {
                id: 3,
                title: 'Kiểm tra Scratch cơ bản',
                course: 'Lập trình Scratch 7C',
                teacher: 'Cô Lê Thị C',
                description: 'Bài kiểm tra kiến thức Scratch',
                duration: 20,
                totalQuestions: 5,
                passingScore: 80,
                startTime: '2024-01-15 16:00',
                endTime: '2024-01-15 16:20',
                status: 'completed',
                attempts: 1,
                maxAttempts: 1,
                lastScore: 90,
                completedAt: '2024-01-15 16:18',
                questions: []
            }
        ];
        setQuizzes(mockQuizzes);
    }, []);

    // Timer effect
    useEffect(() => {
        let interval = null;
        if (isQuizStarted && timeLeft > 0) {
            interval = setInterval(() => {
                setTimeLeft(timeLeft => timeLeft - 1);
            }, 1000);
        } else if (timeLeft === 0 && isQuizStarted) {
            handleSubmitQuiz();
        }
        return () => clearInterval(interval);
    }, [isQuizStarted, timeLeft]);

    const handleStartQuiz = (quiz) => {
        setSelectedQuiz(quiz);
        setAnswers({});
        setCurrentQuestionIndex(0);
        setTimeLeft(quiz.duration * 60); // Convert minutes to seconds
        setIsQuizStarted(false);
        setOpenQuizDialog(true);
    };

    const handleBeginQuiz = () => {
        setIsQuizStarted(true);
        setSnackbar({
            open: true,
            message: 'Bài kiểm tra đã bắt đầu!',
            severity: 'info'
        });
    };

    const handleAnswerChange = (questionId, answerIndex) => {
        setAnswers(prev => ({
            ...prev,
            [questionId]: answerIndex
        }));
    };

    const handleNextQuestion = () => {
        if (currentQuestionIndex < selectedQuiz.questions.length - 1) {
            setCurrentQuestionIndex(currentQuestionIndex + 1);
        }
    };

    const handlePrevQuestion = () => {
        if (currentQuestionIndex > 0) {
            setCurrentQuestionIndex(currentQuestionIndex - 1);
        }
    };

    const handleSubmitQuiz = () => {
        if (!selectedQuiz) return;

        // Calculate score
        let correctAnswers = 0;
        selectedQuiz.questions.forEach(question => {
            if (answers[question.id] === question.correctAnswer) {
                correctAnswers++;
            }
        });

        const score = Math.round((correctAnswers / selectedQuiz.questions.length) * 100);
        const passed = score >= selectedQuiz.passingScore;

        const result = {
            quizId: selectedQuiz.id,
            score: score,
            correctAnswers: correctAnswers,
            totalQuestions: selectedQuiz.questions.length,
            passed: passed,
            timeSpent: (selectedQuiz.duration * 60) - timeLeft,
            completedAt: new Date().toISOString()
        };

        setQuizResult(result);

        // Update quiz status
        setQuizzes(prev => prev.map(quiz => 
            quiz.id === selectedQuiz.id 
                ? { 
                    ...quiz, 
                    status: 'completed',
                    attempts: quiz.attempts + 1,
                    lastScore: score,
                    completedAt: result.completedAt
                }
                : quiz
        ));

        setIsQuizStarted(false);
        setOpenQuizDialog(false);
        setOpenResultDialog(true);
    };

    const handleCloseQuizDialog = () => {
        if (isQuizStarted) {
            if (window.confirm('Bạn có chắc chắn muốn thoát? Kết quả sẽ không được lưu.')) {
                setOpenQuizDialog(false);
                setIsQuizStarted(false);
                setSelectedQuiz(null);
            }
        } else {
            setOpenQuizDialog(false);
            setSelectedQuiz(null);
        }
    };

    const handleCloseResultDialog = () => {
        setOpenResultDialog(false);
        setQuizResult(null);
    };

    const getStatusChip = (quiz) => {
        switch (quiz.status) {
            case 'available':
                return <Chip label="Có thể làm" color="success" size="small" icon={<StartIcon />} />;
            case 'scheduled':
                return <Chip label="Đã lên lịch" color="info" size="small" icon={<ScheduleIcon />} />;
            case 'completed':
                return <Chip label="Đã hoàn thành" color="primary" size="small" icon={<CheckCircleIcon />} />;
            case 'expired':
                return <Chip label="Đã hết hạn" color="error" size="small" icon={<CancelIcon />} />;
            default:
                return <Chip label="Không xác định" color="default" size="small" />;
        }
    };

    const formatTime = (seconds) => {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    };

    const canTakeQuiz = (quiz) => {
        return quiz.status === 'available' && quiz.attempts < quiz.maxAttempts;
    };

    const currentQuestion = selectedQuiz?.questions[currentQuestionIndex];
    const progress = selectedQuiz ? ((currentQuestionIndex + 1) / selectedQuiz.questions.length) * 100 : 0;

    return (
        <Box>
            {/* Header */}
            <Box sx={{ mb: 4 }}>
                <Typography variant="h4" gutterBottom>
                    Bài kiểm tra
                </Typography>
                <Typography variant="body1" color="textSecondary">
                    Làm các bài kiểm tra được giao và theo dõi kết quả
                </Typography>
            </Box>

            {/* Statistics */}
            <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12} sm={6} md={3}>
                    <Card>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Tổng bài kiểm tra
                            </Typography>
                            <Typography variant="h4">
                                {quizzes.length}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                    <Card>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Có thể làm
                            </Typography>
                            <Typography variant="h4" color="success.main">
                                {quizzes.filter(q => q.status === 'available').length}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                    <Card>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Đã hoàn thành
                            </Typography>
                            <Typography variant="h4" color="primary.main">
                                {quizzes.filter(q => q.status === 'completed').length}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                    <Card>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Điểm trung bình
                            </Typography>
                            <Typography variant="h4">
                                {quizzes.filter(q => q.lastScore).length > 0 
                                    ? Math.round(quizzes.filter(q => q.lastScore).reduce((sum, q) => sum + q.lastScore, 0) / quizzes.filter(q => q.lastScore).length)
                                    : 0
                                }
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
            </Grid>

            {/* Quizzes Grid */}
            <Grid container spacing={3}>
                {quizzes.map((quiz) => (
                    <Grid item xs={12} md={6} lg={4} key={quiz.id}>
                        <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                            <CardContent sx={{ flexGrow: 1 }}>
                                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                                    <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
                                        {quiz.title}
                                    </Typography>
                                    <QuizIcon />
                                </Box>
                                
                                <Box sx={{ mb: 2 }}>
                                    <Typography variant="body2" color="textSecondary" sx={{ mb: 1 }}>
                                        <PersonIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                        {quiz.teacher} • {quiz.course}
                                    </Typography>
                                    <Typography variant="body2" color="textSecondary">
                                        {quiz.description}
                                    </Typography>
                                </Box>

                                <Box sx={{ mb: 2 }}>
                                    {getStatusChip(quiz)}
                                </Box>

                                <Box sx={{ mb: 2 }}>
                                    <Typography variant="body2" sx={{ mb: 1 }}>
                                        <TimerIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                        Thời gian: {quiz.duration} phút
                                    </Typography>
                                    <Typography variant="body2" sx={{ mb: 1 }}>
                                        <AssignmentIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                        Số câu hỏi: {quiz.totalQuestions}
                                    </Typography>
                                    <Typography variant="body2" sx={{ mb: 1 }}>
                                        <FlagIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                        Điểm đạt: {quiz.passingScore}%
                                    </Typography>
                                    <Typography variant="body2">
                                        Lần làm: {quiz.attempts}/{quiz.maxAttempts}
                                    </Typography>
                                </Box>

                                {quiz.status === 'scheduled' && (
                                    <Alert severity="info" sx={{ mb: 2 }}>
                                        Thời gian: {quiz.startTime} - {quiz.endTime}
                                    </Alert>
                                )}

                                {quiz.lastScore && (
                                    <Box sx={{ mb: 2 }}>
                                        <Typography variant="body2" color="textSecondary">
                                            Điểm gần nhất:
                                        </Typography>
                                        <Typography variant="h6" color={quiz.lastScore >= quiz.passingScore ? 'success.main' : 'error.main'}>
                                            {quiz.lastScore} điểm
                                        </Typography>
                                    </Box>
                                )}
                            </CardContent>
                            
                            <CardActions>
                                {canTakeQuiz(quiz) && (
                                    <Button
                                        size="small"
                                        variant="contained"
                                        startIcon={<StartIcon />}
                                        onClick={() => handleStartQuiz(quiz)}
                                        sx={{ bgcolor: '#2e7d32' }}
                                    >
                                        Bắt đầu làm
                                    </Button>
                                )}
                                {quiz.status === 'completed' && (
                                    <Button
                                        size="small"
                                        startIcon={<ViewIcon />}
                                    >
                                        Xem kết quả
                                    </Button>
                                )}
                                {!canTakeQuiz(quiz) && quiz.status !== 'completed' && (
                                    <Button
                                        size="small"
                                        disabled
                                    >
                                        Không thể làm
                                    </Button>
                                )}
                            </CardActions>
                        </Card>
                    </Grid>
                ))}
            </Grid>

            {/* Quiz Taking Dialog */}
            <Dialog 
                open={openQuizDialog} 
                onClose={handleCloseQuizDialog} 
                maxWidth="md" 
                fullWidth
                disableEscapeKeyDown={isQuizStarted}
            >
                <DialogTitle>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography variant="h6">
                            {selectedQuiz?.title}
                        </Typography>
                        {isQuizStarted && (
                            <Chip
                                label={formatTime(timeLeft)}
                                color={timeLeft < 300 ? 'error' : 'primary'}
                                icon={<TimerIcon />}
                            />
                        )}
                    </Box>
                </DialogTitle>
                <DialogContent>
                    {selectedQuiz && (
                        <Box>
                            {!isQuizStarted ? (
                                // Quiz Instructions
                                <Box>
                                    <Typography variant="h6" gutterBottom>
                                        Hướng dẫn làm bài
                                    </Typography>
                                    <List>
                                        <ListItem>
                                            <ListItemText primary={`Thời gian làm bài: ${selectedQuiz.duration} phút`} />
                                        </ListItem>
                                        <ListItem>
                                            <ListItemText primary={`Số câu hỏi: ${selectedQuiz.totalQuestions} câu`} />
                                        </ListItem>
                                        <ListItem>
                                            <ListItemText primary={`Điểm đạt: ${selectedQuiz.passingScore}%`} />
                                        </ListItem>
                                        <ListItem>
                                            <ListItemText primary="Chọn đáp án đúng nhất cho mỗi câu hỏi" />
                                        </ListItem>
                                        <ListItem>
                                            <ListItemText primary="Có thể quay lại câu hỏi trước đó để sửa đáp án" />
                                        </ListItem>
                                        <ListItem>
                                            <ListItemText primary="Bài thi sẽ tự động nộp khi hết thời gian" />
                                        </ListItem>
                                    </List>
                                    <Alert severity="warning" sx={{ mt: 2 }}>
                                        Sau khi bắt đầu, bạn không thể thoát khỏi bài kiểm tra!
                                    </Alert>
                                </Box>
                            ) : (
                                // Quiz Questions
                                <Box>
                                    <Box sx={{ mb: 3 }}>
                                        <Typography variant="body2" color="textSecondary" gutterBottom>
                                            Câu {currentQuestionIndex + 1} / {selectedQuiz.questions.length}
                                        </Typography>
                                        <LinearProgress variant="determinate" value={progress} sx={{ mb: 2 }} />
                                    </Box>

                                    {currentQuestion && (
                                        <Box>
                                            <Typography variant="h6" gutterBottom>
                                                {currentQuestion.question}
                                            </Typography>
                                            <FormControl component="fieldset" fullWidth>
                                                <RadioGroup
                                                    value={answers[currentQuestion.id] || ''}
                                                    onChange={(e) => handleAnswerChange(currentQuestion.id, parseInt(e.target.value))}
                                                >
                                                    {currentQuestion.options.map((option, index) => (
                                                        <FormControlLabel
                                                            key={index}
                                                            value={index}
                                                            control={<Radio />}
                                                            label={`${String.fromCharCode(65 + index)}. ${option}`}
                                                        />
                                                    ))}
                                                </RadioGroup>
                                            </FormControl>
                                        </Box>
                                    )}

                                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
                                        <Button
                                            startIcon={<PrevIcon />}
                                            onClick={handlePrevQuestion}
                                            disabled={currentQuestionIndex === 0}
                                        >
                                            Câu trước
                                        </Button>
                                        <Button
                                            endIcon={<NextIcon />}
                                            onClick={handleNextQuestion}
                                            disabled={currentQuestionIndex === selectedQuiz.questions.length - 1}
                                        >
                                            Câu tiếp
                                        </Button>
                                    </Box>
                                </Box>
                            )}
                        </Box>
                    )}
                </DialogContent>
                <DialogActions>
                    {!isQuizStarted ? (
                        <>
                            <Button onClick={handleCloseQuizDialog}>Hủy</Button>
                            <Button 
                                variant="contained" 
                                onClick={handleBeginQuiz}
                                sx={{ bgcolor: '#2e7d32' }}
                            >
                                Bắt đầu làm bài
                            </Button>
                        </>
                    ) : (
                        <Button 
                            variant="contained" 
                            color="primary"
                            onClick={handleSubmitQuiz}
                        >
                            Nộp bài
                        </Button>
                    )}
                </DialogActions>
            </Dialog>

            {/* Quiz Result Dialog */}
            <Dialog open={openResultDialog} onClose={handleCloseResultDialog} maxWidth="sm" fullWidth>
                <DialogTitle>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <ResultIcon sx={{ mr: 1 }} />
                        Kết quả bài kiểm tra
                    </Box>
                </DialogTitle>
                <DialogContent>
                    {quizResult && (
                        <Box sx={{ textAlign: 'center' }}>
                            <Typography variant="h3" color={quizResult.passed ? 'success.main' : 'error.main'} gutterBottom>
                                {quizResult.score}%
                            </Typography>
                            <Typography variant="h6" gutterBottom>
                                {quizResult.passed ? 'Chúc mừng! Bạn đã đạt!' : 'Chưa đạt. Hãy cố gắng hơn!'}
                            </Typography>
                            
                            <Paper sx={{ p: 2, mt: 3, textAlign: 'left' }}>
                                <Typography variant="body1" sx={{ mb: 1 }}>
                                    <strong>Số câu đúng:</strong> {quizResult.correctAnswers}/{quizResult.totalQuestions}
                                </Typography>
                                <Typography variant="body1" sx={{ mb: 1 }}>
                                    <strong>Thời gian làm bài:</strong> {formatTime(quizResult.timeSpent)}
                                </Typography>
                                <Typography variant="body1">
                                    <strong>Hoàn thành lúc:</strong> {new Date(quizResult.completedAt).toLocaleString('vi-VN')}
                                </Typography>
                            </Paper>

                            {!quizResult.passed && (
                                <Alert severity="info" sx={{ mt: 2 }}>
                                    Bạn có thể làm lại bài kiểm tra nếu còn lượt thử.
                                </Alert>
                            )}
                        </Box>
                    )}
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseResultDialog} variant="contained">
                        Đóng
                    </Button>
                </DialogActions>
            </Dialog>

            {/* Snackbar */}
            <Snackbar
                open={snackbar.open}
                autoHideDuration={6000}
                onClose={() => setSnackbar({ ...snackbar, open: false })}
            >
                <Alert
                    onClose={() => setSnackbar({ ...snackbar, open: false })}
                    severity={snackbar.severity}
                    sx={{ width: '100%' }}
                >
                    {snackbar.message}
                </Alert>
            </Snackbar>
        </Box>
    );
}

export default TakeQuiz;
