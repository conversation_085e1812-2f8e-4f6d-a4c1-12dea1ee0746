import React, { useState, useEffect } from 'react';
import {
    Box,
    Typography,
    Grid,
    Card,
    CardContent,
    Paper,
    List,
    ListItem,
    ListItemText,
    Chip,
    LinearProgress,
    Avatar,
    IconButton,
    Button,
    Alert
} from '@mui/material';
import {
    School as SchoolIcon,
    Assignment as AssignmentIcon,
    Quiz as QuizIcon,
    Games as TurboWarpIcon,
    TrendingUp as TrendingUpIcon,
    Schedule as ScheduleIcon,
    CheckCircle as CheckCircleIcon,
    PlayArrow as PlayIcon,
    Notifications as NotificationIcon
} from '@mui/icons-material';

function StudentDashboard({ user }) {
    const [stats, setStats] = useState({
        enrolledCourses: 0,
        completedAssignments: 0,
        pendingQuizzes: 0,
        turboWarpProjects: 0
    });

    const [recentActivities, setRecentActivities] = useState([]);
    const [upcomingDeadlines, setUpcomingDeadlines] = useState([]);
    const [currentCourses, setCurrentCourses] = useState([]);

    useEffect(() => {
        // <PERSON><PERSON><PERSON> lập dữ liệu - sau này sẽ thay bằng API calls
        setStats({
            enrolledCourses: 3,
            completedAssignments: 12,
            pendingQuizzes: 2,
            turboWarpProjects: 5
        });

        setRecentActivities([
            { id: 1, type: 'quiz', title: 'Hoàn thành bài kiểm tra Toán học', time: '1 giờ trước', status: 'completed', score: 85 },
            { id: 2, type: 'lesson', title: 'Xem bài giảng Vật lý - Chuyển động', time: '2 giờ trước', status: 'viewed' },
            { id: 3, type: 'assignment', title: 'Nộp bài tập Scratch - Game Pong', time: '1 ngày trước', status: 'submitted' },
        ]);

        setUpcomingDeadlines([
            { id: 1, title: 'Bài kiểm tra Vật lý giữa kỳ', course: 'Vật lý 9B', deadline: '2024-01-18 14:00', priority: 'high', type: 'quiz' },
            { id: 2, title: 'Nộp bài tập Toán học', course: 'Toán 8A', deadline: '2024-01-20 23:59', priority: 'medium', type: 'assignment' },
            { id: 3, title: 'Dự án Scratch cuối kỳ', course: 'Tin học 7C', deadline: '2024-01-25 23:59', priority: 'low', type: 'turbowarp' },
        ]);

        setCurrentCourses([
            {
                id: 1,
                name: 'Toán học nâng cao 8A',
                teacher: 'Cô Nguyễn Thị A',
                progress: 75,
                nextLesson: 'Phương trình bậc hai',
                nextLessonTime: '2024-01-16 14:00'
            },
            {
                id: 2,
                name: 'Vật lý thí nghiệm 9B',
                teacher: 'Thầy Trần Văn B',
                progress: 60,
                nextLesson: 'Định luật Newton',
                nextLessonTime: '2024-01-17 15:00'
            },
            {
                id: 3,
                name: 'Lập trình Scratch 7C',
                teacher: 'Cô Lê Thị C',
                progress: 90,
                nextLesson: 'Game nâng cao',
                nextLessonTime: '2024-01-18 16:00'
            }
        ]);
    }, []);

    const StatCard = ({ title, value, icon, color, subtitle }) => (
        <Card sx={{ height: '100%', borderRadius: '10px' }}>
            <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box>
                        <Typography color="textSecondary" gutterBottom variant="overline">
                            {title}
                        </Typography>
                        <Typography variant="h4" component="div" sx={{ color: color }}>
                            {value}
                        </Typography>
                        {subtitle && (
                            <Typography variant="body2" color="textSecondary">
                                {subtitle}
                            </Typography>
                        )}
                    </Box>
                    <Avatar sx={{ bgcolor: color, width: 56, height: 56 }}>
                        {icon}
                    </Avatar>
                </Box>
            </CardContent>
        </Card>
    );

    const getStatusColor = (status) => {
        switch (status) {
            case 'completed': return 'success';
            case 'viewed': return 'info';
            case 'submitted': return 'warning';
            default: return 'default';
        }
    };

    const getPriorityColor = (priority) => {
        switch (priority) {
            case 'high': return 'error';
            case 'medium': return 'warning';
            case 'low': return 'success';
            default: return 'default';
        }
    };

    const getTypeIcon = (type) => {
        switch (type) {
            case 'quiz': return <QuizIcon sx={{ fontSize: 16 }} />;
            case 'assignment': return <AssignmentIcon sx={{ fontSize: 16 }} />;
            case 'turbowarp': return <TurboWarpIcon sx={{ fontSize: 16 }} />;
            default: return <ScheduleIcon sx={{ fontSize: 16 }} />;
        }
    };

    return (
        <Box>
            {/* Welcome Section */}
            <Box sx={{ mb: 4 }}>
                <Typography variant="h4" gutterBottom>
                    Chào {user?.first_name || 'bạn'}! 👋
                </Typography>
                <Typography variant="body1" color="textSecondary">
                    Hôm nay là {new Date().toLocaleDateString('vi-VN', {
                        weekday: 'long',
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                    })}. Hãy tiếp tục hành trình học tập của bạn!
                </Typography>
            </Box>

            {/* Statistics Cards */}
            <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12} sm={6} md={3}>
                    <StatCard
                        title="Khóa học đang học"
                        value={stats.enrolledCourses}
                        icon={<SchoolIcon />}
                        color="#2e7d32"
                    />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                    <StatCard
                        title="Bài tập đã hoàn thành"
                        value={stats.completedAssignments}
                        icon={<AssignmentIcon />}
                        color="#1976d2"
                    />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                    <StatCard
                        title="Bài kiểm tra chờ làm"
                        value={stats.pendingQuizzes}
                        icon={<QuizIcon />}
                        color="#f57c00"
                    />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                    <StatCard
                        title="Dự án TurboWarp"
                        value={stats.turboWarpProjects}
                        icon={<TurboWarpIcon />}
                        color="#7b1fa2"
                    />
                </Grid>
            </Grid>

            {/* Content Grid */}
            <Grid container spacing={3}>
                {/* Current Courses */}
                <Grid item xs={12} lg={8}>
                    <Paper sx={{ p: 3, mb: 3, borderRadius: '10px' }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                            <Typography variant="h6">
                                Khóa học đang theo dõi
                            </Typography>
                            <IconButton>
                                <SchoolIcon />
                            </IconButton>
                        </Box>
                        <Grid container spacing={2}>
                            {currentCourses.map((course) => (
                                <Grid item xs={12} md={6} key={course.id}>
                                    <Card sx={{ height: '100%', borderRadius: '10px' }}>
                                        <CardContent>
                                            <Typography variant="h6" gutterBottom>
                                                {course.name}
                                            </Typography>
                                            <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
                                                {course.teacher}
                                            </Typography>

                                            <Box sx={{ mb: 2 }}>
                                                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                                                    <Typography variant="body2">Tiến độ</Typography>
                                                    <Typography variant="body2">{course.progress}%</Typography>
                                                </Box>
                                                <LinearProgress
                                                    variant="determinate"
                                                    value={course.progress}
                                                    sx={{ height: 8, borderRadius: 4 }}
                                                />
                                            </Box>

                                            <Box sx={{ mb: 2 }}>
                                                <Typography variant="body2" color="textSecondary">
                                                    Bài tiếp theo:
                                                </Typography>
                                                <Typography variant="body2" fontWeight="bold">
                                                    {course.nextLesson}
                                                </Typography>
                                                <Typography variant="caption" color="textSecondary">
                                                    {course.nextLessonTime}
                                                </Typography>
                                            </Box>

                                            <Button
                                                variant="contained"
                                                size="small"
                                                startIcon={<PlayIcon />}
                                                fullWidth
                                                sx={{ bgcolor: '#2e7d32', borderRadius: '10px' }}
                                            >
                                                Tiếp tục học
                                            </Button>
                                        </CardContent>
                                    </Card>
                                </Grid>
                            ))}
                        </Grid>
                    </Paper>

                    {/* Recent Activities */}
                    <Paper sx={{ p: 3, borderRadius: '10px' }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                            <Typography variant="h6">
                                Hoạt động gần đây
                            </Typography>
                            <IconButton>
                                <TrendingUpIcon />
                            </IconButton>
                        </Box>
                        <List>
                            {recentActivities.map((activity) => (
                                <ListItem key={activity.id} divider>
                                    <ListItemText
                                        primary={activity.title}
                                        secondary={activity.time}
                                    />
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                        {activity.score && (
                                            <Chip
                                                label={`${activity.score} điểm`}
                                                color="success"
                                                size="small"
                                            />
                                        )}
                                        <Chip
                                            label={activity.status}
                                            color={getStatusColor(activity.status)}
                                            size="small"
                                        />
                                    </Box>
                                </ListItem>
                            ))}
                        </List>
                        <Box sx={{ mt: 2, textAlign: 'center' }}>
                            <Button variant="outlined" size="small">
                                Xem tất cả hoạt động
                            </Button>
                        </Box>
                    </Paper>
                </Grid>

                {/* Upcoming Deadlines */}
                <Grid item xs={12} lg={4}>
                    <Paper sx={{ p: 3, borderRadius: '10px' }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                            <Typography variant="h6">
                                Deadline sắp tới
                            </Typography>
                            <IconButton>
                                <NotificationIcon />
                            </IconButton>
                        </Box>
                        <List>
                            {upcomingDeadlines.map((deadline) => (
                                <ListItem key={deadline.id} divider>
                                    <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>
                                        {getTypeIcon(deadline.type)}
                                    </Box>
                                    <ListItemText
                                        primary={deadline.title}
                                        secondary={`${deadline.course} • ${deadline.deadline}`}
                                    />
                                    <Chip
                                        label={deadline.priority}
                                        color={getPriorityColor(deadline.priority)}
                                        size="small"
                                    />
                                </ListItem>
                            ))}
                        </List>
                        <Box sx={{ mt: 2, textAlign: 'center' }}>
                            <Button variant="outlined" size="small">
                                Xem lịch đầy đủ
                            </Button>
                        </Box>
                    </Paper>
                </Grid>
            </Grid>

            {/* Quick Actions */}
            <Paper sx={{ p: 3, mt: 3, borderRadius: '10px' }}>
                <Typography variant="h6" gutterBottom>
                    Thao tác nhanh
                </Typography>
                <Grid container spacing={2}>
                    <Grid item>
                        <Button
                            variant="contained"
                            startIcon={<PlayIcon />}
                            sx={{ mr: 1, bgcolor: '#2e7d32', borderRadius: '10px' }}
                        >
                            Tiếp tục học bài
                        </Button>
                    </Grid>
                    <Grid item>
                        <Button
                            variant="outlined"
                            startIcon={<QuizIcon />}
                            sx={{ mr: 1, borderRadius: '10px' }}
                        >
                            Làm bài kiểm tra
                        </Button>
                    </Grid>
                    <Grid item>
                        <Button
                            variant="outlined"
                            startIcon={<TurboWarpIcon />}
                            sx={{ mr: 1, borderRadius: '10px' }}
                        >
                            Làm bài TurboWarp
                        </Button>
                    </Grid>
                    <Grid item>
                        <Button
                            variant="outlined"
                            startIcon={<SchoolIcon />}
                            sx={{ mr: 1, borderRadius: '10px' }}
                        >
                            Xem khóa học mới
                        </Button>
                    </Grid>
                </Grid>
            </Paper>
        </Box>
    );
}

export default StudentDashboard;
