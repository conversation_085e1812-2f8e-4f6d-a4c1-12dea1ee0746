import React, { useState, useEffect } from 'react';
import {
    Box,
    Typography,
    Card,
    CardContent,
    CardActions,
    Button,
    Chip,
    Avatar,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Alert,
    Snackbar,
    TextField,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Paper,
    List,
    ListItem,
    ListItemText,
    ListItemIcon,
    Divider
} from '@mui/material';
import Grid from "@mui/material/Grid2";
import {
    School as SchoolIcon,
    Person as PersonIcon,
    Schedule as ScheduleIcon,
    Assignment as AssignmentIcon,
    Quiz as QuizIcon,
    CheckCircle as CheckCircleIcon,
    HourglassEmpty as PendingIcon,
    Cancel as CancelIcon,
    Add as AddIcon,
    Search as SearchIcon,
    FilterList as FilterIcon,
    BookOnline as LessonIcon,
    AccessTime as TimeIcon,
    Group as GroupIcon,
    Payment as PaymentIcon,
    MonetizationOn as PriceIcon,
    Star as StarIcon,
    Discount as DiscountIcon,
    CreditCard as CreditCardIcon,
    AccountBalanceWallet as WalletIcon
} from '@mui/icons-material';

function CourseEnrollment({ user }) {
    const [availableCourses, setAvailableCourses] = useState([]);
    const [filteredCourses, setFilteredCourses] = useState([]);
    const [selectedCourse, setSelectedCourse] = useState(null);
    const [openDialog, setOpenDialog] = useState(false);
    const [openPaymentDialog, setOpenPaymentDialog] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [filterSubject, setFilterSubject] = useState('');
    const [filterPrice, setFilterPrice] = useState('');
    const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
    const [paymentMethod, setPaymentMethod] = useState('credit_card');

    const subjects = ['Toán học', 'Vật lý', 'Hóa học', 'Sinh học', 'Tin học', 'STEM'];
    const priceRanges = ['free', 'under_500k', '500k_1m', 'over_1m'];

    useEffect(() => {
        // Giả lập dữ liệu khóa học online có sẵn
        const mockCourses = [
            {
                id: 4,
                name: 'Hóa học cơ bản 9A - Online',
                teacher: 'Thầy Phạm Văn D',
                subject: 'Hóa học',
                grade: 'Lớp 9',
                description: 'Khóa học hóa học cơ bản online với video thí nghiệm và bài tập tương tác',
                totalLessons: 24,
                totalAssignments: 12,
                totalQuizzes: 6,
                duration: '4 tháng',
                estimatedTime: '48 giờ',
                maxStudents: 100,
                currentStudents: 68,
                price: 899000,
                originalPrice: 1200000,
                discount: 25,
                rating: 4.8,
                totalReviews: 156,
                level: 'Cơ bản',
                language: 'Tiếng Việt',
                certificate: true,
                status: 'available',
                features: [
                    'Video HD chất lượng cao',
                    'Thí nghiệm ảo tương tác',
                    'Bài tập thực hành',
                    'Hỗ trợ 24/7',
                    'Chứng chỉ hoàn thành'
                ],
                requirements: ['Hoàn thành Hóa học 8', 'Có kết nối internet ổn định'],
                syllabus: [
                    'Chương 1: Nguyên tử và phân tử (6 bài)',
                    'Chương 2: Phản ứng hóa học (8 bài)',
                    'Chương 3: Axit - Bazơ - Muối (6 bài)',
                    'Chương 4: Hóa học hữu cơ cơ bản (4 bài)'
                ]
            },
            {
                id: 5,
                name: 'Sinh học nâng cao 10B - Premium',
                teacher: 'Cô Hoàng Thị E',
                subject: 'Sinh học',
                grade: 'Lớp 10',
                description: 'Khóa học sinh học nâng cao online cho học sinh giỏi với mentor 1-1',
                totalLessons: 30,
                totalAssignments: 15,
                totalQuizzes: 8,
                duration: '5 tháng',
                estimatedTime: '75 giờ',
                maxStudents: 50,
                currentStudents: 32,
                price: 1299000,
                originalPrice: 1800000,
                discount: 28,
                rating: 4.9,
                totalReviews: 89,
                level: 'Nâng cao',
                language: 'Tiếng Việt',
                certificate: true,
                status: 'available',
                features: [
                    'Mentor 1-1 hàng tuần',
                    'Lab ảo 3D',
                    'Dự án nghiên cứu',
                    'Cộng đồng học tập',
                    'Chứng chỉ quốc tế'
                ],
                requirements: ['Điểm Sinh học 9 >= 8.0', 'Hoàn thành khóa cơ bản'],
                syllabus: [
                    'Chương 1: Tế bào và mô (8 bài)',
                    'Chương 2: Di truyền học (10 bài)',
                    'Chương 3: Tiến hóa (6 bài)',
                    'Chương 4: Sinh thái học (6 bài)'
                ]
            },
            {
                id: 6,
                name: 'Lập trình Python từ Zero đến Hero',
                teacher: 'Thầy Vũ Văn F',
                subject: 'Tin học',
                grade: 'Lớp 8-12',
                description: 'Khóa học lập trình Python toàn diện từ cơ bản đến nâng cao với dự án thực tế',
                totalLessons: 45,
                totalAssignments: 30,
                totalQuizzes: 8,
                duration: '6 tháng',
                estimatedTime: '120 giờ',
                maxStudents: 200,
                currentStudents: 156,
                price: 0,
                originalPrice: 0,
                discount: 0,
                rating: 4.7,
                totalReviews: 234,
                level: 'Tất cả cấp độ',
                language: 'Tiếng Việt',
                certificate: true,
                status: 'purchased',
                purchasedDate: '2024-01-10',
                features: [
                    'Hoàn toàn miễn phí',
                    'Dự án thực tế',
                    'Code review',
                    'Cộng đồng developer',
                    'Cập nhật liên tục'
                ],
                requirements: ['Biết sử dụng máy tính cơ bản', 'Có máy tính/laptop'],
                syllabus: [
                    'Phần 1: Python cơ bản (15 bài)',
                    'Phần 2: Cấu trúc dữ liệu (10 bài)',
                    'Phần 3: OOP và Module (10 bài)',
                    'Phần 4: Dự án thực tế (10 bài)'
                ]
            },
            {
                id: 7,
                name: 'STEM Innovation Bootcamp',
                teacher: 'Cô Đặng Thị G & Team',
                subject: 'STEM',
                grade: 'Lớp 7-12',
                description: 'Bootcamp STEM tích hợp với dự án thực tế và mentor từ các công ty công nghệ',
                totalLessons: 24,
                totalAssignments: 12,
                totalQuizzes: 4,
                duration: '3 tháng',
                estimatedTime: '60 giờ',
                maxStudents: 30,
                currentStudents: 18,
                price: 2499000,
                originalPrice: 3500000,
                discount: 29,
                rating: 4.6,
                totalReviews: 45,
                level: 'Trung bình',
                language: 'Tiếng Việt',
                certificate: true,
                status: 'in_cart',
                addedToCartDate: '2024-01-12',
                features: [
                    'Mentor từ Google, Microsoft',
                    'Dự án startup thực tế',
                    'Networking events',
                    'Internship opportunities',
                    'Chứng chỉ STEM quốc tế'
                ],
                requirements: ['Điểm trung bình >= 7.0', 'Tinh thần sáng tạo', 'Kỹ năng teamwork'],
                syllabus: [
                    'Module 1: Design Thinking (6 bài)',
                    'Module 2: Robotics & IoT (8 bài)',
                    'Module 3: AI & Machine Learning (6 bài)',
                    'Module 4: Startup Project (4 bài)'
                ]
            },
            {
                id: 8,
                name: 'Toán học Olympic - Intensive',
                teacher: 'Thầy Nguyễn Văn H',
                subject: 'Toán học',
                grade: 'Lớp 9-12',
                description: 'Khóa học toán Olympic chuyên sâu cho học sinh xuất sắc',
                totalLessons: 60,
                totalAssignments: 40,
                totalQuizzes: 12,
                duration: '8 tháng',
                estimatedTime: '200 giờ',
                maxStudents: 25,
                currentStudents: 15,
                price: 3999000,
                originalPrice: 5000000,
                discount: 20,
                rating: 4.9,
                totalReviews: 67,
                level: 'Chuyên sâu',
                language: 'Tiếng Việt',
                certificate: true,
                status: 'available',
                features: [
                    'Giáo viên Olympic quốc gia',
                    'Luyện đề thi quốc tế',
                    'Phương pháp giải nâng cao',
                    'Coaching 1-1',
                    'Tham gia thi Olympic'
                ],
                requirements: ['Điểm Toán >= 9.0', 'Vượt qua bài test đầu vào'],
                syllabus: [
                    'Chuyên đề 1: Đại số nâng cao (20 bài)',
                    'Chuyên đề 2: Hình học không gian (15 bài)',
                    'Chuyên đề 3: Tổ hợp và xác suất (15 bài)',
                    'Chuyên đề 4: Luyện đề Olympic (10 bài)'
                ]
            }
        ];
        setAvailableCourses(mockCourses);
        setFilteredCourses(mockCourses);
    }, []);

    useEffect(() => {
        let filtered = availableCourses;

        if (searchTerm) {
            filtered = filtered.filter(course =>
                course.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                course.teacher.toLowerCase().includes(searchTerm.toLowerCase()) ||
                course.description.toLowerCase().includes(searchTerm.toLowerCase())
            );
        }

        if (filterSubject) {
            filtered = filtered.filter(course => course.subject === filterSubject);
        }

        if (filterPrice) {
            filtered = filtered.filter(course => {
                switch (filterPrice) {
                    case 'free':
                        return course.price === 0;
                    case 'under_500k':
                        return course.price > 0 && course.price < 500000;
                    case '500k_1m':
                        return course.price >= 500000 && course.price <= 1000000;
                    case 'over_1m':
                        return course.price > 1000000;
                    default:
                        return true;
                }
            });
        }

        setFilteredCourses(filtered);
    }, [availableCourses, searchTerm, filterSubject, filterPrice]);

    const handleViewCourse = (course) => {
        setSelectedCourse(course);
        setOpenDialog(true);
    };

    const handleCloseDialog = () => {
        setOpenDialog(false);
        setSelectedCourse(null);
    };

    const handleBuyNow = (courseId) => {
        setSelectedCourse(availableCourses.find(c => c.id === courseId));
        setOpenPaymentDialog(true);
        handleCloseDialog();
    };

    const handlePurchase = () => {
        if (selectedCourse) {
            setAvailableCourses(prev => prev.map(course =>
                course.id === selectedCourse.id
                    ? { ...course, status: 'purchased', purchasedDate: new Date().toISOString().split('T')[0] }
                    : course
            ));
            setSnackbar({
                open: true,
                message: selectedCourse.price === 0
                    ? 'Đăng ký khóa học miễn phí thành công! Bạn có thể bắt đầu học ngay.'
                    : 'Mua khóa học thành công! Bạn có thể bắt đầu học ngay.',
                severity: 'success'
            });
        }
        setOpenPaymentDialog(false);
        setSelectedCourse(null);
    };

    const getStatusChip = (status) => {
        const statusMap = {
            'available': { label: 'Có thể mua', color: 'success', icon: <CheckCircleIcon /> },
            'purchased': { label: 'Đã mua', color: 'primary', icon: <SchoolIcon /> },
            'pending': { label: 'Chờ thanh toán', color: 'info', icon: <PendingIcon /> }
        };
        const statusInfo = statusMap[status] || statusMap.available;
        return (
            <Chip
                label={statusInfo.label}
                color={statusInfo.color}
                size="small"
                icon={statusInfo.icon}
            />
        );
    };

    const getSubjectColor = (subject) => {
        const colors = {
            'Toán học': '#1976d2',
            'Vật lý': '#388e3c',
            'Hóa học': '#f57c00',
            'Sinh học': '#7b1fa2',
            'Tin học': '#d32f2f',
            'STEM': '#795548'
        };
        return colors[subject] || '#757575';
    };

    const canPurchase = (course) => {
        return course.status === 'available';
    };

    const formatPrice = (price) => {
        if (price === 0) return 'Miễn phí';
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
        }).format(price);
    };

    const calculateDiscount = (originalPrice, currentPrice) => {
        if (originalPrice <= currentPrice) return 0;
        return Math.round(((originalPrice - currentPrice) / originalPrice) * 100);
    };

    return (
        <Box>
            {/* Header */}
            <Box sx={{ mb: 4 }}>
                <Typography variant="h4" gutterBottom>
                    Mua khóa học Online
                </Typography>
                <Typography variant="body1" color="textSecondary">
                    Khám phá và mua các khóa học online chất lượng cao với giá ưu đãi
                </Typography>

            </Box>

            {/* Statistics */}
            <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Card sx={{ borderRadius: "10px" }}>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Khóa học có sẵn
                            </Typography>
                            <Typography variant="h4" color="success.main">
                                {availableCourses.filter(c => c.status === 'available').length}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Card sx={{ borderRadius: "10px" }}>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Khóa học miễn phí
                            </Typography>
                            <Typography variant="h4" color="success.main">
                                {availableCourses.filter(c => c.price === 0).length}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Card sx={{ borderRadius: "10px" }}>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Đã mua
                            </Typography>
                            <Typography variant="h4" color="primary.main">
                                {availableCourses.filter(c => c.status === 'purchased').length}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Card sx={{ borderRadius: "10px" }}>
                        <CardContent>
                            <Typography color="textSecondary" gutterBottom>
                                Tổng tiết kiệm
                            </Typography>
                            <Typography variant="h4" color="error.main">
                                {formatPrice(availableCourses.reduce((total, course) =>
                                    total + (course.originalPrice - course.price), 0
                                ))}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
            </Grid>

            {/* Filters */}
            <Paper sx={{ p: 2, mb: 3, borderRadius: "10px" }}>
                <Grid container spacing={2} alignItems="center">
                    <Grid size={{ xs: 12, md: 3 }}>
                        <TextField
                            size="small"
                            fullWidth
                            placeholder="Tìm kiếm khóa học..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            InputProps={{
                                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
                            }}
                        />
                    </Grid>
                    <Grid size={{ xs: 12, md: 3 }}>
                        <FormControl fullWidth size="small">
                            <InputLabel>Môn học</InputLabel>
                            <Select
                                value={filterSubject}
                                onChange={(e) => setFilterSubject(e.target.value)}
                                label="Môn học"
                            >
                                <MenuItem value="">Tất cả</MenuItem>
                                {subjects.map(subject => (
                                    <MenuItem key={subject} value={subject}>{subject}</MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                    </Grid>
                    <Grid size={{ xs: 12, md: 3 }}>
                        <FormControl fullWidth size="small">
                            <InputLabel>Mức giá</InputLabel>
                            <Select
                                value={filterPrice}
                                onChange={(e) => setFilterPrice(e.target.value)}
                                label="Mức giá"
                            >
                                <MenuItem value="">Tất cả</MenuItem>
                                <MenuItem value="free">Miễn phí</MenuItem>
                                <MenuItem value="under_500k">Dưới 500K</MenuItem>
                                <MenuItem value="500k_1m">500K - 1M</MenuItem>
                                <MenuItem value="over_1m">Trên 1M</MenuItem>
                            </Select>
                        </FormControl>
                    </Grid>
                    <Grid size={{ xs: 12, md: 3 }}>
                        <Button
                            fullWidth
                            variant="outlined"
                            startIcon={<FilterIcon />}
                            onClick={() => {
                                setSearchTerm('');
                                setFilterSubject('');
                                setFilterPrice('');
                            }}
                        >
                            Xóa bộ lọc
                        </Button>
                    </Grid>
                </Grid>
            </Paper>

            {/* Courses Grid */}
            <Grid container spacing={3}>
                {filteredCourses.map((course) => (
                    <Grid size={{ xs: 12, md: 6, lg: 4 }} key={course.id}>
                        <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column', borderRadius: "10px" }}>
                            <CardContent sx={{ flexGrow: 1 }}>
                                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                                    <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
                                        {course.name}
                                    </Typography>
                                    <Avatar sx={{ bgcolor: getSubjectColor(course.subject), width: 40, height: 40 }}>
                                        {course.subject.charAt(0)}
                                    </Avatar>
                                </Box>

                                <Box sx={{ mb: 2 }}>
                                    <Typography variant="body2" color="textSecondary" sx={{ mb: 1 }}>
                                        <PersonIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                        {course.teacher}
                                    </Typography>
                                    <Typography variant="body2" color="textSecondary">
                                        {course.description}
                                    </Typography>
                                </Box>

                                <Box sx={{ mb: 2 }}>
                                    {getStatusChip(course.status)}
                                    <Chip
                                        label={course.level}
                                        variant="outlined"
                                        size="small"
                                        sx={{ ml: 1 }}
                                    />
                                </Box>

                                {/* Price and Rating */}
                                <Box sx={{ mb: 2 }}>
                                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                        <Typography variant="h6" color="primary" sx={{ fontWeight: 'bold' }}>
                                            {formatPrice(course.price)}
                                        </Typography>
                                        {course.originalPrice > course.price && (
                                            <>
                                                <Typography
                                                    variant="body2"
                                                    sx={{
                                                        textDecoration: 'line-through',
                                                        color: 'text.secondary',
                                                        ml: 1
                                                    }}
                                                >
                                                    {formatPrice(course.originalPrice)}
                                                </Typography>
                                                <Chip
                                                    label={`-${course.discount}%`}
                                                    color="error"
                                                    size="small"
                                                    sx={{ ml: 1 }}
                                                />
                                            </>
                                        )}
                                    </Box>
                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                        <StarIcon sx={{ fontSize: 16, color: '#ffc107', mr: 0.5 }} />
                                        <Typography variant="body2" sx={{ mr: 1 }}>
                                            {course.rating}
                                        </Typography>
                                        <Typography variant="body2" color="textSecondary">
                                            ({course.totalReviews} đánh giá)
                                        </Typography>
                                    </Box>
                                </Box>

                                <Box sx={{ mb: 2 }}>
                                    <Typography variant="body2" sx={{ mb: 1 }}>
                                        <TimeIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                        {course.estimatedTime} • {course.duration}
                                    </Typography>
                                    <Typography variant="body2" sx={{ mb: 1 }}>
                                        <GroupIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                        {course.currentStudents} học viên đã đăng ký
                                    </Typography>
                                    <Typography variant="body2">
                                        <SchoolIcon sx={{ fontSize: 16, mr: 0.5 }} />
                                        {course.language} • {course.certificate ? 'Có chứng chỉ' : 'Không có chứng chỉ'}
                                    </Typography>
                                </Box>

                                <Box sx={{ mb: 2 }}>
                                    <Typography variant="body2" color="textSecondary" gutterBottom>
                                        Nội dung:
                                    </Typography>
                                    <Grid container spacing={1}>
                                        <Grid item xs={4}>
                                            <Typography variant="caption" display="block" align="center">
                                                Bài học
                                            </Typography>
                                            <Typography variant="body2" align="center" fontWeight="bold">
                                                {course.totalLessons}
                                            </Typography>
                                        </Grid>
                                        <Grid item xs={4}>
                                            <Typography variant="caption" display="block" align="center">
                                                Bài tập
                                            </Typography>
                                            <Typography variant="body2" align="center" fontWeight="bold">
                                                {course.totalAssignments}
                                            </Typography>
                                        </Grid>
                                        <Grid item xs={4}>
                                            <Typography variant="caption" display="block" align="center">
                                                Kiểm tra
                                            </Typography>
                                            <Typography variant="body2" align="center" fontWeight="bold">
                                                {course.totalQuizzes}
                                            </Typography>
                                        </Grid>
                                    </Grid>
                                </Box>

                                {/* Features */}
                                <Box sx={{ mb: 2 }}>
                                    <Typography variant="body2" color="textSecondary" gutterBottom>
                                        Tính năng nổi bật:
                                    </Typography>
                                    {course.features.slice(0, 3).map((feature, index) => (
                                        <Typography key={index} variant="caption" display="block" sx={{ mb: 0.5 }}>
                                            • {feature}
                                        </Typography>
                                    ))}
                                    {course.features.length > 3 && (
                                        <Typography variant="caption" color="textSecondary">
                                            +{course.features.length - 3} tính năng khác...
                                        </Typography>
                                    )}
                                </Box>
                            </CardContent>

                            <CardActions>
                                <Button
                                    size="small"
                                    onClick={() => handleViewCourse(course)}
                                >
                                    Chi tiết
                                </Button>

                                {course.status === 'available' && (
                                    <Button
                                        size="small"
                                        variant="contained"
                                        startIcon={<PaymentIcon />}
                                        onClick={() => handleBuyNow(course.id)}
                                        sx={{ bgcolor: '#2e7d32', ml: 1 }}
                                    >
                                        {course.price === 0 ? 'Đăng ký miễn phí' : 'Mua ngay'}
                                    </Button>
                                )}

                                {course.status === 'purchased' && (
                                    <Button
                                        size="small"
                                        variant="contained"
                                        startIcon={<SchoolIcon />}
                                        sx={{ bgcolor: '#1976d2', ml: 1 }}
                                    >
                                        Bắt đầu học
                                    </Button>
                                )}
                            </CardActions>
                        </Card>
                    </Grid>
                ))}
            </Grid>

            {/* Course Detail Dialog */}
            <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
                <DialogTitle>
                    {selectedCourse?.name}
                </DialogTitle>
                <DialogContent>
                    {selectedCourse && (
                        <Box>
                            <Grid container spacing={3}>
                                <Grid size={{ xs: 12, md: 6 }}>
                                    <Typography variant="h6" gutterBottom>
                                        Thông tin khóa học
                                    </Typography>
                                    <Typography variant="body2" sx={{ mb: 1 }}>
                                        <strong>Giáo viên:</strong> {selectedCourse.teacher}
                                    </Typography>
                                    <Typography variant="body2" sx={{ mb: 1 }}>
                                        <strong>Môn học:</strong> {selectedCourse.subject}
                                    </Typography>
                                    <Typography variant="body2" sx={{ mb: 1 }}>
                                        <strong>Lớp:</strong> {selectedCourse.grade}
                                    </Typography>
                                    <Typography variant="body2" sx={{ mb: 1 }}>
                                        <strong>Thời gian:</strong> {selectedCourse.startDate} - {selectedCourse.endDate}
                                    </Typography>
                                    <Typography variant="body2" sx={{ mb: 1 }}>
                                        <strong>Lịch học:</strong> {selectedCourse.schedule}
                                    </Typography>
                                    <Typography variant="body2" sx={{ mb: 2 }}>
                                        <strong>Số lượng:</strong> {selectedCourse.currentStudents}/{selectedCourse.maxStudents} học sinh
                                    </Typography>
                                    <Typography variant="body2">
                                        {selectedCourse.description}
                                    </Typography>
                                </Grid>
                                <Grid size={{ xs: 12, md: 6 }}>
                                    <Typography variant="h6" gutterBottom>
                                        Yêu cầu tham gia
                                    </Typography>
                                    <List dense>
                                        {selectedCourse.requirements.map((requirement, index) => (
                                            <ListItem key={index}>
                                                <ListItemIcon>
                                                    <CheckCircleIcon color="success" />
                                                </ListItemIcon>
                                                <ListItemText primary={requirement} />
                                            </ListItem>
                                        ))}
                                    </List>
                                </Grid>
                                <Grid size={{ xs: 12 }}>
                                    <Typography variant="h6" gutterBottom>
                                        Chương trình học
                                    </Typography>
                                    <List>
                                        {selectedCourse.syllabus.map((chapter, index) => (
                                            <React.Fragment key={index}>
                                                <ListItem>
                                                    <ListItemIcon>
                                                        <LessonIcon />
                                                    </ListItemIcon>
                                                    <ListItemText primary={chapter} />
                                                </ListItem>
                                                {index < selectedCourse.syllabus.length - 1 && <Divider />}
                                            </React.Fragment>
                                        ))}
                                    </List>
                                </Grid>
                            </Grid>
                        </Box>
                    )}
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseDialog}>Đóng</Button>
                    {selectedCourse && selectedCourse.status === 'available' && (
                        <Button
                            variant="contained"
                            startIcon={<PaymentIcon />}
                            onClick={() => handleBuyNow(selectedCourse.id)}
                            sx={{ bgcolor: '#2e7d32' }}
                        >
                            {selectedCourse.price === 0 ? 'Đăng ký miễn phí' : 'Mua ngay'}
                        </Button>
                    )}
                </DialogActions>
            </Dialog>

            {/* Payment Dialog */}
            <Dialog open={openPaymentDialog} onClose={() => setOpenPaymentDialog(false)} maxWidth="sm" fullWidth>
                <DialogTitle>
                    {selectedCourse?.price === 0 ? 'Đăng ký khóa học miễn phí' : 'Thanh toán khóa học'}
                </DialogTitle>
                <DialogContent>
                    {selectedCourse && (
                        <Box>
                            <Card sx={{ mb: 3, borderRadius: '10px' }}>
                                <CardContent>
                                    <Typography variant="h6" gutterBottom>
                                        {selectedCourse.name}
                                    </Typography>
                                    <Typography variant="body2" color="textSecondary" gutterBottom>
                                        {selectedCourse.teacher}
                                    </Typography>

                                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 2 }}>
                                        <Typography variant="h5" color="primary">
                                            {formatPrice(selectedCourse.price)}
                                        </Typography>
                                        {selectedCourse.originalPrice > selectedCourse.price && (
                                            <Box>
                                                <Typography
                                                    variant="body2"
                                                    sx={{ textDecoration: 'line-through', color: 'text.secondary' }}
                                                >
                                                    {formatPrice(selectedCourse.originalPrice)}
                                                </Typography>
                                                <Chip
                                                    label={`Tiết kiệm ${formatPrice(selectedCourse.originalPrice - selectedCourse.price)}`}
                                                    color="success"
                                                    size="small"
                                                />
                                            </Box>
                                        )}
                                    </Box>
                                </CardContent>
                            </Card>

                            {selectedCourse?.price > 0 && (
                                <>
                                    <Typography variant="h6" gutterBottom>
                                        Phương thức thanh toán
                                    </Typography>

                                    <FormControl fullWidth sx={{ mb: 3 }}>
                                        <Select
                                            value={paymentMethod}
                                            onChange={(e) => setPaymentMethod(e.target.value)}
                                        >
                                            <MenuItem value="credit_card">
                                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                                    <CreditCardIcon sx={{ mr: 1 }} />
                                                    Thẻ tín dụng/ghi nợ
                                                </Box>
                                            </MenuItem>
                                            <MenuItem value="e_wallet">
                                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                                    <WalletIcon sx={{ mr: 1 }} />
                                                    Ví điện tử (MoMo, ZaloPay)
                                                </Box>
                                            </MenuItem>
                                            <MenuItem value="bank_transfer">
                                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                                    <PaymentIcon sx={{ mr: 1 }} />
                                                    Chuyển khoản ngân hàng
                                                </Box>
                                            </MenuItem>
                                        </Select>
                                    </FormControl>
                                </>
                            )}

                            <Alert severity="info" sx={{ borderRadius: '10px' }}>
                                {selectedCourse?.price === 0
                                    ? 'Khóa học này hoàn toàn miễn phí. Bạn sẽ có quyền truy cập vĩnh viễn sau khi đăng ký.'
                                    : 'Sau khi thanh toán thành công, bạn sẽ có quyền truy cập vĩnh viễn vào khóa học này.'
                                }
                            </Alert>
                        </Box>
                    )}
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setOpenPaymentDialog(false)}>
                        Hủy
                    </Button>
                    <Button
                        variant="contained"
                        onClick={handlePurchase}
                        startIcon={selectedCourse?.price === 0 ? <SchoolIcon /> : <PaymentIcon />}
                        sx={{ bgcolor: '#2e7d32' }}
                    >
                        {selectedCourse?.price === 0
                            ? 'Đăng ký miễn phí'
                            : `Thanh toán ${formatPrice(selectedCourse?.price)}`
                        }
                    </Button>
                </DialogActions>
            </Dialog>

            {/* Snackbar */}
            <Snackbar
                open={snackbar.open}
                autoHideDuration={6000}
                onClose={() => setSnackbar({ ...snackbar, open: false })}
            >
                <Alert
                    onClose={() => setSnackbar({ ...snackbar, open: false })}
                    severity={snackbar.severity}
                    sx={{ width: '100%' }}
                >
                    {snackbar.message}
                </Alert>
            </Snackbar>
        </Box>
    );
}

export default CourseEnrollment;
