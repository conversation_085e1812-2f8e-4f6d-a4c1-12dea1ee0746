import { useState, useEffect, useCallback } from 'react';
import { teacherAPI, studentAPI, apiUtils, API_STATUS, ERROR_MESSAGES } from '../services';

/**
 * Custom hook for API calls with loading, error handling, and caching
 * @param {Function} apiFunction - API function to call
 * @param {Array} dependencies - Dependencies for useEffect
 * @param {Object} options - Options for the hook
 * @returns {Object} API state and functions
 */
export const useAPI = (apiFunction, dependencies = [], options = {}) => {
    const {
        immediate = true,
        onSuccess,
        onError,
        cacheKey,
        cacheTTL = 5 * 60 * 1000, // 5 minutes default
    } = options;

    const [data, setData] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [status, setStatus] = useState(API_STATUS.IDLE);

    // Cache management
    const getCachedData = useCallback((key) => {
        if (!key) return null;
        try {
            const cached = localStorage.getItem(`api_cache_${key}`);
            if (cached) {
                const { data, timestamp } = JSON.parse(cached);
                if (Date.now() - timestamp < cacheTTL) {
                    return data;
                }
                localStorage.removeItem(`api_cache_${key}`);
            }
        } catch (error) {
            console.warn('Cache read error:', error);
        }
        return null;
    }, [cacheTTL]);

    const setCachedData = useCallback((key, data) => {
        if (!key) return;
        try {
            localStorage.setItem(`api_cache_${key}`, JSON.stringify({
                data,
                timestamp: Date.now()
            }));
        } catch (error) {
            console.warn('Cache write error:', error);
        }
    }, []);

    // Execute API call
    const execute = useCallback(async (...args) => {
        try {
            setLoading(true);
            setError(null);
            setStatus(API_STATUS.LOADING);

            // Check cache first
            if (cacheKey) {
                const cachedData = getCachedData(cacheKey);
                if (cachedData) {
                    setData(cachedData);
                    setStatus(API_STATUS.SUCCESS);
                    setLoading(false);
                    return cachedData;
                }
            }

            const result = await apiFunction(...args);
            
            setData(result);
            setStatus(API_STATUS.SUCCESS);
            
            // Cache the result
            if (cacheKey) {
                setCachedData(cacheKey, result);
            }
            
            if (onSuccess) {
                onSuccess(result);
            }
            
            return result;
        } catch (err) {
            const errorMessage = apiUtils.formatErrorMessage(err);
            setError(errorMessage);
            setStatus(API_STATUS.ERROR);
            
            if (onError) {
                onError(err);
            }
            
            throw err;
        } finally {
            setLoading(false);
        }
    }, [apiFunction, cacheKey, getCachedData, setCachedData, onSuccess, onError]);

    // Reset state
    const reset = useCallback(() => {
        setData(null);
        setError(null);
        setLoading(false);
        setStatus(API_STATUS.IDLE);
    }, []);

    // Auto-execute on mount if immediate is true
    useEffect(() => {
        if (immediate && apiFunction) {
            execute();
        }
    }, [immediate, ...dependencies]);

    return {
        data,
        loading,
        error,
        status,
        execute,
        reset,
        isLoading: loading,
        isError: status === API_STATUS.ERROR,
        isSuccess: status === API_STATUS.SUCCESS,
        isIdle: status === API_STATUS.IDLE,
    };
};

/**
 * Hook for teacher API calls
 */
export const useTeacherAPI = () => {
    // Course management
    const useCourses = (options = {}) => useAPI(teacherAPI.getCourses, [], options);
    
    const useCourseById = (courseId, options = {}) => 
        useAPI(() => teacherAPI.getCourseById(courseId), [courseId], {
            immediate: !!courseId,
            cacheKey: `teacher_course_${courseId}`,
            ...options
        });

    const useCreateCourse = (options = {}) => 
        useAPI(teacherAPI.createCourse, [], { immediate: false, ...options });

    const useUpdateCourse = (options = {}) => 
        useAPI(teacherAPI.updateCourse, [], { immediate: false, ...options });

    const useDeleteCourse = (options = {}) => 
        useAPI(teacherAPI.deleteCourse, [], { immediate: false, ...options });

    // Course content management
    const useCourseContent = (courseId, options = {}) => 
        useAPI(() => teacherAPI.getCourseContent(courseId), [courseId], {
            immediate: !!courseId,
            cacheKey: `teacher_course_content_${courseId}`,
            ...options
        });

    const useCreateCourseContent = (options = {}) => 
        useAPI(teacherAPI.createCourseContent, [], { immediate: false, ...options });

    // Student management
    const useCourseStudents = (courseId, options = {}) => 
        useAPI(() => teacherAPI.getCourseStudents(courseId), [courseId], {
            immediate: !!courseId,
            ...options
        });

    const useStudentProgress = (courseId, studentId, options = {}) => 
        useAPI(() => teacherAPI.getStudentProgress(courseId, studentId), [courseId, studentId], {
            immediate: !!(courseId && studentId),
            ...options
        });

    // Analytics
    const useDashboardStats = (options = {}) => 
        useAPI(teacherAPI.getDashboardStats, [], {
            cacheKey: 'teacher_dashboard_stats',
            ...options
        });

    const useCourseAnalytics = (courseId, options = {}) => 
        useAPI(() => teacherAPI.getCourseAnalytics(courseId), [courseId], {
            immediate: !!courseId,
            cacheKey: `teacher_course_analytics_${courseId}`,
            ...options
        });

    return {
        // Course management
        useCourses,
        useCourseById,
        useCreateCourse,
        useUpdateCourse,
        useDeleteCourse,
        
        // Course content
        useCourseContent,
        useCreateCourseContent,
        
        // Student management
        useCourseStudents,
        useStudentProgress,
        
        // Analytics
        useDashboardStats,
        useCourseAnalytics,
    };
};

/**
 * Hook for student API calls
 */
export const useStudentAPI = () => {
    // Course discovery & enrollment
    const useAvailableCourses = (filters = {}, options = {}) => 
        useAPI(() => studentAPI.getAvailableCourses(filters), [JSON.stringify(filters)], {
            cacheKey: `student_available_courses_${JSON.stringify(filters)}`,
            ...options
        });

    const useEnrolledCourses = (options = {}) => 
        useAPI(studentAPI.getEnrolledCourses, [], {
            cacheKey: 'student_enrolled_courses',
            ...options
        });

    const useCourseDetails = (courseId, options = {}) => 
        useAPI(() => studentAPI.getCourseDetails(courseId), [courseId], {
            immediate: !!courseId,
            cacheKey: `student_course_${courseId}`,
            ...options
        });

    const usePurchaseCourse = (options = {}) => 
        useAPI(studentAPI.purchaseCourse, [], { immediate: false, ...options });

    // Learning progress
    const useCourseContent = (courseId, options = {}) => 
        useAPI(() => studentAPI.getCourseContent(courseId), [courseId], {
            immediate: !!courseId,
            cacheKey: `student_course_content_${courseId}`,
            ...options
        });

    const useCourseProgress = (courseId, options = {}) => 
        useAPI(() => studentAPI.getCourseProgress(courseId), [courseId], {
            immediate: !!courseId,
            cacheKey: `student_course_progress_${courseId}`,
            cacheTTL: 1 * 60 * 1000, // 1 minute for progress data
            ...options
        });

    const useMarkLessonComplete = (options = {}) => 
        useAPI(studentAPI.markLessonComplete, [], { immediate: false, ...options });

    // Quiz management
    const useQuizDetails = (courseId, quizId, options = {}) => 
        useAPI(() => studentAPI.getQuizDetails(courseId, quizId), [courseId, quizId], {
            immediate: !!(courseId && quizId),
            ...options
        });

    const useStartQuizAttempt = (options = {}) => 
        useAPI(studentAPI.startQuizAttempt, [], { immediate: false, ...options });

    const useSubmitQuizAnswers = (options = {}) => 
        useAPI(studentAPI.submitQuizAnswers, [], { immediate: false, ...options });

    // TurboWarp assignments
    const useTurboWarpAssignment = (courseId, assignmentId, options = {}) => 
        useAPI(() => studentAPI.getTurboWarpAssignment(courseId, assignmentId), [courseId, assignmentId], {
            immediate: !!(courseId && assignmentId),
            ...options
        });

    const useSubmitTurboWarpProject = (options = {}) => 
        useAPI(studentAPI.submitTurboWarpProject, [], { immediate: false, ...options });

    // Dashboard & analytics
    const useDashboardData = (options = {}) => 
        useAPI(studentAPI.getDashboardData, [], {
            cacheKey: 'student_dashboard_data',
            ...options
        });

    const useLearningStats = (options = {}) => 
        useAPI(studentAPI.getLearningStats, [], {
            cacheKey: 'student_learning_stats',
            ...options
        });

    // Payment
    const usePaymentHistory = (options = {}) => 
        useAPI(studentAPI.getPaymentHistory, [], {
            cacheKey: 'student_payment_history',
            ...options
        });

    const useProcessPayment = (options = {}) => 
        useAPI(studentAPI.processPayment, [], { immediate: false, ...options });

    // Profile
    const useProfile = (options = {}) => 
        useAPI(studentAPI.getProfile, [], {
            cacheKey: 'student_profile',
            ...options
        });

    const useUpdateProfile = (options = {}) => 
        useAPI(studentAPI.updateProfile, [], { immediate: false, ...options });

    return {
        // Course discovery & enrollment
        useAvailableCourses,
        useEnrolledCourses,
        useCourseDetails,
        usePurchaseCourse,
        
        // Learning progress
        useCourseContent,
        useCourseProgress,
        useMarkLessonComplete,
        
        // Quiz management
        useQuizDetails,
        useStartQuizAttempt,
        useSubmitQuizAnswers,
        
        // TurboWarp assignments
        useTurboWarpAssignment,
        useSubmitTurboWarpProject,
        
        // Dashboard & analytics
        useDashboardData,
        useLearningStats,
        
        // Payment
        usePaymentHistory,
        useProcessPayment,
        
        // Profile
        useProfile,
        useUpdateProfile,
    };
};

/**
 * Hook for common API operations
 */
export const useCommonAPI = () => {
    const [uploadProgress, setUploadProgress] = useState(0);

    const uploadFile = useCallback(async (file, path = '') => {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('path', path);

        try {
            setUploadProgress(0);
            const response = await axiosInstance.post('/api/common/upload/', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
                onUploadProgress: (progressEvent) => {
                    const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                    setUploadProgress(progress);
                },
            });
            return response.data;
        } catch (error) {
            throw error;
        } finally {
            setUploadProgress(0);
        }
    }, []);

    return {
        uploadFile,
        uploadProgress,
    };
};

export default useAPI;
