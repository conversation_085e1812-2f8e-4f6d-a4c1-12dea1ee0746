from django.db import models

# Create your models here.


class Student(models.Model):
    name = models.Char<PERSON><PERSON>(max_length=100)
    birthday = models.DateField()
    parent_name = models.CharField(max_length=100)
    parent_phone = models.CharField(max_length=20)
    email = models.EmailField(blank=True, null=True)
    note = models.TextField(blank=True, default='')

    def __str__(self):
        return self.name


class Teacher(models.Model):
    name = models.CharField(max_length=100)
    phone = models.CharField(max_length=20)
    email = models.EmailField()
    major = models.CharField(max_length=100, default='', null=True, blank=True)
    about = models.TextField(blank=True, default='')

    def __str__(self):
        return self.name


class Class(models.Model):
    STATUS_CHOICES = [
        ('Chuẩn bị mở', 'Chuẩn bị mở'),
        ('<PERSON>ang học', '<PERSON>ang học'),
        ('<PERSON><PERSON><PERSON> thành', '<PERSON><PERSON><PERSON> thành'),
        ('<PERSON><PERSON> hủy', 'Đã hủy')
    ]
    name = models.CharField(max_length=100)
    start_date = models.DateField()
    number_of_lessons = models.IntegerField(default=0)
    # ví dụ: [{'day': 'T2', 'start': '17:30', 'end': '19:00'}]
    schedule = models.JSONField()
    room = models.CharField(max_length=50)
    fee = models.IntegerField(default=0)
    discount = models.IntegerField(default=0)
    teacher = models.ForeignKey(
        'Teacher', on_delete=models.SET_NULL, null=True)
    completed = models.BooleanField(default=False)
    status = models.TextField(choices=STATUS_CHOICES, default="Chuẩn bị mở")

    def __str__(self):
        return f"{self.name} - {self.teacher}"


class ClassStudent(models.Model):
    student = models.ForeignKey(Student, on_delete=models.CASCADE)
    classroom = models.ForeignKey(Class, on_delete=models.CASCADE)
    fee = models.IntegerField(default=0)
    discount = models.IntegerField(default=0)
    paid = models.BooleanField(default=False)
    note = models.TextField(blank=True, default='')
    joined_date = models.DateField(auto_now_add=True)
    certificate_no = models.TextField(default="", null=True, blank=True)

    def __str__(self):
        return f"{self.student} - {self.classroom}"


class Attendance(models.Model):
    student = models.ForeignKey(Student, on_delete=models.CASCADE)
    class_ref = models.ForeignKey(Class, on_delete=models.CASCADE)
    date = models.DateField()
    status = models.CharField(max_length=10, choices=[(
        'present', 'Có mặt'), ('absent', 'Vắng'), ('excused', 'Có phép')])

    def __str__(self):
        return f"{self.student} - {self.class_ref} - {self.date}"


class Invoice(models.Model):
    student = models.ForeignKey(Student, on_delete=models.CASCADE)
    amount = models.IntegerField()
    discount = models.IntegerField(default=0)
    issued_date = models.DateField(auto_now_add=True)
    status = models.CharField(max_length=20, choices=[(
        'paid', 'Đã thanh toán'), ('unpaid', 'Chưa')])

    def __str__(self):
        return f"{self.student} - {self.amount}"


class Lesson(models.Model):
    class_ref = models.ForeignKey(Class, on_delete=models.CASCADE)
    lesson_no = models.IntegerField(default=0)
    title = models.CharField(max_length=255)
    date = models.DateField()
    content = models.TextField()
    result_summary = models.TextField(blank=True)

    def __str__(self):
        return f"{self.class_ref} - {self.title}"
