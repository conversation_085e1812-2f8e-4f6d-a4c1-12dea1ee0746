# BeE IDE Bluetooth Adapter

## Tổng quan

Bluetooth Adapter cho BeE IDE cung cấp một interface thống nhất để kết nối với thiết bị BeE qua Bluetooth từ cả trình duyệt web (sử dụng Web Bluetooth API) và thiết bị iOS (sử dụng CoreBluetooth thông qua Flutter WebView).

## Tính năng

-   ✅ **Đa nền tảng**: Hỗ trợ cả Web Browser và iOS
-   ✅ **Tự động phát hiện**: Tự động chọn adapter phù hợp với nền tảng
-   ✅ **Kết nối thông minh**: Tự động kết nối lại khi bị ngắt kết nối
-   ✅ **Upload code**: Hỗ trợ upload code và modules qua Bluetooth
-   ✅ **Error handling**: Xử lý lỗi chi tiết và thông báo rõ ràng

## Cấu trúc

```
BeeIDE.jsx
├── BluetoothAdapter (Abstract class)
├── WebBluetoothAdapter (Web implementation)
└── iOSBluetoothAdapter (iOS implementation)
```

## Cách sử dụng

### 1. Web Browser

Không cần cài đặt thêm gì. Adapter sẽ tự động sử dụng Web Bluetooth API.

**Yêu cầu:**

-   Chrome/Edge browser
-   HTTPS connection
-   Bluetooth enabled

### 2. iOS Flutter App

#### Bước 1: Thêm dependencies vào `pubspec.yaml`

```yaml
dependencies:
    flutter:
        sdk: flutter
    webview_flutter: ^4.4.2
    flutter_reactive_ble: ^5.0.3

dev_dependencies:
    flutter_test:
        sdk: flutter
```

#### Bước 2: Cấu hình iOS permissions trong `ios/Runner/Info.plist`

```xml
<key>NSBluetoothAlwaysUsageDescription</key>
<string>Ứng dụng này cần quyền truy cập Bluetooth để kết nối với thiết bị BeE</string>
<key>NSBluetoothPeripheralUsageDescription</key>
<string>Ứng dụng này cần quyền truy cập Bluetooth để kết nối với thiết bị BeE</string>
```

#### Bước 3: Implement Flutter WebView

Sử dụng code trong file `webview_flutter_example.dart` làm template.

**Lưu ý quan trọng về UUID:**

-   Service UUID: `6e400001-b5a3-f393-e0a9-e50e24dcca9e`
-   TX UUID (từ device): `6e400003-b5a3-f393-e0a9-e50e24dcca9e` (dùng cho notify)
-   RX UUID (tới device): `6e400002-b5a3-f393-e0a9-e50e24dcca9e` (dùng cho write)

## API Reference

### BluetoothAdapter Methods

#### `connect(): Promise<void>`

Kết nối với thiết bị BeE.

-   **Web**: Hiển thị popup chọn thiết bị
-   **iOS**: Tự động scan và kết nối

#### `disconnect(): Promise<void>`

Ngắt kết nối với thiết bị hiện tại.

#### `writeData(data: Uint8Array): Promise<void>`

Ghi dữ liệu thô lên thiết bị.

#### `writeASCII(text: string): Promise<void>`

Ghi text ASCII lên thiết bị.

#### `writeBuf20B(buffer: Uint8Array): Promise<void>`

Ghi dữ liệu theo chunks 20 bytes với delay nhỏ.

### Events

#### `setOnDisconnected(callback: Function)`

Đăng ký callback khi thiết bị ngắt kết nối.

#### `setOnMessage(callback: Function)`

Đăng ký callback khi nhận dữ liệu từ thiết bị.

## Cách hoạt động

### 1. Platform Detection

```javascript
const isFlutterApp =
    window.flutter_inappwebview !== undefined ||
    window.webkit?.messageHandlers?.flutterChannel !== undefined ||
    navigator.userAgent.includes("Flutter");
```

### 2. Adapter Selection

```javascript
if (isFlutterApp) {
    bluetoothAdapter.current = new iOSBluetoothAdapter();
} else {
    bluetoothAdapter.current = new WebBluetoothAdapter();
}
```

### 3. Communication Flow

#### Web Browser:

```
BeeIDE → WebBluetoothAdapter → Web Bluetooth API → BeE Device
```

#### iOS:

```
BeeIDE → iOSBluetoothAdapter → Flutter WebView → CoreBluetooth → BeE Device
```

## Xử lý lỗi

Adapter xử lý các lỗi phổ biến:

-   **NotFoundError**: Không tìm thấy thiết bị BeE
-   **SecurityError**: Không có quyền truy cập Bluetooth
-   **NetworkError**: Lỗi kết nối mạng
-   **NotAllowedError**: Người dùng từ chối kết nối
-   **TimeoutError**: Timeout khi kết nối

## Testing

### Web Browser

1. Mở BeE IDE trong Chrome/Edge
2. Bật Bluetooth trên máy tính
3. Bật thiết bị BeE
4. Click nút Connect Bluetooth
5. Chọn thiết bị BeE trong popup

### iOS Simulator

iOS Simulator không hỗ trợ Bluetooth. Cần test trên thiết bị thật.

### iOS Device

1. Build và install Flutter app
2. Bật Bluetooth trên iPhone/iPad
3. Bật thiết bị BeE
4. Mở app và click Connect Bluetooth
5. App sẽ tự động scan và kết nối

## Troubleshooting

### Web Browser Issues

**Lỗi: "Web Bluetooth is not supported"**

-   Đảm bảo sử dụng Chrome/Edge
-   Đảm bảo website chạy trên HTTPS
-   Kiểm tra Bluetooth có được bật không

**Lỗi: "No BeE device found"**

-   Đảm bảo thiết bị BeE đã bật
-   Đảm bảo thiết bị ở chế độ pairing
-   Thử reset thiết bị BeE

### iOS Issues

**Lỗi: "Bluetooth not available"**

-   Kiểm tra permissions trong Info.plist
-   Đảm bảo Bluetooth được bật trên iOS device
-   Restart app nếu cần

**Lỗi: "Connection timeout"**

-   Đảm bảo thiết bị BeE gần iOS device
-   Thử reset Bluetooth trên iOS device
-   Kiểm tra thiết bị BeE có đang kết nối với device khác không

## Phát triển thêm

### Thêm platform mới

1. Tạo class extends `BluetoothAdapter`
2. Implement các method required
3. Thêm logic detect platform
4. Update factory method

### Thêm tính năng mới

1. Thêm method vào `BluetoothAdapter` base class
2. Implement trong tất cả concrete classes
3. Update TypeScript definitions
4. Update documentation

## Changelog

### v1.0.0

-   Initial release
-   Web Bluetooth support
-   iOS CoreBluetooth support
-   Auto platform detection
-   Error handling
-   Reconnection logic
