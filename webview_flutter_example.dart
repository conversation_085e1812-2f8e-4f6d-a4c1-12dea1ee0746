// Flutter integration example for BeE IDE iOS app using webview_flutter
// This is compatible with your existing main.dart code structure

import 'dart:async';
import 'dart:typed_data';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:flutter_reactive_ble/flutter_reactive_ble.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();
  runApp(const BeeApp());
}

class BeeApp extends StatelessWidget {
  const BeeApp({super.key});
  @override
  Widget build(BuildContext context) {
    return const MaterialApp(
      debugShowCheckedModeBanner: false,
      home: BeeHome(),
    );
  }
}

class BeeHome extends StatefulWidget {
  const BeeHome({super.key});
  @override
  State<BeeHome> createState() => _BeeHomeState();
}

class _BeeHomeState extends State<BeeHome> {
  late final WebViewController _wv;
  final _ble = FlutterReactiveBle();

  // BLE state
  StreamSubscription<DiscoveredDevice>? _scanSub;
  StreamSubscription<ConnectionStateUpdate>? _connSub;
  StreamSubscription<List<int>>? _notifySub;
  DiscoveredDevice? _device;
  QualifiedCharacteristic? _rxChar; // notify
  QualifiedCharacteristic? _txChar; // write

  // BeE Board UUIDs
  final Uuid _serviceUuid = Uuid.parse("6e400001-b5a3-f393-e0a9-e50e24dcca9e");
  final Uuid _rxUuid = Uuid.parse("6e400003-b5a3-f393-e0a9-e50e24dcca9e"); // TX from device perspective
  final Uuid _txUuid = Uuid.parse("6e400002-b5a3-f393-e0a9-e50e24dcca9e"); // RX from device perspective

  @override
  void initState() {
    super.initState();

    _wv = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..addJavaScriptChannel(
        'Bee',
        onMessageReceived: (JavaScriptMessage msg) =>
            _handleJsMessage(msg.message),
      )
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageFinished: (url) async {
            // Inject adapter để web dễ gọi/nhận message
            await _injectJsAdapter();
          },
        ),
      )
      // Load your BeE IDE URL here
      ..loadRequest(
        Uri.parse("http://************:4000/play/bee-ide"), // Update with your URL
      );
  }

  Future<void> _injectJsAdapter() async {
    const js = r'''
      // Tạo API gọn cho web:
      window.BeeNative = {
        send: (obj) => {
          try { 
            Bee.postMessage(JSON.stringify(obj)); 
          }
          catch(e){ 
            console.warn('Bee channel not available', e); 
          }
        },
        onMessage: (handler) => { 
          window.__bee_ble_onMessage = handler; 
        }
      };
      
      // Thông báo sẵn sàng
      if (window.__bee_ble_onMessage) {
        window.__bee_ble_onMessage({type:'ready'});
      }
    ''';
    await _wv.runJavaScript(js);
  }

  // ===== Bridge JS -> Flutter =====
  Future<void> _handleJsMessage(String raw) async {
    Map<String, dynamic> m;
    try {
      m = jsonDecode(raw);
    } catch (_) {
      return;
    }
    final type = (m['type'] ?? '').toString();

    try {
      switch (type) {
        case 'ble.scan':
          await _startScan(
            service: (m['service'] as String?) ?? _serviceUuid.toString(),
            nameFilter: m['name'] as String?,
            timeoutMs: (m['timeoutMs'] as int?) ?? 8000,
          );
          break;
        case 'ble.connect':
          await _connectTo((m['id'] as String));
          break;
        case 'ble.write':
          final String b64 = m['base64'] as String; // dữ liệu dạng base64
          final data = base64Decode(b64);
          await _writeChunked(data);
          break;
        case 'ble.subscribe':
          await _subscribeNotify();
          break;
        case 'ble.disconnect':
          await _disconnect();
          break;
      }
    } catch (e) {
      _postToWeb({'type': 'error', 'message': e.toString()});
    }
  }

  // ====== BLE impl ======
  Future<void> _startScan({
    required String service,
    String? nameFilter,
    int timeoutMs = 8000,
  }) async {
    await _scanSub?.cancel();
    final serviceId = Uuid.parse(service);
    final stream = _ble.scanForDevices(
      withServices: [serviceId],
      scanMode: ScanMode.balanced,
    );
    final found = <Map<String, dynamic>>[];
    _scanSub = stream.listen(
      (d) {
        if (nameFilter == null ||
            d.name.toLowerCase().contains(nameFilter.toLowerCase())) {
          found.add({'id': d.id, 'name': d.name, 'rssi': d.rssi});
        }
      },
      onError: (e) {
        _postToWeb({'type': 'ble.scan.error', 'message': e.toString()});
      },
    );

    await Future<void>.delayed(Duration(milliseconds: timeoutMs));
    await _scanSub?.cancel();
    _postToWeb({'type': 'ble.scan.done', 'devices': found});
  }

  Future<void> _connectTo(String id) async {
    await _connSub?.cancel();
    _connSub = _ble
        .connectToDevice(
          id: id,
          servicesWithCharacteristicsToDiscover: {
            _serviceUuid: [_rxUuid, _txUuid],
          },
          connectionTimeout: const Duration(seconds: 15),
        )
        .listen(
          (evt) {
            if (evt.connectionState == DeviceConnectionState.connected) {
              _device = DiscoveredDevice(
                id: id,
                name: 'BeeBoard',
                serviceUuids: const <Uuid>[],
                serviceData: const <Uuid, Uint8List>{},
                manufacturerData: Uint8List(0),
                rssi: 0,
              );
              _rxChar = QualifiedCharacteristic(
                deviceId: id,
                serviceId: _serviceUuid,
                characteristicId: _rxUuid,
              );
              _txChar = QualifiedCharacteristic(
                deviceId: id,
                serviceId: _serviceUuid,
                characteristicId: _txUuid,
              );
              _postToWeb({'type': 'ble.connect.ok', 'id': id});
            } else if (evt.connectionState ==
                DeviceConnectionState.disconnected) {
              _postToWeb({'type': 'ble.disconnected', 'id': id});
            }
          },
          onError: (e) {
            _postToWeb({'type': 'ble.connect.error', 'message': e.toString()});
          },
        );
  }

  Future<void> _subscribeNotify() async {
    if (_rxChar == null) return;
    await _notifySub?.cancel();
    _notifySub = _ble
        .subscribeToCharacteristic(_rxChar!)
        .listen(
          (data) {
            _postToWeb({'type': 'ble.notify', 'base64': base64Encode(data)});
          },
          onError: (e) {
            _postToWeb({'type': 'ble.notify.error', 'message': e.toString()});
          },
        );
  }

  Future<void> _writeChunked(
    List<int> bytes, {
    int chunk = 180,
    int delayMs = 2,
  }) async {
    if (_txChar == null) throw 'Not connected';
    for (var i = 0; i < bytes.length; i += chunk) {
      final end = (i + chunk < bytes.length) ? i + chunk : bytes.length;
      final part = bytes.sublist(i, end);
      await _ble.writeCharacteristicWithResponse(_txChar!, value: part);
      await Future<void>.delayed(Duration(milliseconds: delayMs));
    }
    _postToWeb({'type': 'ble.write.ok'});
  }

  Future<void> _disconnect() async {
    await _notifySub?.cancel();
    await _connSub?.cancel();
    _postToWeb({'type': 'ble.disconnect.ok'});
  }

  // ===== Flutter -> Web =====
  Future<void> _postToWeb(Map<String, dynamic> msg) async {
    final js = "if(window.__bee_ble_onMessage) window.__bee_ble_onMessage(${jsonEncode(msg)});";
    await _wv.runJavaScript(js);
  }

  @override
  void dispose() {
    _notifySub?.cancel();
    _connSub?.cancel();
    _scanSub?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('BeE IDE'),
        backgroundColor: Colors.orange,
      ),
      body: WebViewWidget(controller: _wv),
    );
  }
}
